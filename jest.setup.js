global.EMPTY_OBJECT = {};
global.EMPTY_ARRAY = [];
jest.mock('expo-haptics');
jest.mock('@expo/vector-icons', () => ({
  FontAwesome: 'FontAwesome',
  FontAwesome5: 'FontAwesome5',
  FontAwesome6: 'FontAwesome6',
  AntDesign: 'AntDesign',
  MaterialIcons: 'MaterialIcons',
  MaterialCommunityIcons: 'MaterialCommunityIcons',
}));
jest.mock('expo-router', () => ({
  useRouter: jest.fn(),
}));

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
  useNavigationState: jest.fn(),
}));

jest.mock('expo-modules-core', () => ({
  NativeModules: {
    ExpoHaptics: {
      impactAsync: jest.fn(),
      notificationAsync: jest.fn(),
      selectionAsync: jest.fn(),
      isAvailableAsync: jest.fn(),
    },
  },
  requireNativeModule: () => ({}),
  requireOptionalNativeModule: () => ({}),
}));
jest.mock('expo-font');
jest.mock('expo-constants', () => ({
  Constants: {
    platform: {
      ios: {
        model: 'iPhone',
      },
      android: {},
    },
    deviceName: 'deviceName',
  },
}));
jest.mock('expo-asset', () => ({
  Asset: {
    loadAsync: jest.fn(),
    fromModule: jest.fn(),
  },
}));
