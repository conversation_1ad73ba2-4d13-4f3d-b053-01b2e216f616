const fs = require('fs')
const path = require('path')

const quickFixes = [{
    filePath: 'react-native-copilot/dist/index.mjs',
    contentPath: 'copilotModal.mjs'
}]

const quickFix = ({filePath, contentPath}) => {
    const targetFile = path.resolve('node_modules', filePath)
    const updatedFile = path.resolve('__quickfix', contentPath)
    if (!fs.existsSync(targetFile)) {
        console.log(`File not found, no fix applied to path ${filePath}.`)
        return
    }
    if (!fs.existsSync(updatedFile)) {
        console.log(
            `Content File not found, no fix applied to path ${updatedFile}.`
        )
        return
    }

    const updatedContent = fs.readFileSync(updatedFile, 'utf8')
    fs.writeFileSync(targetFile, updatedContent, 'utf8')
    console.log(`Quick fix applied to ${filePath}`)
}

quickFixes.forEach((item) => {
    quickFix(item)
})
