import { Redirect, useLocalSearchParams } from "expo-router"
import _isNil from "lodash/isNil"
import GameInstructions from "../../../src/modules/game/pages/GameInstructions"

const Instructions = () => {
    const { gameType } = useLocalSearchParams()

    if (_isNil(gameType)) {
        return <Redirect href={"/home"} />
    }

    return <GameInstructions gameType={gameType} />
}

export default Instructions