import _isEmpty from "lodash/isEmpty"
import _isNil from "lodash/isNil"
import { Redirect, useLocalSearchParams } from "expo-router"
import LeagueLeaderboardPage from "modules/league/components/LeagueLeaderboard"
import useMediaQuery from "core/hooks/useMediaQuery"

const LeagueLeaderboard = () => {
    const { isMobile: isCompactMode } = useMediaQuery()
    const searchParams = useLocalSearchParams()
    const { id: leagueID } = searchParams ?? EMPTY_OBJECT

    if (_isEmpty(leagueID) || _isNil(leagueID)) {
        return <Redirect href={"/home"} />
    }

    if (!isCompactMode) {
        return <Redirect href={`/league/${leagueID}`} />
    }

    return <LeagueLeaderboardPage PAGE_SIZE={50} leagueDetails={{ id: leagueID }} key={"League-Leaderboard-Page"} />

}

export default LeagueLeaderboard