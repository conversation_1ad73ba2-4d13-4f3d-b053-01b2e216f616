import React, { useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import ProfilePage from 'modules/profile/pages/Profile';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { Redirect, useLocalSearchParams } from 'expo-router';
import { PAGE_NAMES } from 'core/constants/pageNames';
import _isEmpty from 'lodash/isEmpty';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';

const ProfileScreen = () => {
  const searchParams = useLocalSearchParams();

  const { username } = searchParams;
  const { user } = useSession();
  const currentUserUserName = userReader.username(user);

  useEffect(() => {
    webengage?.screen?.(PAGE_NAMES.PROFILE_PAGE);
    Analytics.track(ANALYTICS_EVENTS.PROFILE.VIEWED);
  }, []);

  if (_isEmpty(username) || username === '*') {
    return <Redirect href={`/profile/${currentUserUserName}`} />;
  }

  return (
    <View style={styles.container}>
      <ProfilePage username={username} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ProfileScreen;
