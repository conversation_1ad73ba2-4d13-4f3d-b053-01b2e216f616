import { registerRootComponent } from 'expo';
import { Platform } from 'react-native';
import messaging from '@react-native-firebase/messaging';
import WebEngage from 'react-native-webengage';
import { App } from 'expo-router/build/qualified-entry';

if(Platform.OS !== 'web'){
  messaging().setBackgroundMessageHandler(async (remoteMessage) => {
    const webengage = new WebEngage();
    webengage.push.onMessageReceived(remoteMessage);
  });
}

registerRootComponent(App);