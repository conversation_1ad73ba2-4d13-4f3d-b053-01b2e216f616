stages:
    - style
    - build
    - test

.debian-amd64-common:
    image: registry.videolan.org/dav1d-debian-unstable:20230213200113
    stage: build
    tags:
        - docker
        - amd64

.debian-amd64-minimum:
    image: registry.videolan.org/dav1d-debian-minimum:20230211045249
    stage: build
    tags:
        - docker
        - amd64

.debian-llvm-mingw-common:
    image: registry.videolan.org/vlc-debian-llvm-msvcrt:20230212072216
    stage: build
    tags:
        - docker
        - amd64

.debian-aarch64-common:
    image: registry.videolan.org/dav1d-debian-bullseye-aarch64:20230211050329
    stage: build
    tags:
        - docker
        - aarch64

.debian-armv7-common:
    image: registry.videolan.org/dav1d-debian-bullseye-armv7:20230211045904
    stage: build
    tags:
        - docker
        - armv7

.debian-ppc64le-common:
    image: registry.videolan.org/dav1d-debian-unstable-ppc64le:20230211050439
    stage: build
    tags:
        - docker
        - ppc64le

.android-common:
    image: registry.videolan.org/vlc-debian-android:20230212071537
    stage: build
    tags:
        - docker
        - amd64

.debian-wasm-emscripten-common:
    image: registry.videolan.org/vlc-debian-wasm-emscripten:20221213104631
    stage: build
    tags:
        - docker
        - amd64


style-check:
    extends: .debian-amd64-common
    stage: style
    script:
        - git grep -I -n -P "\t|\r| $" -- . ':(exclude)*/compat/*' && echo "Trailing whitespace" && exit 1
        - git grep -I -n -i -e 'david' --and --not -e 'copyright' -- . ':(exclude)THANKS.md' ':(exclude).gitlab-ci.yml' && echo "Misspelled dav1d" && exit 1
        - git grep -I -l -z "" -- . ':(exclude)*/compat/*' | while IFS= read -r -d '' i; do
              if [ -n "$(tail -c 1 "$i")" ]; then
                  echo "No newline at end of $i";
                  exit 1;
              fi;
          done
        - rg '[\u061c\u2000-\u200f\u2028-\u202f\u205f-\u206f]' ./ && echo "Invisible Unicode characters" && exit 1
        - git remote rm upstream 2> /dev/null || true
        - git remote add upstream https://code.videolan.org/videolan/dav1d.git
        - git fetch -q upstream master
        - for i in $(git rev-list HEAD ^upstream/master); do
              echo "Checking commit message of $i";
              msg="$(git log --format=%B -n 1 $i)";
              if [ -n "$(echo "$msg" | awk "NR==2")" ]; then
                  echo "Malformed commit message in $i, second line must be empty";
                  exit 1;
              fi;
              if echo "$msg" | head -1 | grep -q '\.$'; then
                  echo "Malformed commit message in $i, trailing period in subject line";
                  exit 1;
              fi;
          done

x86inc-check:
    extends: .debian-amd64-common
    stage: style
    script:
        - git remote rm x86inc 2> /dev/null || true
        - git remote add x86inc https://code.videolan.org/videolan/x86inc.asm.git
        - git fetch -q x86inc master
        - git diff --exit-code x86inc/master:x86inc.asm src/ext/x86/x86inc.asm
    allow_failure: true


build-debian:
    extends: .debian-amd64-common
    tags:
        - docker
        - avx2
        - amd64
    script:
        - meson setup build --buildtype release
                            -Dtrim_dsp=false
                            --werror
        - ninja -C build
        - cd build && meson test -v
    artifacts:
        paths:
            - build/
        expire_in: 1 day

build-debian-static:
    extends: .debian-amd64-common
    script:
        - meson setup build --buildtype release
                            --default-library static
                            --werror
        - ninja -C build
        - cd build && meson test -v
        - nm -A -g src/libdav1d.a | grep " [ABCDGRST] " | (! grep -v " _*dav1d_")

build-debian-illegal-instructions:
    extends: .debian-amd64-common
    tags:
        - docker
        - avx2
        - amd64
    script:
        - meson setup build --buildtype debug
        - ninja -C build
        - cd build
        - exit_code=0
        - time meson test -v --suite checkasm --wrapper 'qemu-x86_64 -cpu Conroe' || exit_code=$((exit_code + $?))
        - time meson test -v --suite checkasm --wrapper 'qemu-x86_64 -cpu Penryn' || exit_code=$((exit_code + $?))
        - if [ $exit_code -ne 0 ]; then exit $exit_code; fi

build-debian32:
    extends: .debian-amd64-common
    script:
        - meson setup build --buildtype release
                            --werror
                            --cross-file package/crossfiles/i686-linux32.meson
        - ninja -C build
        - cd build && meson test -v
    artifacts:
        paths:
            - build/
        expire_in: 1 day

build-debian-examples:
    extends: .debian-amd64-common
    script:
        - meson setup build --buildtype release
                            --werror
                            -Denable_examples=true
        - ninja -C build

build-debian-no-tools:
    extends: .debian-amd64-common
    script:
        - meson setup build --buildtype release
                            --werror
                            -Denable_tools=false
        - ninja -C build

build-debian-bitdepth:
    extends: .debian-amd64-common
    script:
        - meson setup build --buildtype release
                            --werror
                            -Dbitdepths=$DEPTH
        - ninja -C build
    parallel:
      matrix:
        - DEPTH: [8, 16]

build-debian-avx:
    extends: .debian-amd64-common
    tags:
        - docker
        - avx2
        - amd64
    variables:
        CFLAGS: '-mavx'
    script:
        - meson setup build --buildtype debug
                            --werror
        - ninja -C build
        - cd build
        - time meson test -v --suite checkasm

build-debian-minimum:
    extends: .debian-amd64-minimum
    script:
        - meson setup build --buildtype release
                            --werror
                            -Dtrim_dsp=false
        - ninja -C build
        - cd build && meson test -v

build-debian-avx512:
    extends: .debian-amd64-common
    tags:
        - docker
        - amd64-avx512
    variables:
        CFLAGS: '-mavx'
    script:
        - meson setup build --buildtype debug
                            --werror
        - ninja -C build
        - cd build
        - time meson test -v --suite checkasm

build-debian-clang14:
    extends: .debian-amd64-common
    variables:
        CC: clang
        CC_LD: mold
    script:
        - meson setup build --buildtype release
                            --werror
                            -Dtrim_dsp=false
        - ninja -C build
        - cd build && meson test -v

build-win:
    extends: .debian-amd64-common
    script:
        - wineserver -p && wine wineboot
        - meson setup build --buildtype release
                            --werror
                            --libdir lib
                            --prefix "$(pwd)/build/dav1d_install"
                            --cross-file package/crossfiles/${CROSSFILE}.meson
                            -Ddefault_library=both
                            -Dtrim_dsp=false
        - ninja -C build
        - ninja -C build install
        - cd build && meson test -v
        - ${CROSSFILE}-nm -A -g src/libdav1d.a | grep " [ABCDGRST] " | (! grep -E -v " \.| _*dav1d_")
    artifacts:
        name: "$CI_JOB_NAME-$CI_COMMIT_REF_SLUG"
        paths:
            - build/dav1d_install/
        expire_in: 1 week
    parallel:
      matrix:
        - CROSSFILE: [i686-w64-mingw32, x86_64-w64-mingw32]

build-win32-unaligned-stack:
    extends: .debian-llvm-mingw-common
    script:
        - wineserver -p && wine wineboot
        - meson setup build --buildtype release
                            --werror
                            --cross-file package/crossfiles/i686-w64-mingw32.meson
                            -Dstack_alignment=4
                            -Dtrim_dsp=false
        - ninja -C build
        - cd build && meson test -v

build-win-arm:
    extends: .debian-llvm-mingw-common
    script:
        - meson setup build --buildtype release
                            --werror
                            --libdir lib
                            --prefix "$(pwd)/build/dav1d_install"
                            --cross-file /opt/crossfiles/${CROSSFILE}.meson
                            -Ddefault_library=both
                            -Dtrim_dsp=false
        - ninja -C build
        - ninja -C build install
        - ${CROSSFILE}-nm -A -g build/src/libdav1d.a | grep " [ABCDGRST] " | (! grep -E -v " \.| _*dav1d_")
    artifacts:
        name: "$CI_JOB_NAME-$CI_COMMIT_REF_SLUG"
        paths:
            - build/dav1d_install/
        expire_in: 1 week
    parallel:
      matrix:
        - CROSSFILE: [armv7-w64-mingw32, aarch64-w64-mingw32]

.build-android-common:
    extends: .android-common
    script:
        - meson setup build --buildtype release
                            --werror
                            --libdir lib
                            --prefix "$(pwd)/build/dav1d_install"
                            --cross-file $CROSSFILE
                            -Ddefault_library=both
                            -Dtrim_dsp=false
        - ninja -C build
        - ninja -C build install

build-android-armv7:
    extends: .build-android-common
    variables:
        CROSSFILE: package/crossfiles/arm-android.meson
    rules:
        - if: '$CI_COMMIT_BRANCH'

build-android-aarch64:
    extends: .build-android-common
    variables:
        CROSSFILE: package/crossfiles/aarch64-android.meson
    rules:
        - if: '$CI_COMMIT_BRANCH'

build-android-armv7-release:
    extends: build-android-armv7
    rules:
        - if: '$CI_COMMIT_TAG && $CI_PROJECT_PATH == "videolan/dav1d"'
    artifacts:
        name: "$CI_JOB_NAME-$CI_COMMIT_REF_SLUG"
        paths:
            - build/dav1d_install/
        expire_in: 1 week

build-android-aarch64-release:
    extends: build-android-aarch64
    rules:
        - if: '$CI_COMMIT_TAG && $CI_PROJECT_PATH == "videolan/dav1d"'
    artifacts:
        name: "$CI_JOB_NAME-$CI_COMMIT_REF_SLUG"
        paths:
            - build/dav1d_install/
        expire_in: 1 week

build-debian-aarch64:
    extends: .debian-aarch64-common
    script:
        - meson setup build --buildtype debugoptimized
                            --werror
        - ninja -C build
        - cd build && meson test -v

build-debian-aarch64-clang-5:
    extends: .debian-aarch64-common
    variables:
        CC: clang-5.0
        CFLAGS: '-integrated-as'
    script:
        - meson setup build --buildtype release
        - ninja -C build
        - cd build && meson test -v

build-macos:
    stage: build
    tags:
        - amd64
        - macos
    script:
        - meson setup build --buildtype release
                            -Ddefault_library=both
                            -Dtrim_dsp=false
                            --werror
        - ninja -C build
        - cd build && meson test -v

build-debian-werror:
    extends: .debian-aarch64-common
    variables:
        CC: clang
    script:
        - meson setup build --buildtype debug
                            --werror
        - ninja -C build

build-debian-armv7:
    extends: .debian-armv7-common
    script:
        - linux32 meson setup build --buildtype debugoptimized
                                    --werror
        - ninja -C build
        - cd build && meson test -v

build-debian-armv7-clang-5:
    extends: .debian-armv7-common
    variables:
        CC: clang-5.0
        CFLAGS: '-integrated-as'
    script:
        - linux32 meson setup build --buildtype release
        - ninja -C build
        - cd build && meson test -v

build-debian-ppc64le:
    extends: .debian-ppc64le-common
    script:
        - meson setup build --buildtype release
                            -Dtrim_dsp=false
                            --werror
        - ninja -C build
        - cd build && meson test -v

build-debian-wasm:
    extends: .debian-wasm-emscripten-common
    script:
        - source $EMSCRIPTEN_SDK/emsdk_env.sh
        - meson setup build --buildtype release
                            --werror
                            --default-library static
                            --cross-file package/crossfiles/${CROSSFILE}.meson
        - ninja -C build
        - cd build && meson test -v
    parallel:
      matrix:
        - CROSSFILE: [wasm32, wasm64]


.test-common:
    stage: test
    cache:
        key: testdata.git-20190215
        paths:
            - cache/dav1d-test-data.git/
    before_script:
        - test -d cache || mkdir cache
        - test -d cache/dav1d-test-data.git && GIT_DIR=cache/dav1d-test-data.git git fetch --refmap=refs/heads/master:refs/heads/master origin master
        - test -d cache/dav1d-test-data.git || git clone --bare https://code.videolan.org/videolan/dav1d-test-data.git cache/dav1d-test-data.git
        - git clone cache/dav1d-test-data.git tests/dav1d-test-data
        - git -C tests/dav1d-test-data describe --always --long
    dependencies: []
    artifacts:
        when: always
        reports:
          junit: build/meson-logs/testlog.junit.xml

.test-asm-common:
    extends:
        - .debian-amd64-common
        - .test-common
    tags:
        - docker
        - amd64
        - avx2
    script:
        - meson configure build -Dtestdata_tests=true
        - ninja -C build
        - cd build
        - exit_code=0
        - time meson test -q --suite testdata-8 --suite testdata-10 --suite testdata-12 --test-args "--cpumask 0"     || exit_code=$((exit_code + $?))
        - time meson test -q --suite testdata-8 --suite testdata-10 --suite testdata-12 --test-args "--cpumask sse2"  || exit_code=$((exit_code + $?))
        - time meson test -q --suite testdata-8 --suite testdata-10 --suite testdata-12 --test-args "--cpumask ssse3" || exit_code=$((exit_code + $?))
        - time meson test -q --suite testdata-8 --suite testdata-10 --suite testdata-12 --test-args "--cpumask sse41" || exit_code=$((exit_code + $?))
        - time meson test -q --suite testdata-8 --suite testdata-10 --suite testdata-12 --test-args "--cpumask avx2"  || exit_code=$((exit_code + $?))
        - if [ $exit_code -ne 0 ]; then exit $exit_code; fi

test-debian:
    extends:
        - .debian-amd64-common
        - .test-common
    needs: ["build-debian"]
    script:
        - meson setup build --buildtype release
                            -Dtestdata_tests=true
                            -Dlogging=false
                            -Db_coverage=true
                            -Dtrim_dsp=false
        - ninja -C build
        - cd build && time meson test -v
        - ninja coverage-html
        - mv meson-logs/coveragereport ../coverage
        - ninja coverage-xml
        - grep -Eo 'line-rate="[^"]+"' meson-logs/coverage.xml | head -n 1 |
          grep -Eo '[0-9.]+' | awk '{ print "coverage:", $1 * 100 } '
        - time meson test -v --suite testdata_seek-stress --test-args "--threads 2 --framedelay 1"
        - time meson test -v --suite testdata_seek-stress --test-args "--threads 2 --framedelay 2"
        - time meson test -v --suite testdata-8 --suite testdata-10 --suite testdata-12 --test-args "--threads=1 --negstride"
    coverage: '/^coverage: (\d+.\d+)$/'
    artifacts:
        expose_as: 'Coverage HTML report'
        paths:
            - coverage/
        reports:
            coverage_report:
                coverage_format: cobertura
                path: build/meson-logs/coverage.xml

test-debian-asm:
    extends:
        - .test-asm-common
    needs: ["build-debian"]
    dependencies: ["build-debian"]

test-debian32-asm:
    extends:
        - .test-asm-common
    needs: ["build-debian32"]
    dependencies: ["build-debian32"]

test-debian-avx512:
    extends:
        - .debian-amd64-common
        - .test-common
    tags:
        - docker
        - amd64-avx512
    variables:
        CFLAGS: '-mavx'
    needs: ["build-debian-avx512"]
    script:
        - meson setup build --buildtype release
                            -Dtestdata_tests=true
                            -Dtrim_dsp=false
        - ninja -C build
        - cd build && time meson test --suite testdata-8 --suite testdata-10 --suite testdata-12 --test-args "--cpumask avx512icl"
        - time meson test --suite testdata-8 --suite testdata-10 --suite testdata-12 --test-args "--threads 2 --framedelay 2 --cpumask avx512icl"

test-debian-unaligned-stack:
    extends:
        - .debian-amd64-common
        - .test-common
    needs: ["build-debian"]
    tags:
        - docker
        - avx2
        - amd64
    script:
        - meson setup build --buildtype release
                            -Dtestdata_tests=true
                            -Dlogging=false
                            -Dstack_alignment=16
                            -Dtrim_dsp=false
        - ninja -C build
        - cd build && time meson test -v

test-debian-asan:
    extends:
        - .debian-amd64-common
        - .test-common
    needs: ["build-debian"]
    variables:
        ASAN_OPTIONS: 'detect_leaks=0'
    script:
        - meson setup build --buildtype debugoptimized
                            -Dtestdata_tests=true
                            -Dlogging=false
                            -Db_sanitize=address
        - ninja -C build
        - cd build
        - exit_code=0
        - time meson test -v --setup=sanitizer --test-args "--cpumask 0"    || exit_code=$((exit_code + $?))
        - time meson test -v --setup=sanitizer --test-args "--cpumask 0xff" || exit_code=$((exit_code + $?))
        - if [ $exit_code -ne 0 ]; then exit $exit_code; fi

test-debian-msan:
    extends:
        - .debian-amd64-common
        - .test-common
    needs: ["build-debian"]
    variables:
        MSAN_OPTIONS: 'exitcode=1'
        CC: clang
    script:
        - meson setup build --buildtype debugoptimized
                            -Dtestdata_tests=true
                            -Dlogging=false
                            -Db_sanitize=memory
                            -Db_lundef=false
                            -Denable_asm=false
        - ninja -C build
        - cd build && time meson test -v --setup=sanitizer

test-debian-ubsan:
    extends:
        - .debian-amd64-common
        - .test-common
    needs: ["build-debian"]
    variables:
        UBSAN_OPTIONS: 'print_stacktrace=1:halt_on_error=1'
        CC: clang
    script:
        - meson setup build --buildtype debugoptimized
                            -Dtestdata_tests=true
                            -Dlogging=false
                            -Db_sanitize=undefined
                            -Db_lundef=false
                            -Denable_asm=false
        - ninja -C build
        - cd build && time meson test -v --setup=sanitizer

test-debian-tsan:
    extends:
        - .debian-amd64-common
        - .test-common
    needs: ["build-debian"]
    variables:
        TSAN_OPTIONS: 'halt_on_error=1'
        CC: clang
    script:
        - meson setup build --buildtype debugoptimized
                            -Dtestdata_tests=true
                            -Dlogging=false
                            -Db_sanitize=thread
                            -Db_lundef=false
        - ninja -C build
        - cd build
        - exit_code=0
        - time meson test -v --setup=sanitizer --suite testdata-8 --suite testdata-10 --suite testdata-12 --test-args "--threads 2 --framedelay 1"             || exit_code=$((exit_code + $?))
        - time meson test -v --setup=sanitizer --suite testdata-8 --suite testdata-10 --suite testdata-12 --test-args "--threads 2 --framedelay 2"             || exit_code=$((exit_code + $?))
        - time meson test -v --setup=sanitizer --suite testdata-8 --suite testdata-10 --suite testdata-12 --test-args "--threads 2 --framedelay 2 --negstride" || exit_code=$((exit_code + $?))
        - time meson test -v --setup=sanitizer --suite testdata_seek-stress --test-args "--threads 2 --framedelay 1"                                           || exit_code=$((exit_code + $?))
        - time meson test -v --setup=sanitizer --suite testdata_seek-stress --test-args "--threads 2 --framedelay 2"                                           || exit_code=$((exit_code + $?))
        - time meson test -v --setup=sanitizer --suite oss-fuzz-asan --suite oss-fuzz-msan --suite oss-fuzz-ubsan                                              || exit_code=$((exit_code + $?))
        - if [ $exit_code -ne 0 ]; then exit $exit_code; fi

test-win64:
    extends:
        - .debian-amd64-common
        - .test-common
    needs: ["build-win: [x86_64-w64-mingw32]"]
    tags:
        - docker
        - avx2
        - amd64
    script:
        - wineserver -p && wine wineboot
        - meson setup build --buildtype release
                            -Dtestdata_tests=true
                            -Dlogging=false
                            -Dtrim_dsp=false
                            --cross-file package/crossfiles/x86_64-w64-mingw32.meson
        - ninja -C build
        - cd build && time meson test -v

test-debian-aarch64:
    extends:
        - .debian-aarch64-common
        - .test-common
    needs: ["build-debian-aarch64"]
    script:
        - meson setup build --buildtype release
                            -Dtestdata_tests=true
                            -Dlogging=false
                            -Dtrim_dsp=false
        - ninja -C build
        - cd build && time meson test -v

test-debian-ppc64le:
    extends:
        - .debian-ppc64le-common
        - .test-common
    needs: ["build-debian-ppc64le"]
    script:
        - meson setup build --buildtype release
                            -Dtestdata_tests=true
                            -Dlogging=false
                            -Dtrim_dsp=false
        - ninja -C build
        - cd build && time meson test -v

test-debian-armv7-clang-5:
    extends:
        - .debian-armv7-common
        - .test-common
    needs: ["build-debian-armv7-clang-5"]
    variables:
        CC: clang-5.0
        CFLAGS: '-integrated-as'
    script:
        - linux32 meson setup build --buildtype release
                                    -Dtestdata_tests=true
                                    -Dlogging=false
                                    -Dtrim_dsp=false
        - ninja -C build
        - cd build && time meson test -v


.pages-common:
    extends: .debian-amd64-common
    script:
        - meson setup build --buildtype release
                            --werror
                            -Denable_docs=true
        - ninja -C build doc/html
        - mv build/doc/html public
    artifacts:
        paths:
            - public

build-pages:
    extends: .pages-common
    rules:
        - if: '$CI_COMMIT_BRANCH && $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH'

pages:
    extends: .pages-common
    rules:
        - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'
          changes:
              - include/dav1d/*
              - doc/meson.build
              - doc/Doxyfile.in.in
