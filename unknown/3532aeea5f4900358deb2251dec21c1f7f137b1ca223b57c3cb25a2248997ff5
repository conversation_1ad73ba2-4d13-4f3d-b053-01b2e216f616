import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';
import { getGameConfigInputForGame } from 'modules/game/utils/getGameCategoryAndGameTypeInfos';
import { GAME_MODES, GAME_TYPES } from 'modules/game/constants/game';
import { GAME_FRAGMENT } from 'core/graphql/fragments/game';

const CHALLENGE_FRIEND = gql`
  ${GAME_FRAGMENT}
  mutation ChallengeUser($challengeUserInput: ChallengeUserInput) {
    challengeUser(challengeUserInput: $challengeUserInput) {
      ...CoreGameFields
    }
  }
`;

const useChallengeUser = () => {
  const [challengeUserQuery] = useMutation(CHALLENGE_FRIEND);

  const challengeUser = useCallback(
    async ({ userId, gameConfig }) => {
      const {
        gameType = GAME_TYPES.DMAS,
        timeLimit = 60,
        numPlayers = 2,
      } = gameConfig ?? EMPTY_OBJECT;

      return await challengeUserQuery({
        variables: {
          challengeUserInput: {
            userId,
            gameConfig: getGameConfigInputForGame({
              gameType,
              gameMode: GAME_MODES.ONLINE_CHALLENGE,
              timeLimit,
              configs: {
                numPlayers,
              },
            }),
          },
        },
      });
    },
    [challengeUserQuery],
  );

  return {
    challengeUser,
  };
};

export default useChallengeUser;
