import { expect, test } from '@playwright/test';
import { AUTH_STORAGE_KEY } from './testConstants';

test.beforeEach(async ({ page }) => {
  await page.goto('http://localhost:8081/home');
});

test.describe('Home Page Tests (Authenticated)', () => {
  test('should able to update profile', async ({ page }) => {
    const username = `testuser${(Math.random() * 1000).toFixed(0)}`;
    const weeklyLeagueButton = page.getByTestId(
      'WeeklyLeagueBottomSheetButton',
    );
    try {
      await weeklyLeagueButton.waitFor({ state: 'visible', timeout: 5000 });
      await weeklyLeagueButton.click();
    } catch (error) {}
    await page.getByTestId('user-profile-tooltip').click();
    await page.getByRole('tooltip').getByText('My Profile').click();
    await page.getByTestId('edit-profile').click();
    await page.getByPlaceholder('Enter User Name').fill(username);
    await page.getByTestId('country-dropdown').click();
    await page.getByPlaceholder('search country').fill('India');
    await page.getByTestId('India').locator('div').nth(1).click();
    await page.getByText('Save').click();
    await page.getByTestId('confirm-button').click();
    await page.waitForTimeout(100);
    const usernameText = await page.getByTestId('user-username').textContent();
    expect(usernameText).toBe(`@${username}`);
  });

  test('should logout', async ({ page }) => {
    const weeklyLeagueButton = page.getByTestId(
      'WeeklyLeagueBottomSheetButton',
    );
    try {
      await weeklyLeagueButton.waitFor({ state: 'visible', timeout: 5000 });
      await weeklyLeagueButton.click();
    } catch (error) {}
    await page.getByTestId('user-profile-tooltip').click();
    await page.getByRole('tooltip').getByText('Logout').click();
    await expect(page).toHaveURL('http://localhost:8081');
    const token = await page.evaluate(
      (key) => window.localStorage.getItem(key),
      AUTH_STORAGE_KEY,
    );
    expect(token).toBeNull();
  });
});
