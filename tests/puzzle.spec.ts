import { test, expect } from '@playwright/test';
import { AUTH_STORAGE_KEY, MOCK_AUTH_TOKEN_USER2 } from './testConstants';
import { decryptJsonData } from '@/src/core/utils/encryptions/decrypt';
import _map from 'lodash/map';
import { CrossMathCellType } from '@/src/modules/puzzles/types/crossMathCellType';
import { rowKey } from './testConstants';
import puzzleReader from '@/src/modules/puzzles/readers/puzzleReader';
import { waitForApiResponse } from './testUtils';

test.beforeEach(async ({ page }) => {
  await page.goto('http://localhost:8081/');
});

test.describe('Puzzle Page Tests', async () => {
  test('should complete cross-math puzzle successfully', async ({ page }) => {
    let cells: any = [];
    const weeklyLeagueButton = page.getByTestId(
      'WeeklyLeagueBottomSheetButton',
    );
    try {
      await weeklyLeagueButton.waitFor({ state: 'visible', timeout: 5000 });
      await page.getByRole('button', { name: 'Dialog Close' }).click();
    } catch (error) {}
    await page.getByText('DAILY PUZZLE').nth(1).click();
    await page.getByText('Cross-Math').click();
    const [apiResponse] = await Promise.all([
      page.waitForResponse(async (res) =>
        waitForApiResponse(res, {
          urlIncludes: '/api',
          method: 'POST',
          validateBody: (body) => !!body?.data?.getDailyPuzzleByType,
        }),
      ),
      page.getByText("PLAY TODAY'S PUZZLE").click(),
    ]);
    const responseBody = await apiResponse.json();
    if (responseBody?.data?.getDailyPuzzleByType) {
      cells = responseBody.data.getDailyPuzzleByType.cells;
    }
    for (let i = 0; i < cells.length; i++) {
      for (let j = 0; j < cells[i].length; j++) {
        const targetCell = cells[i][j];
        let itemClicked = false;
        if (
          targetCell.type === CrossMathCellType.Operand &&
          !targetCell.isVisible
        ) {
          try {
            const ind = i * cells[i].length + j;
            const cell = page.getByTestId(`grid-item-${ind}`);
            await cell.waitFor({ state: 'visible', timeout: 1000 });
            for (let k = 0; k < 2; k++) {
              for (let l = 0; l < 5; l++) {
                if (k === 0) {
                  const footerItem = page.getByTestId(
                    `footer-item-${rowKey.FIRST_ROW}-${l}-${targetCell.value}`,
                  );
                  if (await footerItem.isVisible()) {
                    await footerItem.click();
                    itemClicked = true;
                    break;
                  }
                } else {
                  const footerItem = page.getByTestId(
                    `footer-item-${rowKey.SECOND_ROW}-${l}-${targetCell.value}`,
                  );
                  if (await footerItem.isVisible()) {
                    await footerItem.click();
                    break;
                  }
                }
              }
              if (itemClicked) break;
            }
          } catch (error) {
            break;
          }
        }
      }
    }
    await expect(page.getByText('Share result')).toBeVisible();
    await page.getByTestId('backToHomeButtonTest').click();
    await expect(page).toHaveURL('http://localhost:8081/home');
  });

  test('should complete ken-ken puzzle successfully', async ({ page }) => {
    const weeklyLeagueButton = page.getByTestId(
      'WeeklyLeagueBottomSheetButton',
    );
    try {
      await weeklyLeagueButton.waitFor({ state: 'visible', timeout: 5000 });
      await page.getByRole('button', { name: 'Dialog Close' }).click();
    } catch (error) {}
    await page.getByText('DAILY PUZZLE').nth(1).click();
    await page.getByText('Ken Ken').click();
    const [apiResponse] = await Promise.all([
      page.waitForResponse(async (res) =>
        waitForApiResponse(res, {
          urlIncludes: '/api',
          method: 'POST',
          validateBody: (body) => !!body?.data?.getDailyPuzzleByType,
        }),
      ),
      page.getByText("PLAY TODAY'S PUZZLE").click(),
    ]);
    const responseBody = await apiResponse.json();
    if (responseBody?.data?.getDailyPuzzleByType) {
      const encryptedPuzzleString = puzzleReader.kenKenPuzzleString(
        responseBody.data.getDailyPuzzleByType,
      );
      const decryptedPuzzleString = decryptJsonData(encryptedPuzzleString);
      let solutions: string[] = [];
      if (decryptedPuzzleString) {
        const lines = decryptedPuzzleString.trim().split('\n');
        const sizeMatch = lines[0].match(/Size: (\d+)/);
        if (!sizeMatch) {
          throw new Error('Could not parse puzzle size from the first line.');
        }
        const size = parseInt(sizeMatch[1]);
        const totalCells = size * size;
        const solutionsStartInd = lines.findIndex((line: string) =>
          line.trim().startsWith('Solution:'),
        );
        if (solutionsStartInd === -1) {
          throw new Error('Could not find solutions in the puzzle string.');
        }
        const solutionsLines = lines.slice(solutionsStartInd + 1);
        solutions = solutionsLines
          .join('')
          .replace(/[^1-6]/g, '')
          .split('');
        for (let i = 0; i < totalCells; i++) {
          try {
            await page
              .getByTestId(`grid-item-${i}`)
              .waitFor({ state: 'visible', timeout: 1000 });
            await page.getByTestId(`grid-item-${i}`).click();
            await page.getByTestId(`footer-${solutions[i]}`).click();
          } catch (err) {
            break;
          }
        }
        await expect(page.getByText('Share result')).toBeVisible();
      }
    }
  });

  test('should complete daily-challenge puzzle successfully', async ({
    page,
  }) => {
    test.setTimeout(120000);
    await page.addInitScript(
      (tokenData) => {
        window.localStorage.setItem(tokenData.key, tokenData.token);
      },
      { key: AUTH_STORAGE_KEY, token: MOCK_AUTH_TOKEN_USER2 },
    );
    await page.goto('localhost:8081/home');
    const weeklyLeagueButton = page.getByTestId(
      'WeeklyLeagueBottomSheetButton',
    );
    try {
      await weeklyLeagueButton.waitFor({ state: 'visible', timeout: 5000 });
      await page.getByRole('button', { name: 'Dialog Close' }).click();
    } catch (error) {}

    try {
      const timeoutMs = 1000;
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(
          () =>
            reject(
              new Error(
                `Promise.all in daily-challenge test timed out after ${timeoutMs}ms`,
              ),
            ),
          timeoutMs,
        ),
      );

      const [apiResponse] = await Promise.race([
        Promise.all([
          page.waitForResponse(async (res) =>
            waitForApiResponse(res, {
              urlIncludes: '/api',
              method: 'POST',
              validateBody: (body) => !!body?.data?.dailyChallenge,
            }),
          ),
          page.getByText('OPEN #').click(),
        ]),
        timeoutPromise,
      ]);
      await page.getByRole('button', { name: 'Start Challenge' }).click();
      await page.waitForURL(
        /http:\/\/localhost:8081\/daily-challenge\/play\/[a-f0-9]+/,
      );
      const responseBody = await apiResponse.json();
      if (responseBody?.data?.dailyChallenge) {
        const { encryptedQuestions } = responseBody.data.dailyChallenge ?? {};
        const questions = encryptedQuestions
          ? _map(encryptedQuestions, decryptJsonData)
          : [];
        expect(questions.length).toBeGreaterThan(0);
        for (const question of questions) {
          const { answers } = question.question;
          const randomTimeout = Math.floor(Math.random() * 1001) + 1000;
          await page.waitForTimeout(randomTimeout);
          await page
            .getByRole('textbox', { name: 'Enter Answer' })
            .fill(answers[0]);
        }
        await expect(page.getByText('Lightning Fast!')).toBeVisible();
      }
    } catch (err) {
      await expect(page.getByText('current ranking')).toBeVisible();
      await expect(page.getByText('Time Taken')).toBeVisible();
    }
  });
});
