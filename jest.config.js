module.exports = {
  preset: 'react-native',
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  transformIgnorePatterns: [
    'node_modules/(?!(@rneui|expo-router|react-native|@react-native|react-native-.*|@react-native-.*|@expo/.*|expo-.*)/)',
  ],
  setupFilesAfterEnv: [
    '@testing-library/jest-native/extend-expect',
    '<rootDir>/__mocks__/molecule.setup.tsx',
    '<rootDir>/jest.setup.js',
  ],
  testPathIgnorePatterns: ['/node_modules/', '/android/', '/ios/', '/tests/'],
  collectCoverage: false,
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/styles.{js,jsx,ts,tsx}',
  ],
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
    '^@/(.*)$': '<rootDir>/$1',
    '^core/(.*)$': '<rootDir>/src/core/$1',
    '^atoms/(.*)$': '<rootDir>/src/components/atoms/$1',
    '^molecules/(.*)$': '<rootDir>/src/components/molecules/$1',
    '^shared/(.*)$': '<rootDir>/src/components/shared/$1',
    '^modules/(.*)$': '<rootDir>/src/modules/$1',
    '^overlays/(.*)$': '<rootDir>/src/overlays/$1',
    '^store/(.*)$': '<rootDir>/src/store/$1',
    '^navigator/(.*)$': '<rootDir>/src/navigator/$1',
    '^assets/(.*)$': '<rootDir>/assets/$1',
    '\\.(ttf)$': '<rootDir>/__mocks__/fileMock.js',
  },
};
