import useMediaQuery from "core/hooks/useMediaQuery"
import CompactProfileShimmer from "./CompactProfileShimmer"
import ExpandedProfileShimmer from "./ExpandedProfileShimmer"
import React from "react"

const ProfilePageShimmer = () => {
    const { isMobile:isCompactMode } = useMediaQuery()

    const ComponentTobeRendered = isCompactMode ? CompactProfileShimmer : ExpandedProfileShimmer

    return (
        <ComponentTobeRendered/>
    )
}

export default React.memo(ProfilePageShimmer)

