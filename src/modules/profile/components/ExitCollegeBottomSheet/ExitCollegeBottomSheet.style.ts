import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

const styles = StyleSheet.create({
  bottomSheetContainer: {
    padding: 20,
    backgroundColor: dark.colors.background,
    minWidth: 280,
    alignItems: 'center',
    gap: 12,
  },
  bottomSheetIconPlaceholder: {
    width: 100,
    height: 100,
    backgroundColor: dark.colors.tertiary,
    borderRadius: 10,
    marginBottom: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomSheetTitle: {
    fontSize: 20,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textLight,
    textAlign: 'center',
    textTransform: 'uppercase',
  },
  bottomSheetMessage: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: withOpacity(dark.colors.text, 0.6),
    marginBottom: 28,
    textAlign: 'center',
    lineHeight: 20,
    letterSpacing: 1,
    textTransform: 'uppercase',
  },
  bottomSheetButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    gap: 10,
  },
  buttonContainer: {
    flex: 1,
    height: 51,
    paddingTop: 10,
  },
  bottomSheetButton: {
    backgroundColor: dark.colors.tertiary,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  bottomSheetButtonText: {
    fontSize: 12,
    fontFamily: 'Montserrat-800',
    color: dark.colors.textLight,
  },
  removeButton: {
    backgroundColor: dark.colors.errorDark,
  },
  removeButtonText: {
    color: dark.colors.text,
  },
});

export default styles;
