import React from 'react';
import { Text, View } from 'react-native';
import DefaultCollegeIcon from 'atoms/DefaultCollegeIcon';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';
import styles from './ExitCollegeBottomSheet.style';

interface ExitCollegeBottomSheetProps {
  institutionName?: string | null;
  onChangeCollege: () => void;
  onRemoveCollege: () => void;
}

const ExitCollegeBottomSheet: React.FC<ExitCollegeBottomSheetProps> = ({
  institutionName,
  onChangeCollege,
  onRemoveCollege,
}) => (
  <View style={styles.bottomSheetContainer}>
    <View style={styles.bottomSheetIconPlaceholder}>
      <DefaultCollegeIcon size={40} />
    </View>

    <Text style={styles.bottomSheetTitle}>Exit From COLLEGE?</Text>
    <Text style={styles.bottomSheetMessage}>
      ARE U SURE YOU WANT TO EXIT FROM {institutionName || 'YOUR COLLEGE'}?
    </Text>
    <View style={styles.bottomSheetButtonContainer}>
      <InteractiveSecondaryButton
        label="CHANGE COLLEGE"
        onPress={onChangeCollege}
        buttonContainerStyle={styles.buttonContainer}
        labelStyle={styles.bottomSheetButtonText}
        borderColor={withOpacity(dark.colors.textLight, 0.6)}
      />
      <InteractiveSecondaryButton
        label="REMOVE"
        onPress={onRemoveCollege}
        labelStyle={styles.bottomSheetButtonText}
        buttonContainerStyle={styles.buttonContainer}
        borderColor={dark.colors.defeatColor}
      />
    </View>
  </View>
);

export default React.memo(ExitCollegeBottomSheet);
