import React from 'react';
import { Image, View, Text } from 'react-native';
import AntDesign from '@expo/vector-icons/AntDesign';
import StreakIcon from '@/assets/images/icons/Streak.png';
import useStreakAnalytics from '../../hooks/query/useStreakAnalytics';
import dark from '../../../../core/constants/themes/dark';
import useExpandedStreakCardStyles from './ExpandedStreakCard.style';

const ExpandedStreakCard = () => {
  const styles = useExpandedStreakCardStyles();
  const currentDayIndex = new Date().getDay();

  const {
    currentStreakCount,
    personName,
    weekDays,
    maxStreakCount,
    streakStatus,
  } = useStreakAnalytics();

  return (
    <View style={styles.containerStyle}>
      <View style={{ flexDirection: 'row', gap: 16 }}>
        <Image source={StreakIcon} style={styles.streakIcon} />
        <View>
          <Text style={styles.daysCountText}>{currentStreakCount}</Text>
          <Text style={styles.daysText}>
            {currentStreakCount <= 1 ? 'Day Streak' : 'Days Streak'}
          </Text>
          <Text style={styles.bestStreakText}>
            {`${maxStreakCount} ${maxStreakCount > 1 ? 'Days' : 'D'} Best Streak`}
          </Text>
        </View>
      </View>
      <View style={styles.streakDaysContainer}>
        {weekDays.map((day, index) => (
          <View key={`weekday-${index}-${day}`} style={styles.streakDay}>
            {!streakStatus[index] ? (
              <View style={styles.streakUnactiveBox} />
            ) : (
              <View style={[styles.streakUnactiveBox, styles.streakActiveBox]}>
                <AntDesign
                  name="check"
                  size={10}
                  color="black"
                  style={{ fontWeight: '800' }}
                />
              </View>
            )}
            <Text
              style={[
                styles.dayText,
                currentDayIndex == index && { color: dark.colors.secondary },
              ]}
            >
              {day}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
};

export default React.memo(ExpandedStreakCard);
