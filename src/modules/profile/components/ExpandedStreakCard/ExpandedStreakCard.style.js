import { StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';
import dark from '../../../../core/constants/themes/dark';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },

    innerContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      height: '70%',
    },
    streakIconText: {
      fontSize: 64,
      lineHeight: 78,
    },
    daysCountText: {
      fontSize: 20,
      lineHeight: 25,
      fontFamily: 'Montserrat-800',
      color: 'white',
    },
    daysText: {
      fontSize: 12,
      lineHeight: 15,
      fontFamily: 'Montserrat-600',
      color: 'white',
    },
    doingGreatTextModal: {
      fontSize: 14,
      textAlign: 'center',
      lineHeight: 24,
      paddingHorizontal: 55,
      fontFamily: 'Montserrat-500',
      color: dark.colors.textDark,
      marginBottom: '20%',
    },
    doingGreatText: {
      width: '100%',
      position: 'absolute',
      fontSize: 14,
      textAlign: 'center',
      lineHeight: 24,
      paddingHorizontal: 55,
      fontFamily: 'Montserrat-500',
      color: dark.colors.textDark,
      bottom: '20%',
    },
    bestStreakText: {
      fontSize: 10,
      color: dark.colors.textDark,
      fontFamily: 'Montserrat-600',
      lineHeight: 20,
    },
    shareStreakButton: {
      width: '100%',
      alignItems: 'center',
      position: 'absolute',
      bottom: 20,
    },
    shareStreakText: {
      fontSize: 16,
      lineHeight: 20,
      fontFamily: 'Montserrat',
      color: dark.colors.secondary,
      fontWeight: '600',
    },

    // CARD
    streakCardStyle: {
      minWidth: 41,
      height: 28,
      justifyContent: 'center',
      backgroundColor: dark.colors.primary,
      paddingHorizontal: 10,
      borderRadius: 5,
    },
    streakCardTextStyle: {
      color: 'white',
      textAlign: 'center',
      fontFamily: 'Montserrat-600',
      fontSize: isCompactMode ? 10 : 13,
      lineHeight: isCompactMode ? 13 : 20,
    },
    streakDaysContainer: {
      width: '100%',
      gap: 10,
      justifyContent: 'center',
      flexDirection: 'row',
    },
    dayText: {
      fontFamily: 'Montserrat-700',
      fontSize: 10,
      lineHeight: 12,
      color: dark.colors.textDark,
    },
    streakDay: {
      gap: 4,
      justifyContent: 'center',
      alignItems: 'center',
    },
    streakUnactiveBox: {
      height: 20,
      width: 20,
      borderColor: dark.colors.tertiary,
      borderWidth: 2,
      borderRadius: 4,
    },
    streakActiveBox: {
      borderColor: dark.colors.secondary,
      backgroundColor: dark.colors.secondary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    streakIcon: {
      height: 'auto',
      minHeight: 54,
      width: 44,
    },

    containerStyle: {
      backgroundColor: dark.colors.gradientBackground,
      paddingHorizontal: 18,
      borderRadius: 12,
      paddingVertical: 23,
      justifyContent: 'center',
      gap: 24,
    },
  });

const useExpandedStreakCardStyles = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(() => createStyles(isMobile), [isMobile]);

  return styles;
};

export default useExpandedStreakCardStyles;
