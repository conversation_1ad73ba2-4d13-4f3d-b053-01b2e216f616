import AchievementIcon from "@/assets/images/icons/achievement.png"
import { Image, View, Text } from "react-native"
import PropTypes from "prop-types"
import styles from "./AchievementCard.style"
import React from "react"

const AchievementCard = (props) => {
    const { title, description, imageUrl } = props
    return (
        <View style={styles.container}>
            <Image style={styles.imageContainer} source={imageUrl ?? AchievementIcon} />
            <View style={styles.infoColumn}>
                <Text style={styles.titleText} numberOfLines={1}>
                    {title}
                </Text>
                <Text style={styles.descriptionText}>
                    {description}
                </Text>
            </View>
        </View>
    )
}

AchievementCard.propTypes = {
    title: PropTypes.string,
    description: PropTypes.string,
    imageUrl: PropTypes.string
}

export default React.memo(AchievementCard)