import React, { useCallback } from 'react';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import {
  closeBottomSheet,
  openBottomSheet,
} from 'molecules/BottomSheet/BottomSheet';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import { useSession } from 'modules/auth/containers/AuthProvider';
import useUpdateUserProfile from 'modules/profile/hooks/query/useUpdateUserProfile';
import useGoBack from 'navigator/hooks/useGoBack';
import ExitCollegeBottomSheet from 'modules/profile/components/ExitCollegeBottomSheet';
import dark from 'core/constants/themes/dark';
import userReader from 'core/readers/userReader';
import { router, usePathname } from 'expo-router';
import _isNil from 'lodash/isNil';
import { TouchableOpacity } from 'react-native';
import Icon, { ICON_TYPES } from 'atoms/Icon';
import _includes from 'lodash/includes';

const ExitCollegeButton = () => {
  const { user, updateCurrentUser } = useSession();
  const { updateUser } = useUpdateUserProfile();
  const { goBack } = useGoBack();
  const currentActivePath = usePathname();

  const currentUserInstitutionName = userReader.institutionName(user);
  const currentUserInstitutionId = userReader.institutionId(user);

  const handleChangeCollege = useCallback(() => {
    closeBottomSheet();
    Analytics.track(ANALYTICS_EVENTS.PROFILE.CLICKED_ON_CHANGE_COLLEGE);
    if (
      _includes(currentActivePath, 'add-college') &&
      !_includes(currentActivePath, 'friends')
    ) {
      return;
    }
    router.push(
      `/profile/${userReader.username(user)}/add-college?instituteName=${currentUserInstitutionName}`,
    );
  }, [currentActivePath, user, currentUserInstitutionName]);

  const handleRemoveCollege = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.PROFILE.CLICKED_ON_REMOVE_COLLEGE);
    try {
      updateCurrentUser?.({ institutionId: null, institutionName: null });
      closeBottomSheet?.();
      goBack?.();
      updateUser?.({ removeInstitution: true })
        .then(() => {
          showToast({
            type: TOAST_TYPE.SUCCESS,
            description: 'College removed successfully!',
          });
        })
        .then(() => {
          Analytics.track(ANALYTICS_EVENTS.PROFILE.REMOVED_COLLEGE);
        });
    } catch (error) {
      console.error('Failed to remove college:', error);
    }
  }, [updateUser, goBack, updateCurrentUser]);

  const handleOpenExitBottomSheet = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.PROFILE.CLICKED_ON_EXIT_COLLEGE_ICON);
    openBottomSheet({
      content: (
        <ExitCollegeBottomSheet
          institutionName={currentUserInstitutionName}
          onChangeCollege={handleChangeCollege}
          onRemoveCollege={handleRemoveCollege}
        />
      ),
      styles: {
        frame: {
          borderTopColor: dark.colors.defeatColor,
          padding: 0,
        },
      },
    });
  }, [handleChangeCollege, handleRemoveCollege, currentUserInstitutionName]);

  if (!_isNil(currentUserInstitutionId)) {
    return (
      <TouchableOpacity onPress={handleOpenExitBottomSheet}>
        <Icon
          name="exit-outline"
          type={ICON_TYPES.IONICON}
          color={dark.colors.textLight}
          size={20}
        />
      </TouchableOpacity>
    );
  }

  return null;
};

export default React.memo(ExitCollegeButton);
