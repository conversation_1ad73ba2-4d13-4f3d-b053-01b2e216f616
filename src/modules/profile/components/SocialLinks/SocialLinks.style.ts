import { StyleSheet } from 'react-native';
import { withOpacity } from 'core/utils/colorUtils';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    gap: 12,
    paddingHorizontal: 16,
  },
  mainContainer: {
    backgroundColor: dark.colors.gradientBackground,
    borderRadius: 12,
    paddingVertical: 12,
  },
  linkItem: {
    width: 110,
    height: 28,
    borderWidth: 1,
    borderColor: withOpacity(dark.colors.textLight, 0.4),
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  iconContainer: {
    backgroundColor: '#353535',
    height: 28,
    width: 28,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderLeftWidth: 0,
    borderColor: withOpacity(dark.colors.textLight, 0.4),
  },
  linkText: {
    fontFamily: 'Montserrat-600',
    fontSize: 9,
    maxWidth: 75,
    color: withOpacity(dark.colors.textLight, 0.4),
    letterSpacing: 0.5,
  },
});

export default styles;
