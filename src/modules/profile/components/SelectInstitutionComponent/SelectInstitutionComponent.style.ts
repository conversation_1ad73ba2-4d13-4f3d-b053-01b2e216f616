import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

export default StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    backgroundColor: dark.colors.background,
  },
  input: {
    backgroundColor: dark.colors.primary,
    color: dark.colors.text,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    fontSize: 13,
    fontFamily: 'Montserrat-500',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    height: 40,
    outlineStyle: 'none',
  },
  loader: {
    marginVertical: 16,
  },
  popoverImagePlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: dark.colors.tertiary,
    marginBottom: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: dark.colors.error,
    textAlign: 'center',
    marginVertical: 16,
    fontSize: 13,
    fontFamily: 'Montserrat-400',
  },
  list: {
    flex: 1,
  },
  footerContainer: {
    gap: 16,
  },
  addNewText: {
    color: dark.colors.textDark,
    fontSize: 12,
    fontFamily: 'Montserrat-400',
    numberOfLines: 1,
  },
  addNewButton: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 12,
  },
  emptyText: {
    color: withOpacity(dark.colors.textLight, 0.4),
    lineHeight: 16,
    numberOfLines: 1,
    fontSize: 12,
    fontFamily: 'Montserrat-600',
  },
  confirmationContainer: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 'auto',
  },
  selectedInfoText: {
    color: dark.colors.text,
    fontSize: 13,
    fontFamily: 'Montserrat-500',
    flex: 1,
    marginRight: 10,
  },
  confirmButton: {
    backgroundColor: dark.colors.secondary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    height: 40,
    width: '100%',
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Montserrat-600',
  },
  headerExpanded: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 24,
    marginHorizontal: 16,
  },
  titleTextStyle: {
    color: 'white',
    fontSize: 17,
    fontFamily: 'Montserrat-500',
  },
  logoContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  logo: {
    width: 32,
    height: 32,
    borderRadius: 16,
    overflow: 'hidden',
  },
});
