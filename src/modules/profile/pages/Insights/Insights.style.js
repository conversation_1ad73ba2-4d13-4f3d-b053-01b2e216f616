import { StyleSheet } from 'react-native'
import dark from 'core/constants/themes/dark'
import useMediaQuery from 'core/hooks/useMediaQuery'
import { useMemo } from 'react'

const createStyles = (isCompactMode) =>
    StyleSheet.create({
        container: {
            flex: 1,
            paddingHorizontal: isCompactMode ? 0 : 16,
            width: '100%',
            height: '100%',
        },
        dropdownContainer: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: 20,
            width: '100%',
            gap: 20,
            paddingHorizontal: isCompactMode ? 16 : 0,
        },
        dropdown: {
            flex: 1,
            backgroundColor: dark.colors.cardBackground,
            borderRadius: 8,
            padding: 10,
            borderColor: dark.colors.background,
            borderWidth: 1,
        },
        section: {
            marginBottom: 30,
            width: '100%',
        },
        selectedTextStyle: {
            fontFamily: 'Montserrat-500',
            fontSize: 14,
            color: 'white',
        },
        label: {
            paddingHorizontal: 8,
            fontSize: 14,
            fontFamily: 'Montserrat-500',
            color: dark.colors.textDark,
        },
        itemContainerStyle: {},
        dropdownContainerItem: {
            backgroundColor: dark.colors.primary,
            // borderRadius: 16,
            borderColor: dark.colors.tertiary,
        },
        placeholderStyle: {
            fontSize: 14,
            fontFamily: 'Montserrat-500',
            color: dark.colors.textDark,
        },
        tabContainer: {
            flexDirection: 'row',
            justifyContent: 'space-around',
            marginBottom: 10,
        },
        tabItem: {
            padding: 10,
            borderBottomWidth: 2,
            borderBottomColor: dark.colors.tertiary,
        },
        activeTab: {
            borderBottomColor: dark.colors.secondary,
        },
        tabText: {
            fontFamily: 'Montserrat-400',
            fontSize: 14,
            lineHeight: 20,
            color: dark.colors.textDark,
        },
        activeTabText: {
            fontFamily: 'Montserrat-500',
            fontSize: 14,
            lineHeight: 20,
            color: dark.colors.secondary,
        },
        dropdownItem: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            padding: 10,
        },
        graphsSection: {
            paddingHorizontal: isCompactMode ? 16 : 0,
        },
    })

const useInsightPageStyles = () => {
    const { isMobile } = useMediaQuery()
    const styles = useMemo(() => createStyles(isMobile), [isMobile])
    return styles
}

export default useInsightPageStyles
