import React, { useEffect } from 'react';
import { Image, Text, View } from 'react-native';
import PaginatedList from 'shared/PaginatedList';
import shieldIcon from '@/assets/images/icons/shield_icon.png';
import Header from 'shared/Header';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import useStreakShieldTransactions from '../../hooks/useStreakShieldTransactions';
import useStreakAnalytics from '../../hooks/query/useStreakAnalytics';
import {
  EarnViaType,
  TransactionResult,
  TransactionType,
} from '../../types/streakShields';
import styles from './StreakShields.style';
import ReferralShieldCard from './components/ReferralShieldCard';
import BoughtShieldCard from './components/BoughtShieldCard';
import DebitShieldCard from './components/DebitShieldCard';

const StreakShields: React.FC = () => {
  const { streakFreezers } = useStreakAnalytics();
  const { fetchTransactions, pageInfo, isLoading } =
    useStreakShieldTransactions({ initialPageSize: 10 });

  useEffect(() => {
    Analytics.track(
      ANALYTICS_EVENTS.STREAKS.VIEWED_STREAK_FREEZER_TRANSACTIONS,
    );
  }, []);

  const renderCurrentShields = () => (
    <View style={styles.currentShieldsContainer}>
      <View style={styles.shieldIconContainer}>
        <Image source={shieldIcon} style={styles.shieldIcon} />
      </View>
      <View style={styles.shieldInfoContainer}>
        <Text style={styles.shieldCountText}>{streakFreezers}</Text>
        <Text style={styles.shieldLabelText}>Available Shields</Text>
      </View>
    </View>
  );

  const renderTransactionItem = ({ item }: { item: TransactionResult }) => {
    const { transaction, referralDetails } = item;

    if (transaction.transactionType === TransactionType.DEBITED) {
      return <DebitShieldCard transaction={transaction} />;
    }
    if (transaction.earnVia === EarnViaType.REFERRAL && referralDetails) {
      return (
        <ReferralShieldCard
          transaction={transaction}
          referralDetails={referralDetails}
        />
      );
    }
    return <BoughtShieldCard transaction={transaction} />;
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>No transactions found</Text>
    </View>
  );

  return (
      <View style={styles.container}>
        <Header title="Streak Shields" showBackInWeb />
        <View style={styles.content}>
          {renderCurrentShields()}
          <View style={styles.transactionsContainer}>
            <Text style={styles.sectionTitle}>Transaction History</Text>
            <PaginatedList
              fetchData={fetchTransactions}
              renderItem={renderTransactionItem}
              keyExtractor={(item) => item?.transaction?._id}
              loading={isLoading}
              hasMore={pageInfo.hasMore}
              pageSize={10}
              contentContainerStyle={styles.listContentContainer}
              emptyListComponent={renderEmptyState}
            />
          </View>
        </View>
      </View>
  );
};

// Card components for different transaction types
export default React.memo(StreakShields);
