import React from 'react';
import { TransactionResult } from 'modules/profile/types/streakShields';
import { format } from 'date-fns';
import { Image, Text, View } from 'react-native';
import styles from 'modules/profile/pages/StreakShields/StreakShields.style';
import shieldIcon from 'assets/images/icons/shield_icon.png';

const DebitShieldCard: React.FC<{
  transaction: TransactionResult['transaction'];
}> = ({ transaction }) => {
  const formattedDate = format(new Date(transaction.createdAt), 'dd MMM yyyy');

  return (
    <View style={[styles.transactionCard, styles.debitCard]}>
      <View style={styles.cardIconContainer}>
        <Image source={shieldIcon} style={styles.cardIcon} />
      </View>
      <View style={styles.cardContent}>
        <Text style={styles.cardTitle}>Shield Used</Text>
        <Text style={styles.cardSubtitle}>{formattedDate}</Text>
      </View>
      <View style={styles.cardQuantity}>
        <Text style={[styles.quantityText, styles.debitQuantity]}>
          -{transaction.quantity}
        </Text>
      </View>
    </View>
  );
};

export default React.memo(DebitShieldCard);
