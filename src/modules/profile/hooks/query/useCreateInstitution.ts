import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';
import { SEARCH_INSTITUTIONS_QUERY } from 'modules/profile/hooks/query/useSearchInstitutions';

const CREATE_INSTITUTION_MUTATION = gql`
  mutation CreateInstitution($input: CreateInstitutionInput!) {
    createInstitution(input: $input) {
      id
      name
      domains
      country
      state
      city
      slug
    }
  }
`;

const useCreateInstitution = () => {
  const [createInstitutionMutation, { loading, error }] = useMutation(
    CREATE_INSTITUTION_MUTATION,
    {
      update(cache, { data: { createInstitution: newInstitution } }) {
        try {
          const existingSearchData = cache.readQuery({
            query: SEARCH_INSTITUTIONS_QUERY,
            variables: { query: newInstitution.name, limit: 10 },
          });

          if (existingSearchData && existingSearchData?.searchInstitutions) {
            cache.writeQuery({
              query: SEARCH_INSTITUTIONS_QUERY,
              variables: { query: newInstitution.name, limit: 10 },
              data: {
                searchInstitutions: [
                  newInstitution,
                  ...existingSearchData?.searchInstitutions,
                ].slice(0, 10),
              },
            });
          }
        } catch (e) {
          // Cache entry might not exist, ignore error
        }
      },
    },
  );

  const createInstitution = useCallback(
    async (input: any) => {
      if (!input || !input?.name) {
        console.error('Institution name is required.');
        return null;
      }
      try {
        const response = await createInstitutionMutation({
          variables: { input },
        });
        return response.data?.createInstitution;
      } catch (err) {
        console.error('Error creating institution:', err);
        return null;
      }
    },
    [createInstitutionMutation],
  );

  return {
    createInstitution,
    loading,
    error,
  };
};

export default useCreateInstitution;
