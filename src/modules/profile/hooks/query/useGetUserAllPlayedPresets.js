import { gql, useQuery } from '@apollo/client';
import _groupBy from 'lodash/groupBy';
import _isNil from 'lodash/isNil';
import _isEmpty from 'lodash/isEmpty';
import React from 'react';
import _map from 'lodash/map';
import _toNumber from 'lodash/toNumber';
import { getTagStringFromIdentifier } from '../../../practice/utils/getIdentifierStringFromConfig';

export const GET_USERS_ALL_PLAYED_PRESETS = gql`
  query GetUsersAllPlayedPresets($username: String!) {
    getUsersAllPlayedPresets(username: $username) {
      presets {
        identifier
        avgTime
      }
      count
    }
  }
`;

const useGetUsersAllPlayedPresets = ({ username }) => {
  const { data, loading, error } = useQuery(GET_USERS_ALL_PLAYED_PRESETS, {
    variables: {
      username,
    },
  });

  const categorizedData = React.useMemo(() => {
    if (
      _isNil(data) ||
      _isEmpty(data) ||
      _isEmpty(data?.getUsersAllPlayedPresets) ||
      _isNil(data?.getUsersAllPlayedPresets)
    )
      return {};

    const { presets } = data?.getUsersAllPlayedPresets;

    const adaptedPresets = _map(presets, (preset) => ({
      ...preset,
      avgTime: Math.abs(_toNumber(preset.avgTime)),
    }));

    return _groupBy(adaptedPresets, (preset) =>
      getTagStringFromIdentifier({ identifier: preset.identifier }),
    );
  }, [data]);

  return {
    playedPresets: categorizedData,
    loading,
    error,
  };
};

export default useGetUsersAllPlayedPresets;
