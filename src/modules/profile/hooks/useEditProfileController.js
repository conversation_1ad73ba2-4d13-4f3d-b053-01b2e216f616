import { useCallback, useEffect, useMemo, useState } from 'react';
import _isEmpty from 'lodash/isEmpty';
import _toString from 'lodash/toString';
import _slice from 'lodash/slice';
import _size from 'lodash/size';
import _map from 'lodash/map';
import { launchImageLibraryAsync } from 'expo-image-picker';
import { useRouter } from 'expo-router';

import useGoBack from 'navigator/hooks/useGoBack';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';

import { closeRightPane } from 'molecules/RightPane/RightPane';
import _isNil from 'lodash/isNil';
import _debounce from 'lodash/debounce';
import _isEqual from 'lodash/isEqual';
import _some from 'lodash/some';
import userReader from 'core/readers/userReader';
import useMediaQuery from 'core/hooks/useMediaQuery';
import useGuestLogin from 'modules/auth/hooks/useGuestLogin';
import useUploadFiles from '../../../core/hooks/useUploadFiles';
import useCheckIsUserNameAvailable from './query/useCheckIsUserNameAvailable';
import useUpdateUserProfile from './query/useUpdateUserProfile';
import useUploadProfilePicture from './mutations/useUploadProfilePicture';
import usePickAndCompressImage from '../../../core/hooks/usePickAndCompressImage';

const EMPTY_OBJECT = {};
const USERNAME_MIN_LENGTH = 3;
const BIO_MAX_LENGTH = 100;
const USERNAME_REGEX = /^[a-zA-Z0-9_.]+$/;
const SOCIAL_LINK_REGEX = /^(https?:\/\/)?([\w-]+(\.[\w-]+)+\/?|@[\w._-]+).*$/;
const DEBOUNCE_DELAY = 500;

const initialErrors = {
  username: '',
  name: '',
  countryCode: '',
  bio: '',
};

const getInitialUserDetails = (user) => ({
  username: user?.username || '',
  name: user?.name || '',
  countryCode: user?.countryCode || '',
  bio: user?.bio || '',
});

const getInitialUserAchievements = (user) => {
  const achievementAndAwards = user?.awardsAndAchievements || [];
  return _map(achievementAndAwards, (item, index) => ({
    imageUrl: item?.imageUrl,
    link: item?.link,
    description: item?.description,
    title: item?.title,
  }));
};

const getInitialSocialLinks = (user) =>
  _map(user?.links ?? EMPTY_ARRAY, (link) => ({ link }));

const validateField = ({ key, value }) => {
  const trimmedValue = _toString(value).trim();

  switch (key) {
    case 'name':
      if (_isEmpty(trimmedValue)) return 'Full name is required';
      if (trimmedValue.length < 2)
        return 'Full name must be at least 2 characters';
      break;

    case 'countryCode':
      if (_isEmpty(trimmedValue)) return 'Country is required';
      break;

    case 'bio':
      if (!_isEmpty(trimmedValue) && trimmedValue.length > BIO_MAX_LENGTH) {
        return `Bio must not exceed ${BIO_MAX_LENGTH} characters`;
      }
      break;

    case 'socialLink':
      if (!_isEmpty(trimmedValue) && !SOCIAL_LINK_REGEX.test(trimmedValue)) {
        return 'Please enter a valid social media link or handle';
      }
      break;
    case 'link':
      if (!_isEmpty(trimmedValue) && !SOCIAL_LINK_REGEX.test(trimmedValue)) {
        return 'Please enter a valid award link';
      }
      break;
  }
  return '';
};

const useEditProfileController = () => {
  const { user, updateCurrentUser, signOut } = useSession();
  const { setGuestId } = useGuestLogin();
  const { goBack } = useGoBack();
  const router = useRouter();
  const { compressionStatus, pickAndCompressImage, compressImage } =
    usePickAndCompressImage();
  const { uploadImage: uploadProfileImage } = useUploadProfilePicture();

  const { uploadFile } = useUploadFiles();
  const { updateUser } = useUpdateUserProfile();
  const { isMobile: isCompactMode } = useMediaQuery();

  const [editedUserDetails, setEditedUserDetails] = useState(
    getInitialUserDetails(user),
  );
  const [isUpdatingDetails, setIsUpdatingDetails] = useState(false);
  const [errors, setErrors] = useState(initialErrors);
  const [isImagePopoverVisible, setIsImagePopoverVisible] = useState(false);
  const [isSavePopoverVisible, setIsSavePopoverVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState(user?.image);
  const [isImageLoading, setIsImageLoading] = useState(false);
  const [currentUsername, setCurrentUsername] = useState('');

  const [achievements, setAchievements] = useState(
    getInitialUserAchievements(user),
  );
  const [socialHandles, setSocialHandles] = useState(
    getInitialSocialLinks(user),
  );

  const {
    isAvailable: isUsernameAvailable,
    loading: isCheckingUsername,
    error: usernameError,
    checkUserNameAvailability,
  } = useCheckIsUserNameAvailable(currentUsername);

  const achievementsList = useMemo(() => {
    if (_isEmpty(achievements)) {
      return [];
    }

    if (achievements.length > 0 && _isEmpty(achievements[0].title)) {
      return _map(
        _slice(achievements, 0, achievements.length - 1),
        (item, index) => ({
          imageUrl: item?.imageUrl,
          link: item?.link,
          description: item?.description,
          title: item?.title,
        }),
      );
    }

    return _map(achievements, (item, index) => ({
      imageUrl: item?.imageUrl,
      link: item?.link,
      description: item?.description,
      title: item?.title,
    }));
  }, [achievements]);

  const socialLinks = useMemo(() => {
    if (_isEmpty(socialHandles)) {
      return [];
    }

    if (socialHandles.length > 0 && _isEmpty(socialHandles[0].link)) {
      return _map(
        _slice(socialHandles, 0, socialHandles.length - 1),
        (item) => item.link,
      );
    }
    return _map(socialHandles, (item) => item.link);
  }, [socialHandles]);

  const hasEdits = useCallback(() => {
    const hasBasicEdits = Object.keys(editedUserDetails).some(
      (key) => !_isEqual(editedUserDetails[key], user?.[key]),
    );

    const originalAchievements = getInitialUserAchievements(user);

    const hasAchievementEdits =
      JSON.stringify(achievementsList) !== JSON.stringify(originalAchievements);
    const originalSocialLinks = _map(getInitialSocialLinks(user), 'link');
    const hasSocialEdits =
      JSON.stringify(socialLinks) !== JSON.stringify(originalSocialLinks);
    return hasBasicEdits || hasAchievementEdits || hasSocialEdits;
  }, [editedUserDetails, user, achievementsList, socialLinks]);

  const debouncedCheckUsername = useCallback(
    _debounce((username) => {
      checkUserNameAvailability({ username });
    }, DEBOUNCE_DELAY),
    [checkUserNameAvailability],
  );

  const validateUsername = useCallback((username) => {
    if (_isEmpty(username)) {
      return 'Username is required';
    }
    if (username.length < USERNAME_MIN_LENGTH) {
      return 'Username must be at least 3 characters';
    }
    if (!USERNAME_REGEX.test(username)) {
      return 'Username can only contain letters, numbers and underscores';
    }
    return null;
  }, []);

  const handleInputChange = useCallback(
    ({ key, text }) => {
      setEditedUserDetails((prev) => ({ ...prev, [key]: text }));
      if (key === 'username') {
        if (text !== user?.username) {
          setCurrentUsername(text);
        }
        const error = validateUsername(text);
        if (_isNil(error)) {
          debouncedCheckUsername(text);
        }
        setErrors((prev) => ({ ...prev, username: error }));
      } else {
        const error = validateField({ key, value: text });
        setErrors((prev) => ({ ...prev, [key]: error }));
      }
    },
    [debouncedCheckUsername, validateUsername, user?.username],
  );

  useEffect(() => {
    if (isUsernameAvailable) {
      setErrors((prev) => ({ ...prev, username: '' }));
    } else {
      setErrors((prev) => ({ ...prev, username: 'Username is not available' }));
    }
  }, [isUsernameAvailable]);

  const isFormValid = useCallback(() => {
    const requiredFields = ['username', 'name', 'countryCode'];
    const hasErrors = Object.values(errors).some((error) => _size(error) > 0);
    const hasRequiredFields = requiredFields.every(
      (field) => editedUserDetails[field] && editedUserDetails[field].trim(),
    );

    const hasSocialLinkErrors = socialHandles.some((handle) => handle.error);

    const hasAchievementErrors = _some(
      achievements,
      (achievement) => _size(achievement.error) > 0,
    );

    const isEdited = hasEdits();

    return (
      !hasErrors &&
      hasRequiredFields &&
      !hasSocialLinkErrors &&
      !hasAchievementErrors &&
      isEdited &&
      (editedUserDetails.username === user?.username || isUsernameAvailable) &&
      !isCheckingUsername
    );
  }, [
    errors,
    editedUserDetails,
    user?.username,
    isUsernameAvailable,
    isCheckingUsername,
    socialHandles,
    hasEdits,
    achievements,
  ]);

  const handleChooseImage = useCallback(async () => {
    try {
      setIsImageLoading(true);
      const result = await launchImageLibraryAsync({
        mediaTypes: 'Images',
        allowsEditing: true,
        quality: 1,
        allowsMultipleSelection: false,
      });

      const { assets } = result ?? EMPTY_OBJECT;
      if (!_isEmpty(assets) && !_isEmpty(assets[0].uri)) {
        const compressedUri = await compressImage(assets[0].uri);
        const uploadedUrl = await uploadProfileImage({
          fileUri: compressedUri,
        });
        await updateCurrentUser({ profileImageUrl: uploadedUrl });
        setSelectedImage(uploadedUrl);
      }
      setIsImageLoading(false);
    } catch (error) {
      console.error('Error uploading image:', error);
      setIsImageLoading(false);
    } finally {
      setIsImageLoading(false);
      setIsImagePopoverVisible(false);
    }
  }, [compressImage, uploadProfileImage, updateCurrentUser]);

  const handleRemoveImage = useCallback(() => {
    setSelectedImage(null);
    setEditedUserDetails((prev) => ({ ...prev, profilePicture: null }));
    setIsImagePopoverVisible(false);
  }, []);

  const handleSave = useCallback(async () => {
    if (isUpdatingDetails || !isFormValid()) return;
    try {
      const updatedUserObj = {
        ...editedUserDetails,
        awardsAndAchievements: achievementsList,
        links: socialLinks,
      };
      setIsUpdatingDetails(true);
      const { data } = await updateUser({ ...updatedUserObj });
      await updateCurrentUser({ ...data?.updateUser });
      if (!_isEqual(editedUserDetails?.username, userReader.username(user))) {
        if (isCompactMode) {
          router.replace(`/profile/${editedUserDetails.username}`);
        } else {
          router.setParams({ username: editedUserDetails.username });
          closeRightPane?.();
        }
      } else if (isCompactMode) {
        goBack?.();
      } else {
        closeRightPane?.();
      }

      setIsSavePopoverVisible(false);
    } catch (error) {
      //
    } finally {
      setIsUpdatingDetails(false);
    }
  }, [
    isUpdatingDetails,
    isFormValid,
    editedUserDetails,
    achievementsList,
    socialLinks,
    updateUser,
    updateCurrentUser,
    user,
    isCompactMode,
    router,
  ]);

  const handleAddAchievement = useCallback(() => {
    if (
      !_isEmpty(achievements) > 0 &&
      _isEmpty(achievements[_size(achievements) - 1].title?.trim())
    ) {
      return;
    }
    setAchievements((prev) => [
      ...prev,
      {
        title: '',
        description: '',
        imageUrl: null,
        link: '',
        isPickingImage: false,
      },
    ]);
  }, [achievements]);

  const handleAchievementChange = useCallback(
    (index, key, value) => {
      let error = '';
      if (key === 'link') {
        error = validateField({ key, value });
      }
      setAchievements((prev) => {
        const updated = [...prev];
        updated[index] = { ...updated[index], [key]: value, error };
        return updated;
      });
    },
    [setAchievements],
  );

  const handleRemoveAchievement = useCallback((index) => {
    setAchievements((prev) => prev.filter((_, i) => i !== index));
  }, []);

  const handleAddSocialHandle = useCallback(() => {
    if (
      !_isEmpty(socialHandles) > 0 &&
      _isEmpty(socialHandles[_size(socialHandles) - 1].link?.trim())
    ) {
      return;
    }
    setSocialHandles((prev) => [...prev, { link: '', error: '' }]);
  }, [socialHandles]);

  const handleSocialHandleChange = useCallback((index, key, value) => {
    const error = validateField({ key: 'socialLink', value });
    setSocialHandles((prev) => {
      const updated = [...prev];
      updated[index] = { ...updated[index], [key]: value, error };
      return updated;
    });
  }, []);

  const handleRemoveSocialHandle = useCallback((index) => {
    setSocialHandles((prev) => prev.filter((_, i) => i !== index));
  }, []);

  const toggleImagePopover = useCallback(() => {
    setIsImagePopoverVisible((prev) => !prev);
  }, []);

  const toggleSavePopover = useCallback(() => {
    if (!isFormValid()) {
      Object.keys(editedUserDetails).forEach((key) => {
        if (key === 'username') {
          const error = validateUsername(editedUserDetails[key]);
          setErrors((prev) => ({ ...prev, username: error }));
        } else {
          const error = validateField({ key, value: editedUserDetails[key] });
          setErrors((prev) => ({ ...prev, [key]: error }));
        }
      });
      return;
    }
    setIsSavePopoverVisible((prev) => !prev);
  }, [editedUserDetails, isFormValid, validateUsername]);

  useEffect(
    () => () => {
      debouncedCheckUsername.cancel();
    },
    [debouncedCheckUsername],
  );

  const pickAchievementImage = useCallback(
    async ({ index }) => {
      try {
        handleAchievementChange(index, 'isPickingImage', true);
        const imageUri = await pickAndCompressImage();

        const url = await uploadFile({ fileUri: imageUri });

        if (!_isEmpty(url)) {
          handleAchievementChange(index, 'imageUrl', url);
        }
      } catch (e) {
        handleAchievementChange(index, 'isPickingImage', false);
      } finally {
        handleAchievementChange(index, 'isPickingImage', false);
      }
    },
    [handleAchievementChange, uploadFile, pickAndCompressImage],
  );

  const hasUserChangedFields = useMemo(() => hasEdits(), [hasEdits]);

  return {
    user,
    editedUserDetails,
    errors,
    isImagePopoverVisible,
    isSavePopoverVisible,
    selectedImage,
    isImageLoading,
    compressionStatus,
    achievements,
    socialHandles,
    isUpdatingDetails,
    isCheckingUsername,
    isUsernameAvailable,
    usernameError,
    handleSave,
    handleInputChange,
    handleRemoveImage,
    handleChooseImage,
    isFormValid,
    validateField,
    editedUserName: currentUsername,
    compressImage,
    toggleImagePopover,
    toggleSavePopover,
    handleAchievementChange,
    handleAddAchievement,
    handleRemoveAchievement,
    handleAddSocialHandle,
    handleRemoveSocialHandle,
    handleSocialHandleChange,
    pickAchievementImage,
    hasEdits,
    hasUserChangedFields,
    goBack,
    updateUser,
  };
};

export default useEditProfileController;
