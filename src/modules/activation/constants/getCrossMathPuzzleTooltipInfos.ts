import CrossMathPuzzleQuestionGrid from 'modules/activation/pages/ExploreCrossMathPuzzle/components/CrossMathPuzzleQuestionGrid';
import CrossMathPuzzleOptions from 'modules/activation/pages/ExploreCrossMathPuzzle/components/CrossMathPuzzleOptions';
import CrossMathPuzzleFooter from 'modules/activation/pages/ExploreCrossMathPuzzle/components/CrossMathPuzzleFooter';

export const CROSS_MATH_PUZZLE_TOOLTIP_SECTIONS = {
  PUZZLE_HEADER: 'PUZZLE_HEADER',
  PUZZLE_GRID: 'PUZZLE_GRID',
  FOOTER_ITEMS: 'FOOTER_ITEMS',
  OPTIONS: 'OPTIONS',
};

export const CROSS_MATH_PUZZLE_WALKTHROUGH_STEPS = [
  {
    name: CROSS_MATH_PUZZLE_TOOLTIP_SECTIONS.PUZZLE_GRID,
    text: 'This is the puzzle grid. Your goal is to fill every square with a number.',
    order: 2,
    component: CrossMathPuzzleQuestionGrid,
  },
  {
    name: CROSS_MATH_PUZZLE_TOOLTIP_SECTIONS.OPTIONS,
    text: 'These are the options. You can tap on the number in the footer row to fill the grid cell.',
    order: 3,
    component: CrossMathPuzzleOptions,
  },
  {
    name: CROSS_MATH_PUZZLE_TOOLTIP_SECTIONS.FOOTER_ITEMS,
    text: 'These are the footer items. You can tap on the number in the footer row to fill the grid cell.',
    order: 3,
    component: CrossMathPuzzleFooter,
  },
];
