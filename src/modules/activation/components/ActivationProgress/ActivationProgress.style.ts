import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingHorizontal: 8,
    height: 36,
    alignItems: 'center',
    backgroundColor: dark.colors.primary,
    borderRadius: 20,
    marginHorizontal: 16,
    marginTop: 24,
  },
  closeButton: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepsContainer: {
    flex: 1,
    height: 8,
    gap: 4,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  step: {
    flex: 1,
    height: 8,
    minHeight: 8,
    borderRadius: 100,
    backgroundColor: dark.colors.tertiary,
  },
  activeStep: {
    backgroundColor: dark.colors.streak,
  },

  stepsCount: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textDark,
  },
});

export default styles;
