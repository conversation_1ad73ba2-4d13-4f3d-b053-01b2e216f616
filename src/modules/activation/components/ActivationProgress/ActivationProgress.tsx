import React from 'react';
import { Pressable, Text, View } from 'react-native';
import AntDesign from '@expo/vector-icons/AntDesign';

import dark from 'core/constants/themes/dark';
import styles from './ActivationProgress.style';

const ActivationProgress = ({
  onClose,
  stepsCount,
  onClickStep,
  currentStep,
}) => {
  const renderStep = (index) => (
    <Pressable
      key={index}
      onPress={() => onClickStep(index + 1)}
      style={[styles.step, index <= currentStep - 1 && styles.activeStep]}
    />
  );

  return (
    <View style={styles.container}>
      <Pressable onPress={onClose} style={styles.closeButton}>
        <AntDesign name="close" size={18} color={dark.colors.textDark} />
      </Pressable>
      <View style={styles.stepsContainer}>
        {[...Array(stepsCount)].map((_, index) => renderStep(index))}
      </View>

      <Text style={styles.stepsCount}>
        {currentStep}/{stepsCount}
      </Text>
    </View>
  );
};

export default React.memo(ActivationProgress);
