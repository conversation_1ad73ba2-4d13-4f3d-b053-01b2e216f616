import React from 'react';
import { Image, Text, View } from 'react-native';
import dark from '@/src/core/constants/themes/dark';
import { closeBottomSheet } from '@/src/components/molecules/BottomSheet/BottomSheet';
import { YStack } from 'tamagui';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import { withOpacity } from 'core/utils/colorUtils';

interface ModuleAlreadyExploredSheetProps {
  title: string;
  color: string;
  backgroundImage: string;
  completionTime: string;
  tutorialImageIcon: string;
}

const ModuleAlreadyExploredSheet = ({
  displayInfo,
}: {
  displayInfo: ModuleAlreadyExploredSheetProps;
}) => {
  const { title, color, tutorialImageIcon } = displayInfo ?? EMPTY_OBJECT;

  return (
    <YStack
      space="$4"
      alignItems="center"
      paddingHorizontal="$4"
      paddingBottom="$4"
    >
      <Image
        source={{ uri: tutorialImageIcon }}
        style={{ width: 72, height: 72, marginBottom: 20, marginTop: 32 }}
        resizeMode="contain"
      />
      <Text
        style={{
          color: '#FFFFFF',
          fontSize: 16,
          fontFamily: 'Montserrat-800',
          textAlign: 'center',
          textTransform: 'uppercase',
          letterSpacing: 0.5,
          marginBottom: 20,
        }}
      >
        YOU HAVE ALREADY COMPLETED
      </Text>
      <View
        style={{
          backgroundColor: withOpacity(color, 0.2),
          paddingVertical: 10,
          paddingHorizontal: 24,
          borderRadius: 25,
          marginBottom: 60,
        }}
      >
        <Text
          style={{
            color: withOpacity(dark.colors.textLight, 0.9),
            fontSize: 14,
            fontFamily: 'Montserrat-800',
          }}
        >
          {title.toUpperCase()}
        </Text>
      </View>
      <InteractiveSecondaryButton
        label="OKAY"
        onPress={closeBottomSheet}
        labelStyle={{
          color: dark.colors.textLight,
          fontSize: 14,
          fontFamily: 'Montserrat-800',
        }}
        buttonContainerStyle={{
          width: '100%',
          height: 48,
        }}
      />
    </YStack>
  );
};

export default React.memo(ModuleAlreadyExploredSheet);
