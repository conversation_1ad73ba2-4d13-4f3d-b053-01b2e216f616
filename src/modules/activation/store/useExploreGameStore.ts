import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import userReader from 'core/readers/userReader';
import { updateLeaderboardOnSubmitAnswer } from 'modules/activation/store/gameLeaderboardUtils';
import gameReader from 'core/readers/gameReader';
import _find from 'lodash/find';
import _toString from 'lodash/toString';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import { GAME_TYPES } from 'core/constants/gameTypes';
import _isNil from 'lodash/isNil';
import { getDummyFlashAnzanGame } from 'modules/activation/dummyData/dummyFlashAnzanGame';
import { getDummyGame as getDummyBlitzGame } from '../dummyData/dummyBlitzGame';
import { getDummyDmasAbilityGame } from '../dummyData/dmasAbilityGame';

// Types
export interface DummyQuestion {
  id: string;
  expression: string[];
  answers: string[];
  options?: number[];
  category: string;
}

export interface GameProgress {
  currentQuestionIndex: number;
  correctAnswers: number;
  incorrectAnswers: number;
  totalQuestions: number;
  timeRemaining: number;
  gameStartTime: number;
  gameEndTime?: number;
  userAnswers: {
    questionId: string;
    userAnswer: number | null;
    correctAnswer: number;
    isCorrect: boolean;
    timeSpent: number;
  }[];
}

export interface LeaderboardEntry {
  userId: string;
  username: string;
  score: number;
  correctAnswers: number;
  totalQuestions: number;
  accuracy: number;
  timeSpent: number;
  gameDate: string;
}

export interface ExploreGameState {
  // Game data
  questions: DummyQuestion[];
  currentGame: GameProgress | null;

  // Leaderboard
  leaderboard: LeaderboardEntry[];
  userBestScore: LeaderboardEntry | null;

  // Game state
  isGameActive: boolean;
  isGameCompleted: boolean;
  gameTimer: number;

  // user
  currentUser: any;

  // Actions
  initializeGame: ({
    currentUser,
    gameType,
  }: {
    currentUser: any;
    gameType: string;
  }) => void;
  endGame: () => void;
  submitAnswer: ({
    questionId,
    answer,
    timeSpent,
    maxScore,
  }: {
    questionId: string;
    answer: number;
    timeSpent: number;
    maxScore: number;
  }) => void;
  updateTimer: (timeRemaining: number) => void;
  addToLeaderboard: (entry: LeaderboardEntry) => void;
  resetGame: () => void;
  clearStore: () => void;
  handleAnswerSubmissionByBot: () => void;
}

const GAME_TIME = 15;

const getDummyGame = ({
  currentUser,
  gameType,
}: {
  currentUser: any;
  gameType: string;
}) => {
  if (gameType === GAME_TYPES.DMAS_ABILITY) {
    return getDummyDmasAbilityGame({ currentUser });
  }
  if (gameType === GAME_TYPES.FLASH_ANZAN) {
    return getDummyFlashAnzanGame({ currentUser });
  }
  return getDummyBlitzGame({ currentUser });
};

const defaultGameProgress: GameProgress = {
  currentQuestionIndex: 0,
  correctAnswers: 0,
  incorrectAnswers: 0,
  totalQuestions: 30,
  timeRemaining: GAME_TIME,
  gameStartTime: 0,
  userAnswers: [],
};

const useExploreGameStore = create<ExploreGameState>()(
  immer((set, get) => ({
    questions: [],
    currentGame: null,
    leaderboard: [],
    userBestScore: null,
    isGameActive: false,
    isGameCompleted: false,
    currentUser: null,
    gameTimer: GAME_TIME,

    // Actions
    initializeGame: ({ currentUser, gameType }) => {
      set((state) => {
        const dummyGame = getDummyGame({ currentUser, gameType });
        const startTime = getCurrentTime() + 3 * 1000;
        state.currentGame = {
          ...dummyGame,
          ...defaultGameProgress,
          startTime,
          gameStartTime: startTime,
        };
        state.currentUser = currentUser;
        state.isGameActive = true;
        state.isGameCompleted = false;
        state.gameTimer = GAME_TIME;
      });
    },

    endGame: () => {
      set((state) => {
        if (state.currentGame) {
          state.currentGame.gameEndTime = getCurrentTime();
          state.isGameActive = false;
          state.isGameCompleted = true;
        }
      });
    },

    submitAnswer: ({
      questionId,
      answer,
      timeSpent,
      maxScore,
    }: {
      questionId: string;
      answer: number;
      timeSpent: number;
      maxScore: number;
    }) => {
      set((state) => {
        const userId = userReader.id(state.currentUser);
        if (!state.currentGame) return;

        if (!_isNil(maxScore)) {
          updateLeaderboardOnSubmitAnswer({
            state,
            userId,
            score: maxScore,
          } as any);
          return;
        }

        const questions = gameReader.questions(
          state.currentGame,
        ) as DummyQuestion[];
        const question = _find(
          questions,
          (q: DummyQuestion) => q.id === questionId,
        ) as DummyQuestion;
        if (!question) return;

        const correctAnswer = parseInt(question.answers[0], 10);
        const isCorrect = _toString(answer) === _toString(correctAnswer);

        state.currentGame.userAnswers.push({
          questionId,
          userAnswer: answer,
          correctAnswer,
          isCorrect,
          timeSpent,
        });

        updateLeaderboardOnSubmitAnswer({
          state,
          userId,
          isCorrect,
        } as any);

        if (isCorrect) {
          state.currentGame.currentQuestionIndex++;
          state.currentGame.correctAnswers++;
        } else {
          state.currentGame.incorrectAnswers++;
        }
      });
    },

    handleAnswerSubmissionByBot: () => {
      set((state) => {
        const currentUserId = userReader.id(state.currentUser);
        const leaderboard = _get(state, ['currentGame', 'leaderBoard']);
        const currentUserLeaderboardEntry = _find(
          leaderboard,
          (entry) => entry?.userId === currentUserId,
        );

        const opponentUser = _find(
          leaderboard,
          (entry) => entry?.userId !== currentUserId,
        );

        if (_isEmpty(currentUserLeaderboardEntry) || _isEmpty(opponentUser)) {
          return;
        }

        const currentUserScore = _get(
          currentUserLeaderboardEntry,
          ['correct'],
          0,
        );
        const opponentUserScore = _get(opponentUser, ['correct'], 0);

        const gameType = gameReader.gameType(state.currentGame);

        if (gameType === GAME_TYPES.FLASH_ANZAN) {
          updateLeaderboardOnSubmitAnswer({
            state,
            userId: opponentUser?.userId,
            score: Math.max(30, currentUserScore - 30),
          });
          return;
        }

        if (opponentUserScore <= currentUserScore - 2) {
          const gameType = gameReader.gameType(state.currentGame);
          const score =
            gameType === GAME_TYPES.FLASH_ANZAN ? currentUserScore - 20 : null;
          const opponentUserId = opponentUser?.userId;
          updateLeaderboardOnSubmitAnswer({
            state,
            userId: opponentUserId,
            score,
          });
        }
      });
    },

    updateTimer: (timeRemaining: number) => {
      set((state) => {
        state.gameTimer = timeRemaining;
        if (state.currentGame) {
          state.currentGame.timeRemaining = timeRemaining;
        }
      });
    },

    addToLeaderboard: (entry: LeaderboardEntry) => {
      set((state) => {
        state.leaderboard.push(entry);
        state.leaderboard.sort((a, b) => b.score - a.score);

        // Keep only top 10
        if (state.leaderboard.length > 10) {
          state.leaderboard = state.leaderboard.slice(0, 10);
        }

        // Update user best score if this is better
        if (!state.userBestScore || entry.score > state.userBestScore.score) {
          state.userBestScore = entry;
        }
      });
    },

    resetGame: () => {
      set((state) => {
        state.currentGame = null;
        state.isGameActive = false;
        state.isGameCompleted = false;
        state.gameTimer = GAME_TIME;
      });
    },

    clearStore: () => {
      set((state) => {
        state.currentGame = null;
        state.leaderboard = [];
        state.userBestScore = null;
        state.isGameActive = false;
        state.isGameCompleted = false;
        state.gameTimer = GAME_TIME;
      });
    },
  })),
);

export default useExploreGameStore;
