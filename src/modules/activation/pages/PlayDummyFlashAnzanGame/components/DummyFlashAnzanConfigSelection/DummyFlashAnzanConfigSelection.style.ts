import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

const styles = StyleSheet.create({
  formContainer: {
    gap: 24,
  },
  mainContainer: {
    gap: 20,
  },
  configContainer: {
    flex: 1,
    width: '100%',
    maxWidth: 452,
    gap: 32,
  },
  countdownText: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: 'white',
    paddingTop: 8,
    textAlign: 'center',
  },
  conversionTopContainer: {
    height: 152,
    paddingTop: 24,
    paddingBottom: 32,
    width: '100%',
    maxWidth: 452,
    gap: 32,
    backgroundColor: dark.colors.primary,
  },
  conversionContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  conversionItem: {
    borderWidth: 1,
    borderColor: withOpacity(dark.colors.text, 0.6),
    borderRadius: 25,
    width: 132,
    height: 52,
    alignItems: 'center',
    justifyContent: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    width: '100%',
    height: 140,
    paddingHorizontal: 16,
  },
  playerInfo: {
    alignItems: 'center',
    gap: 10,
  },
  score: {
    fontSize: 32,
    fontFamily: 'Montserrat-700',
    color: withOpacity(dark.colors.text, 0.4),
    textAlign: 'center',
  },
  playerName: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    color: dark.colors.text,
  },
  conversionText: {
    fontFamily: 'Montserrat-800',
    fontSize: 12,
    color: withOpacity(dark.colors.text, 0.4),
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  conversionNameText: {
    fontSize: 10,
    fontFamily: 'Montserrat-600',
    color: withOpacity(dark.colors.text, 0.4),
    textAlign: 'center',
  },
  questionCount: {
    fontSize: 16,
    fontFamily: 'Montserrat-700',
    color: dark.colors.text,
    textAlign: 'center',
  },
  playerNameContainer: {
    gap: 4,
    paddingVertical: 6,
    paddingHorizontal: 4,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    borderWidth: 1,
    borderRadius: 4,
    borderColor: dark.colors.tertiary,
    width: 71,
  },
  playerNameAndRatingContainer: {
    width: 71,
    paddingVertical: 6,
    paddingHorizontal: 4,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 4,
  },
  userRatingText: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    lineHeight: 16,
    color: withOpacity(dark.colors.text, 0.6),
  },
});

export default styles;
