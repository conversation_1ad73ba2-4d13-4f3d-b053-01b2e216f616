import React, { useCallback, useEffect, useState } from 'react';
import {
  Dimensions,
  ImageBackground,
  SafeAreaView,
  StyleSheet,
  View,
} from 'react-native';
import * as Animatable from 'react-native-animatable';

import ActivationProgress from 'modules/activation/components/ActivationProgress';
import useGoBack from 'navigator/hooks/useGoBack';
import {
  Copilot<PERSON>rovider,
  CopilotStep,
  useCopilot,
  walkthroughable,
} from 'react-native-copilot';
import StartActivationModal from 'modules/activation/components/StartActivationModal';
import dark from 'core/constants/themes/dark';
import {
  ACTIVATION_GAME_CONFIG,
  EXPLORE_FEATURES,
  FEATURE_ACTIVATION_INFOS,
  WALKTHROUGH_STEPS_INFO_BY_FEATURE,
} from 'modules/activation/constants/explore';
import { router } from 'expo-router';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import KenKenPuzzleQuestion from 'shared/KenKenPuzzleQuestion/KenKenPuzzleQuestion';
import { DUMMY_KENKEN_PUZZLE_DATA } from 'modules/activation/dummyData/dummyPuzzleData';
import { KENKEN_PUZZLE_IMAGE } from 'modules/activation/constants/walkthroughImages';
import useExplorationStatusFromStore from 'store/useExploredFeaturesStore/useExplorationStatusFromStore';
import CustomTooltip, {
  withCustomProps,
} from '../../components/CustomWalkthroughTooltip';
import PlayFirstGameModal from '../../components/PlayFirstGameModal';
import styles from './ExploreKenKenPuzzle.style';
import KenKenPuzzleQuestionActions from '../../../../components/shared/KenKenPuzzleQuestion/components/KenKenPuzzleQuestionActions';
import KenKenPuzzleQuestionOptions from '../../../../components/shared/KenKenPuzzleQuestion/components/KenKenPuzzleQuestionOptions';

const KenKenPuzzleTooltip = withCustomProps(CustomTooltip, {
  color: dark.colors.puzzle.primary,
});

const WalkthroughableView = walkthroughable(View);

const ExploreKenKenPuzzle = React.memo(({ feature }: any) => {
  const { goBack } = useGoBack();
  const { markFeatureAsExplored } = useExplorationStatusFromStore();
  const KENKEN_PUZZLE_ACTIVATION_INFO = FEATURE_ACTIVATION_INFOS[feature];

  const STEPS_INFO = WALKTHROUGH_STEPS_INFO_BY_FEATURE[feature];

  const TOTAL_STEPS = STEPS_INFO.totalSteps;

  const [currentStep, setCurrentStep] = useState(0);

  const [
    playFirstKenKenPuzzleModalVisible,
    setPlayFirstKenKenPuzzleModalVisible,
  ] = useState(false);
  const [startActivationModalVisible, setStartActivationModalVisible] =
    useState(false);

  const { start, copilotEvents } = useCopilot();

  const onClickStep = useCallback((index: number) => {
    // if (index >= 1 && index <= TOTAL_STEPS) {
    //   setCurrentStep(index);
    // }
  }, []);

  const onPressSkip = useCallback(async () => {
    setStartActivationModalVisible(false);
    goBack();
    markFeatureAsExplored(feature);
  }, [feature, goBack, markFeatureAsExplored]);

  const onPressStartActivation = useCallback(() => {
    setStartActivationModalVisible(false);
    start();
  }, [start]);

  const onPressStartKenKenGame = useCallback(async () => {
    setPlayFirstKenKenPuzzleModalVisible(false);
    markFeatureAsExplored(feature);
    router.push(
      `/explore/puzzle-play?puzzleType=${PUZZLE_TYPES.KEN_KEN_PUZZLE}&feature=${EXPLORE_FEATURES.KEN_KEN_PUZZLE}`,
    );
  }, [feature, markFeatureAsExplored]);

  useEffect(() => {
    copilotEvents.on('stepChange', (step) => {
      setCurrentStep(step ? step.order : 0);
    });

    copilotEvents.on('stop', () => {
      setPlayFirstKenKenPuzzleModalVisible(true);
    });
  }, [copilotEvents]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setStartActivationModalVisible(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <KenKenPuzzleQuestion
        puzzle={DUMMY_KENKEN_PUZZLE_DATA as any}
        onSubmitPuzzle={NULL_FUN}
      >
        <View
          style={{
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <View style={{ height: 60 }} />

          <Animatable.View
            style={{
              height: 300,
              width: 300,
              justifyContent: 'center',
              alignItems: 'center',
            }}
            animation="fadeInDown"
            delay={200}
          >
            <ImageBackground
              source={{
                uri: KENKEN_PUZZLE_IMAGE,
              }}
              style={{
                width: 300,
                height: 300,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <CopilotStep
                key="grid"
                text="This is your play arena"
                order={1}
                name="Playground"
              >
                <WalkthroughableView
                  style={{
                    width: '100%',
                    height: '100%',
                  }}
                />
              </CopilotStep>
              <CopilotStep
                key="no-repeat"
                text="No repeating numbers in any row.For a 5x5 grid, you'll use numbers 1 through 5."
                order={2}
                name="No Repeating Numbers in Rows"
              >
                <WalkthroughableView
                  style={{
                    position: 'absolute',
                    top: 3,
                    left: 3,
                    width: '100%',
                    height: 60,
                  }}
                />
              </CopilotStep>
              <CopilotStep
                key="no-repeat"
                text="Similarly, No repeating numbers in any column. For a 5x5 grid, you'll use numbers 1 through 5."
                order={3}
                name="No Repeating Numbers in Columns"
              >
                <WalkthroughableView
                  style={{
                    position: 'absolute',
                    top: 3,
                    left: 3,
                    height: '100%',
                    width: 60,
                  }}
                />
              </CopilotStep>

              <CopilotStep
                key="cage-rule"
                text="The numbers in each cage must combine to produce the target number using the specified operation. For example, this cage says '+4', the numbers in that cage must give 4 after adding them."
                order={4}
                name="Cage Rule"
              >
                <WalkthroughableView
                  style={{
                    position: 'absolute',
                    top: 3,
                    left: 60,
                    width: 120,
                    height: 60,
                  }}
                />
              </CopilotStep>
              <CopilotStep
                key="cages"
                text="The grid is divided into 'cages' (the heavily outlined areas). Each cage has a target number and a mathematical operation."
                order={5}
                name="Cages"
              >
                <WalkthroughableView
                  style={{
                    position: 'absolute',
                    top: 3,
                    left: 3,
                    width: '100%',
                    height: 120,
                  }}
                />
              </CopilotStep>
              <CopilotStep
                key="cage-rule-example"
                text="For example, a cage with '4+' means the numbers inside must add up to 3. A cage with '3-' means the numbers must give a absolute difference of 3."
                order={6}
                name="Cage Rule Example"
              >
                <WalkthroughableView
                  style={{
                    position: 'absolute',
                    top: 3,
                    left: 60,
                    width: 120,
                    height: 120,
                  }}
                />
              </CopilotStep>
            </ImageBackground>
          </Animatable.View>

          <View style={{ height: 20 }} />
          <CopilotStep
            key="actions"
            text="Use these buttons to undo, redo, or clear as needed."
            order={7}
            name="Tool Kits"
          >
            <WalkthroughableView>
              <Animatable.View animation="fadeInUp" delay={200}>
                <KenKenPuzzleQuestionActions />
              </Animatable.View>
            </WalkthroughableView>
          </CopilotStep>
          <View style={{ height: 20 }} />
          <CopilotStep
            key="options"
            text="Pick a Number, tap a cell and complete the equations using BODMAS"
            order={8}
            name="Play with Numbers"
          >
            <WalkthroughableView>
              <Animatable.View animation="fadeInUp" delay={200}>
                <KenKenPuzzleQuestionOptions />
              </Animatable.View>
            </WalkthroughableView>
          </CopilotStep>
        </View>
      </KenKenPuzzleQuestion>
      <StartActivationModal
        onSkip={onPressSkip}
        activationConfig={KENKEN_PUZZLE_ACTIVATION_INFO}
        onStart={onPressStartActivation}
        isVisible={startActivationModalVisible}
      />
      <Animatable.View
        animation="slideInDown"
        duration={500}
        delay={500}
        style={[StyleSheet.absoluteFill, { zIndex: 1 }]}
      >
        <ActivationProgress
          currentStep={currentStep}
          stepsCount={TOTAL_STEPS}
          onClose={goBack}
          onClickStep={onClickStep}
        />
      </Animatable.View>
      <PlayFirstGameModal
        isVisible={playFirstKenKenPuzzleModalVisible}
        onClickPlayNow={onPressStartKenKenGame}
        gameConfig={ACTIVATION_GAME_CONFIG[feature] as any}
      />
    </SafeAreaView>
  );
});

const MARGIN = 16;
const WIDTH = Dimensions.get('window').width - 2 * MARGIN;

const ExploreKenKenPuzzleContainer = (props: any) => (
  <CopilotProvider
    tooltipComponent={KenKenPuzzleTooltip as any}
    tooltipStyle={{
      backgroundColor: 'transparent',
      padding: 0,
      borderRadius: 0,
      width: WIDTH,
      maxWidth: WIDTH,
      left: MARGIN,
    }}
    arrowColor="transparent"
    stopOnOutsideClick
    androidStatusBarVisible
    stepNumberComponent={() => null}
    backdropColor="rgba(0, 0, 0, 0.6)"
    labels={{
      previous: 'Previous',
      next: 'Next',
      skip: 'Skip',
      finish: 'Got it!',
    }}
  >
    <ExploreKenKenPuzzle {...props} />
  </CopilotProvider>
);

export default React.memo(ExploreKenKenPuzzleContainer);
