import dark from 'core/constants/themes/dark';
import { StyleSheet } from 'react-native';
import { withOpacity } from 'core/utils/colorUtils';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: dark.colors.background,
    gap: 32,
    width: '100%',
  },
  contentContainer: {
    gap: 8,
  },
  headerContainer: {
    height: 44,
  },
  titleText: {
    fontSize: 12,
    paddingHorizontal: 16,
    fontFamily: 'Montserrat-500',
    color: withOpacity(dark.colors.text, 0.4),
  },
});

export default styles;
