import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Dimensions, SafeAreaView, View } from 'react-native';
import {
  CopilotProvider,
  CopilotStep,
  useCopilot,
  walkthroughable,
} from 'react-native-copilot';
import dark from 'core/constants/themes/dark';
import PuzzleResultFooter from 'modules/puzzles/components/PuzzleResult/PuzzleResultFooter';
import PuzzleResultInfoCard from 'modules/puzzles/components/PuzzleResult/PuzzleResultInfoCard';
import CustomTooltip, {
  withCustomProps,
} from 'modules/activation/components/CustomWalkthroughTooltip';
import ActivationProgress from 'modules/activation/components/ActivationProgress';
import useGoBack from 'navigator/hooks/useGoBack';
import usePuzzleWalkthroughStore from 'modules/activation/store/usePuzzleWalkthroughStore';
import _isNil from 'lodash/isNil';
import { Redirect, router, useLocalSearchParams } from 'expo-router';
import { PUZZLE_TYPES } from '@/src/modules/puzzles/types/puzzleType';
import {
  EXPLORE_FEATURES,
  FINISHED_WALKTHROUGH_MODAL_INFO_BY_FEATURE,
  WALKTHROUGH_STEPS_INFO_BY_FEATURE,
} from 'modules/activation/constants/explore';
import FinishedWalkthroughModal from 'modules/activation/components/FinishedWalkthroughModal';
import MetricStatCard from 'shared/MetricStatCard';
import StaticCoinGainedMetric from 'shared/StaticCoinGainedMetric';
import styles from './PuzzleDummyResultPage.style';

const WalkthroughableView = walkthroughable(View);

const CrossMathPuzzleTooltip = withCustomProps(CustomTooltip, {
  color: dark.colors.puzzle.primary,
});

const PuzzleDummyResultPage = () => {
  const { start, copilotEvents } = useCopilot();

  const searchParams = useLocalSearchParams() ?? EMPTY_OBJECT;

  const {
    feature = EXPLORE_FEATURES.CROSS_MATH_PUZZLE,
    puzzleType = PUZZLE_TYPES.CROSS_MATH_PUZZLE,
  }: {
    feature: string;
    puzzleType: string;
  } = searchParams ?? EMPTY_OBJECT;

  const STEPS_INFO = WALKTHROUGH_STEPS_INFO_BY_FEATURE[feature];

  const TOTAL_STEPS = STEPS_INFO.totalSteps;
  const INITIAL_STEP = STEPS_INFO.preDummyPlay;

  const FINISHED_WALKTHROUGH_MODAL_INFO =
    FINISHED_WALKTHROUGH_MODAL_INFO_BY_FEATURE[feature];

  const [currentStep, setCurrentStep] = useState(INITIAL_STEP + 1);

  const [hasFinishedWalkthrough, setHasFinishedWalkthrough] = useState(false);

  const onFinishedModalClose = useCallback(() => {
    setHasFinishedWalkthrough(false);
  }, []);

  const { timeSpent, statikCoinsEarned } = usePuzzleWalkthroughStore();

  const navigateToHome = useCallback(() => {
    router.replace('/home');
  }, []);

  const today = new Date();
  const formattedTodayDate = today.toISOString().split('T')[0];

  const puzzleData = React.useMemo(
    () => ({
      id: 'dummy-puzzle',
      name: 'Dummy Puzzle',
      difficulty: 1,
      solvedBy: [],
      currentUserResult: {
        timeSpent,
        statikCoinsEarned,
        bestTime: timeSpent,
      },
      stats: {
        averageTime: timeSpent + 5000,
      },
      userStat: {
        bestTime: timeSpent,
      },
    }),
    [timeSpent, statikCoinsEarned],
  );

  const { goBack } = useGoBack();

  const onClickStep = useCallback((index: number) => {
    // if (index >= 1 && index <= TOTAL_STEPS) {
    //   setCurrentStep(index);
    // }
  }, []);

  useEffect(() => {
    const stepChangeHandler = (step: any) => {
      if (step) {
        setCurrentStep(INITIAL_STEP + step.order);
      }
    };
    copilotEvents.on('stepChange', stepChangeHandler);
    copilotEvents.on('stop', () => {
      setHasFinishedWalkthrough(true);
    });
    return () => {
      copilotEvents.off('stepChange', stepChangeHandler);
    };
  }, [copilotEvents]);

  const startWalkthroughRef = useRef(start);
  startWalkthroughRef.current = start;

  useEffect(() => {
    setTimeout(() => startWalkthroughRef.current(), 1000);
  }, []);

  if (_isNil(timeSpent) || timeSpent === 0) {
    return <Redirect href="/home" />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ActivationProgress
        currentStep={currentStep}
        stepsCount={TOTAL_STEPS}
        onClose={goBack}
        onClickStep={onClickStep}
      />

      <View style={{ flex: 1, justifyContent: 'center' }}>
        <CopilotStep
          key="Info"
          text="Here you can see your results, time you took to solve this puzzle"
          order={1}
          name="Info"
        >
          <WalkthroughableView>
            <PuzzleResultInfoCard
              puzzle={puzzleData as any}
              puzzleType={puzzleType}
            />
          </WalkthroughableView>
        </CopilotStep>
      </View>

      <View
        style={{
          flexDirection: 'row',
          gap: 13,
          width: '100%',
          paddingHorizontal: 16,
          paddingBottom: 30,
        }}
      >
        <View style={{ flex: 1 }}>
          <CopilotStep
            key="footer"
            text="This is your best time for this type of puzzle."
            order={2}
            name="Your Best Time"
          >
            <WalkthroughableView>
              <MetricStatCard
                label="YOUR BEST"
                value="00:15:35"
                backgroundColor={dark.colors.tertiary}
              />
            </WalkthroughableView>
          </CopilotStep>
        </View>

        <View style={{ flex: 1 }}>
          <CopilotStep
            key="footer"
            text="You can see how much XP you've Earned for solving this puzzle."
            order={3}
            name="XP Earned"
          >
            <WalkthroughableView>
              <StaticCoinGainedMetric coinsGained={15} />
            </WalkthroughableView>
          </CopilotStep>
        </View>
      </View>

      <CopilotStep
        key="footer"
        text="You can solve more puzzles by clicking on the play more button."
        order={4}
        name="Solve More"
      >
        <WalkthroughableView>
          <PuzzleResultFooter
            puzzle={puzzleData as any}
            date={formattedTodayDate}
          />
        </WalkthroughableView>
      </CopilotStep>

      <FinishedWalkthroughModal
        isVisible={hasFinishedWalkthrough}
        onFinished={navigateToHome}
        key="finishedWalkthroughModal"
        finishedConfig={FINISHED_WALKTHROUGH_MODAL_INFO}
        onBackdropPress={onFinishedModalClose}
      />
    </SafeAreaView>
  );
};

const MARGIN = 16;
const WIDTH = Dimensions.get('window').width - 2 * MARGIN;

const CrossMathPuzzleDummyResultPageContainer = () => (
  <CopilotProvider
    tooltipComponent={CrossMathPuzzleTooltip as any}
    tooltipStyle={{
      backgroundColor: 'transparent',
      padding: 0,
      borderRadius: 0,
      width: WIDTH,
      maxWidth: WIDTH,
      left: MARGIN,
    }}
    arrowColor="transparent"
    stopOnOutsideClick
    androidStatusBarVisible
    stepNumberComponent={() => null}
    backdropColor="rgba(0, 0, 0, 0.6)"
    labels={{
      previous: 'Previous',
      next: 'Next',
      skip: 'Skip',
      finish: 'Got it!',
    }}
  >
    <PuzzleDummyResultPage />
  </CopilotProvider>
);

export default React.memo(CrossMathPuzzleDummyResultPageContainer);
