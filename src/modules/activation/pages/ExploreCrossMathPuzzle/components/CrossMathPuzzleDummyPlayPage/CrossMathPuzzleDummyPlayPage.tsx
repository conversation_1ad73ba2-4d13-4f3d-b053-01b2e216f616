import React, { useCallback } from 'react';
import { SafeAreaView, View } from 'react-native';
import CrossMathPuzzleQuestion from 'shared/CrossMathPuzzleQuestion/CrossMathPuzzleQuestion';
import Header from 'shared/Header';
import usePuzzleWalkthroughStore from 'modules/activation/store/usePuzzleWalkthroughStore';
import { router } from 'expo-router';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import { EXPLORE_FEATURES } from 'modules/activation/constants/explore';
import { DUMMY_CROSS_MATH_PUZZLE_DATA } from 'modules/activation/dummyData/dummyPuzzleData';
import styles from './CrossMathPuzzleDummyPlayPage.style';

const CrossMathPuzzleDummyPlayPage = () => {
  const setResult = usePuzzleWalkthroughStore((state) => state.setResult);

  const onSubmitPuzzle = useCallback(
    ({ timeSpent }: { timeSpent: number }) => {
      setResult({ timeSpent });
      router.push(
        `/explore/puzzle-play-result?puzzleType=${PUZZLE_TYPES.CROSS_MATH_PUZZLE}&feature=${EXPLORE_FEATURES.CROSS_MATH_PUZZLE}`,
      );
    },
    [setResult],
  );

  return (
    <SafeAreaView style={styles.container}>
      <CrossMathPuzzleQuestion
        puzzle={DUMMY_CROSS_MATH_PUZZLE_DATA}
        onSubmitPuzzle={onSubmitPuzzle}
      >
        <Header
          renderTrailingComponent={() => <CrossMathPuzzleQuestion.Timer />}
        />
        <View style={{ gap: 50 }}>
          <View style={{ paddingTop: 20 }}>
            <CrossMathPuzzleQuestion.Grid />
          </View>
          <CrossMathPuzzleQuestion.Actions />
          <CrossMathPuzzleQuestion.Options />
        </View>
      </CrossMathPuzzleQuestion>
    </SafeAreaView>
  );
};

export default React.memo(CrossMathPuzzleDummyPlayPage);
