import React, { useMemo } from 'react';
import { View } from 'react-native';
import * as Animatable from 'react-native-animatable';
import ModuleActivationScreen from 'modules/activation/pages/ExploreModule/components/ModuleActivationScreen';
import SwipableScreens from 'shared/SwipableScreens';
import { SwipableScreen } from 'shared/SwipableScreens/types';
import AllFeaturesExploredScreen from 'modules/activation/pages/ExploreModule/components/AllFeaturesExploredScreen';
import Header from 'shared/Header';
import _filter from 'lodash/filter';
import useExploredFeaturesStore from '../../../../store/useExploredFeaturesStore';
import {
  EXPLORE_MODULE_FEATURES,
  EXPLORE_MODULES,
  MODULE_ACTIVATION_INFO,
} from '../../constants/explore';
import FeatureActivationScreen from './components/FeatureActivationScreen';

interface ExploreModuleProps {
  module: keyof typeof EXPLORE_MODULES;
}

const ExploreModule: React.FC<ExploreModuleProps> = ({ module }) => {
  const { getModuleProgress, isFeatureExplored } = useExploredFeaturesStore(
    (state) => ({
      getModuleProgress: state.getModuleProgress,
      isFeatureExplored: state.isFeatureExplored,
    }),
  );

  const moduleId = EXPLORE_MODULES[module];
  const moduleInfo = MODULE_ACTIVATION_INFO[moduleId];
  const moduleFeatures = EXPLORE_MODULE_FEATURES[moduleId] || EMPTY_ARRAY;
  const moduleProgress = getModuleProgress(moduleId);

  const unexploredFeatures = useMemo(
    () => _filter(moduleFeatures, (feature) => !isFeatureExplored(feature)),
    [moduleFeatures, isFeatureExplored],
  );

  const allFeaturesExplored = unexploredFeatures.length === 0;

  if (allFeaturesExplored) {
    return (
      <AllFeaturesExploredScreen
        moduleInfo={moduleInfo}
        moduleProgress={moduleProgress}
      />
    );
  }

  const screens: SwipableScreen[] = useMemo(
    () => [
      {
        id: 'module-details',
        component: (
          <ModuleActivationScreen
            moduleInfo={moduleInfo}
            moduleProgress={moduleProgress}
          />
        ),
        title: 'Module Details',
      },
      {
        id: 'unexplored-features',
        component: (
          <FeatureActivationScreen featuresToExplore={unexploredFeatures} />
        ),
        title: 'Unexplored Features',
        swipeable: true,
      },
    ],
    [moduleInfo, moduleProgress, unexploredFeatures],
  );

  return (
    <View style={{ flex: 1 }}>
      <Animatable.View animation="fadeIn" duration={500}>
        <Header />
      </Animatable.View>
      <Animatable.View animation="fadeInUp" delay={200} style={{ flex: 1 }}>
        <SwipableScreens
          screens={screens}
          initialScreenIndex={0}
          showIndicator
          indicatorPosition="bottom"
          enableHapticFeedback
          hapticIntensity="Medium"
        />
      </Animatable.View>
    </View>
  );
};

export default React.memo(ExploreModule);
