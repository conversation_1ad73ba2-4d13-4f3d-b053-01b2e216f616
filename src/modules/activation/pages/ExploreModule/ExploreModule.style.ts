import { StyleSheet } from 'react-native';
import dark from '../../../../core/constants/themes/dark';

const styles = StyleSheet.create({
  // All Features Explored Screen
  allExploredContainer: {
    flex: 1,
    backgroundColor: dark.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  allExploredContent: {
    alignItems: 'center',
    maxWidth: 400,
  },
  allExploredIcon: {
    fontSize: 80,
    marginBottom: 20,
  },
  allExploredTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: dark.colors.text,
    textAlign: 'center',
    marginBottom: 12,
  },
  allExploredSubtitle: {
    fontSize: 16,
    color: dark.colors.textDark,
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
  },

  // Module Details Screen
  detailsContainer: {
    flex: 1,
    backgroundColor: dark.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  detailsContent: {
    alignItems: 'center',
    maxWidth: 400,
  },
  moduleIconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  moduleIcon: {
    fontSize: 50,
  },
  moduleTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: dark.colors.text,
    textAlign: 'center',
    marginBottom: 16,
  },
  moduleDescription: {
    fontSize: 16,
    color: dark.colors.textDark,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  swipeHint: {
    fontSize: 14,
    color: dark.colors.placeholder,
    textAlign: 'center',
    marginTop: 20,
    fontStyle: 'italic',
  },

  // Unexplored Features Screen
  featuresContainer: {
    flex: 1,
    backgroundColor: dark.colors.background,
    padding: 20,
  },
  featuresHeader: {
    alignItems: 'center',
    marginBottom: 30,
    paddingTop: 40,
  },
  featuresTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: dark.colors.text,
    marginBottom: 8,
  },
  featuresSubtitle: {
    fontSize: 16,
    color: dark.colors.textDark,
    textAlign: 'center',
  },
  featuresList: {
    width: '100%',
    height: 120,
    minHeight: 120,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: dark.colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    maxHeight: 100,
    borderWidth: 1,
    borderColor: dark.colors.border,
  },
  featureIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: dark.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureIcon: {
    fontSize: 24,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: dark.colors.text,
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: dark.colors.textDark,
    lineHeight: 20,
  },
  featureStatus: {
    marginLeft: 12,
  },
  newBadge: {
    fontSize: 12,
    fontWeight: 'bold',
    color: dark.colors.success,
    backgroundColor: dark.colors.darkGreen,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    overflow: 'hidden',
  },
  featuresFooter: {
    paddingTop: 20,
    paddingBottom: 10,
  },

  // Common Progress Styles
  progressContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 20,
  },
  progressLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: dark.colors.text,
    marginBottom: 8,
  },
  progressText: {
    fontSize: 14,
    color: dark.colors.textDark,
    marginBottom: 12,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: dark.colors.border,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },

  // Common Button Styles
  homeButton: {
    width: '100%',
    maxWidth: 300,
  },
});

export default styles;
