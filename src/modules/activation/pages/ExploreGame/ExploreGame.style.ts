import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: dark.colors.background,
  },
  gameContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    width: '100%',
  },
  loadingText: {
    color: dark.colors.text,
    fontSize: 18,
    textAlign: 'center',
    marginTop: 100,
  },
  startGameContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  startGameTitle: {
    color: dark.colors.text,
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  startGameDescription: {
    color: dark.colors.text,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  startGameInstructions: {
    color: dark.colors.text,
    fontSize: 14,
    textAlign: 'left',
    lineHeight: 20,
    backgroundColor: dark.colors.background,
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: dark.colors.primary,
  },
});

export default styles;
