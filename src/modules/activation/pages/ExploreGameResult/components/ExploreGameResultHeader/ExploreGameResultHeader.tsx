import styles from 'modules/game/pages/GameResultPage/components/GameResultHeader/GameResultHeader.style';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import { closePopover } from 'molecules/Popover/Popover';
import { ICON_TYPES } from 'atoms/Icon';
import dark from 'core/constants/themes/dark';
import { Animated, View } from 'react-native';
import React, { useCallback } from 'react';
import tie from 'assets/images/game/tie.png';
import victory from 'assets/images/game/victory.png';
import defeat from 'assets/images/game/defeat.png';
import useExploreGameResult from 'modules/activation/hooks/useExploreGameResult';

const ExploreGameResultHeader = ({ game }) => {
  const { isCurrentPlayerWinner, isMatchTied } = useExploreGameResult({ game });

  const getImageIcon = useCallback(() => {
    if (isMatchTied) {
      return tie;
    }
    if (isCurrentPlayerWinner) {
      return victory;
    }
    return defeat;
  }, [isCurrentPlayerWinner, isMatchTied]);

  return (
    <View style={[styles.container]}>
      <InteractiveSecondaryButton
        testID="close-popover"
        disabled
        onPress={closePopover}
        iconConfig={{
          type: ICON_TYPES.MATERIAL_ICONS,
          name: 'close',
          size: 16,
          color: dark.colors.textLight,
        }}
        buttonContainerStyle={{ width: 40, height: 40 }}
        buttonContentStyle={{ paddingHorizontal: 0, paddingVertical: 0 }}
      />
      <View style={[styles.imageContainer, { width: 180 }]}>
        <Animated.Image
          source={getImageIcon()}
          style={styles.image}
          resizeMode="contain"
        />
      </View>
      <InteractiveSecondaryButton
        onPress={NULL_FUN}
        disabled
        iconConfig={{
          type: ICON_TYPES.ENTYPO,
          name: 'share',
          size: 16,
          color: dark.colors.textLight,
        }}
        buttonContainerStyle={{ width: 40, height: 40 }}
        buttonContentStyle={{ paddingHorizontal: 0, paddingVertical: 0 }}
      />
    </View>
  );
};

export default React.memo(ExploreGameResultHeader);
