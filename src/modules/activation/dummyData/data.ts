import { QUESTION_CATEGORIES } from 'core/constants/questionCategories';
import { QuestionType } from 'shared/MCQWordProblemQuestion/types/types';

export const dummyDMASQuestion = {
  id: 'sampleQuestion',
  expression: [24, '+', 26],
  answers: [50],
  category: QUESTION_CATEGORIES.ADD_AND_SUBSTRACT,
};

export const dummyDMASAbilityQuestion = {
  id: 'sampleQuestion-1',
  category: QUESTION_CATEGORIES.HCF,
  expression: '63 HCF 8',
  answers: [1],
  questionType: QuestionType.FillInTheBlanks,
  rating: 1202,
  presetIdentifier: 'HCF_2,1',
};
