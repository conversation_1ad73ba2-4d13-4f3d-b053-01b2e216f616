import { GAME_MODES, GAME_STATUS } from 'modules/game/constants/game';
import { GAME_CATEGORIES, GAME_TYPES } from 'core/constants/gameTypes';
import {
  DUMMY_PLAYER_DATA,
  getDefaultLeaderboard,
  getPlayerFromUser,
} from './dummyBlitzGame';

export const DUMMY_FLASH_ANZAN_GAME = {
  _id: 'sampleGame',
  gameCategory: GAME_CATEGORIES.MEMORY,
  gameMode: GAME_MODES.ONLINE_SEARCH,
  gameType: GAME_TYPES.FLASH_ANZAN,
  gameStatus: GAME_STATUS.STARTED,
  questions: [],
  players: [],
  config: {
    timeLimit: 30,
    numPlayers: 2,
  },
  leaderBoard: [],
};

export const getDummyFlashAnzanGame = ({
  currentUser,
}: {
  currentUser: any;
}) => {
  const players = [
    getPlayerFromUser(currentUser),
    getPlayerFromUser(DUMMY_PLAYER_DATA),
  ];

  const leaderboard = [
    {
      ...getDefaultLeaderboard(currentUser),
      rank: 1,
      isWinner: true,
      ratingChange: 24,
      statikCoinsEarned: 10,
    },
    getDefaultLeaderboard(DUMMY_PLAYER_DATA),
  ];

  return {
    ...DUMMY_FLASH_ANZAN_GAME,
    players,
    leaderBoard: leaderboard,
  };
};
