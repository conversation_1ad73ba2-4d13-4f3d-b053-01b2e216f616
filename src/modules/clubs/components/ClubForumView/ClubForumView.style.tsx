import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 30,
  },
  addIconContainer: {
    width: 30,
    height: 30,
    borderRadius: 15,
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    top: 10,
    right: 15,
    backgroundColor: dark.colors.secondary,
  },
  threadContainer: {
    padding: 15,
    backgroundColor: dark.colors.background,
    borderRadius: 10,
    marginBottom: 15,
  },
  threadText: {
    fontFamily: 'Montserrat-500',
    fontSize: 16,
    color: 'white',
  },
  input: {
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    padding: 10,
    borderRadius: 4,
    marginBottom: 10,
  },
  replyContainer: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: dark.colors.tertiary,
  },
  replyText: {
    fontSize: 14,
    fontFamily: 'Montserrat-500',
  },
});

export default styles;
