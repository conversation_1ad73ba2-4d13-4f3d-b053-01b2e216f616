import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  popover: {
    width: 180,
    // height: 60,
    position: 'absolute',
    // top: 52,
    gap: 15,
    right: -2,
    justifyContent: 'space-between',
    backgroundColor: dark.colors.primary,
    borderRadius: 6,
    paddingVertical: 12,
  },
  popoverContent: {
    flexDirection: 'row',
    alignItems: 'center',
    // marginVertical: 10,
    paddingHorizontal: 16,
  },
  popoverText: {
    color: 'white',
    fontSize: 14,
  },
  deleteText: {
    color: '#FF6A6A',
    fontSize: 14,
  },
});

export default styles;
