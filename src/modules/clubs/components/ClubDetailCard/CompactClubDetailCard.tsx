import React, { useCallback } from 'react';
import { ClubInfo } from 'modules/clubs/hooks/queries/useGetClubById';
import { Image, Text, View } from 'react-native';
import styles from 'modules/clubs/pages/ClubDetailView/ClubDetailView.style';
import Header from 'shared/Header';
import _map from 'lodash/map';
import PrimaryButton from 'atoms/PrimaryButton';
import dark from 'core/constants/themes/dark';
import { router } from 'expo-router';
import { clubReader } from 'core/readers/clubReader';
import ClubMenuIconButton from '../ClubMenuIconButton';

const CompactClubDetailCard = ({
  clubInfo,
  clubDetailsList,
  isJoiningClub,
  joinClub,
}: {
  clubInfo: ClubInfo;
  clubDetailsList: any;
  isJoiningClub: boolean;
  joinClub: any;
}) => {
  const navigateToAdminView = useCallback(() => {
    router.push(`/clubs/${clubInfo.id}/admin`);
  }, [clubInfo.id]);

  const renderTrailingComponent = useCallback(() => {
    if (
      !clubReader.isClubMember(clubInfo) &&
      !clubReader.hasRequestedToJoin(clubInfo)
    ) {
      return (
        <PrimaryButton
          buttonStyle={styles.joinButtonStyle}
          labelStyle={[
            styles.joinButtonText,
            isJoiningClub && { color: dark.colors.textDark },
          ]}
          label="JOIN"
          onPress={joinClub}
        />
      );
    }
    if (clubReader.isAdmin(clubInfo)) {
      return <ClubMenuIconButton clubId={clubReader.id(clubInfo)} />;
    }
    return null;
  }, [joinClub, isJoiningClub, clubInfo]);

  return (
    <View style={{ gap: 5 }}>
      <View style={styles.topBarContainer}>
        <View style={styles.backgroundImage}>
          {clubReader.bannerImage(clubInfo) ? (
            <Image
              source={clubReader.bannerImage(clubInfo)}
              style={{
                width: '100%',
                height: '100%',
                position: 'absolute',
              }}
            />
          ) : null}
          <Header
            title=""
            isTransparentBg
            renderTrailingComponent={renderTrailingComponent}
          />
        </View>
        <View style={styles.clubImageContainer}>
          <View style={styles.clubImageView}>
            {clubReader.logoImage(clubInfo) ? (
              <Image
                source={clubReader.logoImage(clubInfo)}
                style={{ width: '100%', height: '100%' }}
              />
            ) : null}
          </View>
        </View>
      </View>

      <Text style={[styles.clubName]}>{clubReader.name(clubInfo)}</Text>

      <Text style={styles.clubDesc}>{clubReader.description(clubInfo)}</Text>

      <View
        style={{
          justifyContent: 'space-between',
          flexDirection: 'row',
          marginTop: 7,
          paddingHorizontal: 16,
        }}
      >
        {_map(clubDetailsList, (item) => (
          <View style={styles.detailedInfoContainer}>
            <View style={styles.detailIconContainer} />
            <View style={styles.detailTextContainer}>
              <Text style={styles.detailValueText}>{item?.value}</Text>
              <Text style={styles.detailLabelText}>{item?.label}</Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

export default React.memo(CompactClubDetailCard);
