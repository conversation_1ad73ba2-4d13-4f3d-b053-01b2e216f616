import React from 'react';
import { ClubInfo } from 'modules/clubs/hooks/queries/useGetClubById';
import { Image, Text, View } from 'react-native';
import styles from 'modules/clubs/pages/ClubDetailView/ClubDetailView.style';
import { clubReader } from 'core/readers/clubReader';
import ClubMenuIconButton from 'modules/clubs/components/ClubMenuIconButton';

const ExpandedClubDetailCard = ({ clubInfo }: { clubInfo: ClubInfo }) => (
  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
    <View style={{ flexDirection: 'row', gap: 10 }}>
      <Image
        source={clubReader.logoImage(clubInfo)}
        style={styles.clubImageView}
      />

      <View
        style={{
          alignItems: 'flex-start',
          gap: 5,
          justifyContent: 'flex-start',
        }}
      >
        <Text style={[styles.clubName, { textAlign: 'left', paddingTop: 5 }]}>
          {clubReader.name(clubInfo)}
        </Text>

        <Text
          style={[styles.clubDesc, { textAlign: 'left', paddingHorizontal: 0 }]}
        >
          {clubReader.description(clubInfo)}
        </Text>
      </View>
    </View>
    {clubReader.isAdmin(clubInfo) && (
      <ClubMenuIconButton clubId={clubReader.id(clubInfo)} />
    )}
  </View>
);

export default React.memo(ExpandedClubDetailCard);
