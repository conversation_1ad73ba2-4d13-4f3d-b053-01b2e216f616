import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  buttonStyle: {
    height: 40,
    width: 130,
    borderRadius: 20,
    // backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: dark.colors.secondary,
  },
  noAccessText: {
    fontFamily: 'Montserrat-500',
    textAlign: 'center',
    color: 'white',
    fontSize: 13,
  },
  innerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
    gap: 20,
  },
});

export default styles;
