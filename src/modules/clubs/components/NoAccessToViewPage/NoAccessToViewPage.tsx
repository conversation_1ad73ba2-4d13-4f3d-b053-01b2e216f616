import React, { useCallback } from 'react';
import { Text, View } from 'react-native';
import PrimaryButton from '@/src/components/atoms/PrimaryButton';
import dark from '@/src/core/constants/themes/dark';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import styles from './NoAccessToViewPage.style';
import useJoinClub from '../../hooks/mutations/useJoinClub';
import { ClubInfo } from '../../hooks/queries/useGetClubById';

const NoAccessToViewPage = ({
  clubInfo,
  refetch,
}: {
  clubInfo: ClubInfo;
  refetch: Function;
}) => {
  const { joinClub: joinClubQuery, isJoiningClub } = useJoinClub();

  const joinClub = useCallback(async () => {
    try {
      if (isJoiningClub) {
        return;
      }
      await joinClubQuery({
        clubId: clubInfo?.id,
      });
      refetch();
      showToast({
        type: TOAST_TYPE.SUCCESS,
        description: 'Joined the club Successfully',
      });
    } catch (e) {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: 'Something went wrong while joining club',
      });
    }
  }, [clubInfo?.id, refetch]);

  return (
    <View
      style={{
        marginVertical: 10,
        flex: 1,
        paddingVertical: 30,
        borderTopWidth: 1,
        borderColor: dark.colors.tertiary,
      }}
    >
      <View style={styles.innerContainer}>
        <Text style={styles.noAccessText}>
          You don't have the access to view this only club clubMembers can view
          this
        </Text>
        {!clubInfo?.hasRequestedToJoin ? (
          <PrimaryButton
            label="Join Club"
            onPress={joinClub}
            buttonStyle={styles.buttonStyle}
          />
        ) : (
          <Text style={[styles.noAccessText, { fontSize: 10, marginTop: 5 }]}>
            You have already requested to join this club
          </Text>
        )}
      </View>
    </View>
  );
};

export default React.memo(NoAccessToViewPage);
