import React from 'react';
import { View, Text } from 'react-native';
import { Forum } from '../../hooks/queries/useGetForums';
import styles from './ForumItemView.style';

const ForumItemView = ({ forum }: { forum: Forum }) => (
  <View style={styles.container} key={forum.id}>
    <Text style={styles.title}>{forum.title}</Text>
    {/* <Text style={styles.description}>{forum.description}</Text> */}
  </View>
);

export default React.memo(ForumItemView);
