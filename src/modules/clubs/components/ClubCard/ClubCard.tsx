import { ImageBackground, Text, TouchableOpacity, View } from 'react-native';
import React, { useCallback } from 'react';
import { router } from 'expo-router';
import { clubReader } from 'core/readers/clubReader';
import { ClubInfo } from '../../hooks/queries/useGetClubById';
import styles from './ClubCard.style';

const ClubCard = ({ club }: { club: ClubInfo }) => {
  const navigateToClubDetail = useCallback(() => {
    router.push(`/clubs/${club.id}`);
  }, [club.id]);

  return (
    <TouchableOpacity onPress={navigateToClubDetail}>
      <ImageBackground source={club.bannerImage} style={styles.container}>
        <View style={styles.visibilityInfo}>
          <Text style={styles.visibilityText}>
            {clubReader.visibility(club)}
          </Text>
        </View>
        <View style={styles.infoText}>
          <Text style={styles.clubNameText}>{clubReader.name(club)}</Text>
          <Text style={styles.clubMemberCountText}>{club.membersCount}</Text>
        </View>
      </ImageBackground>
    </TouchableOpacity>
  );
};

export default React.memo(ClubCard);
