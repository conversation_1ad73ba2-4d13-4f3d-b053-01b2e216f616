import { SelectOption } from '@/src/components/shared/Form/types/formTypes';
import { FORM_INPUT_TYPES } from '@/src/core/constants/forms';
import _get from 'lodash/get';
import _isEqual from 'lodash/isEqual';
import dark from '@/src/core/constants/themes/dark';
import { clubReader } from 'core/readers/clubReader';
import styles from '../pages/CreateClub/CreateClub.style';

export const clubOptions = [
  { label: 'Public', value: 'PUBLIC', description: 'Anyone can join the club' },
  {
    label: 'Private',
    value: 'PRIVATE',
    description: '<PERSON><PERSON> must approve requests',
  },
] as SelectOption[];

export const permissionOptions = [
  {
    label: 'Public Content',
    value: 'publicContent',
    description: 'Visible to non-clubMembers',
  },
  {
    label: 'Show Members list',
    value: 'showMembers',
    description: 'Non-clubMembers can view clubMembers',
  },
  {
    label: 'Create forum',
    value: 'createForum',
    description: 'Anyone can create topics',
  },
] as SelectOption[];

const CLUB_CREATION_FIELD_KEYS = {
  CLUB_NAME: 'name',
  CLUB_DESCRIPTION: 'description',
  CLUB_VISIBILITY: 'visibility',
};

const EVENT_CREATION_FIELD_KEYS = {
  EVENT_CATEGORY: 'clubEventType',
  EVENT_TITLE: 'title',
  EVENT_DESCRIPTION: 'description',
  EVENT_START_TIME: 'startTime',
  EVENT_END_TIME: 'endTime',
  MIN_RATING: 'minRating',
  MAX_RATING: 'maxRating',
  EVENT_VISIBILITY: 'visibility',
  IS_RATED_EVENT: 'isRatedEvent',
  TIME_LIMIT: 'timeLimit',
  NUM_OF_PLAYERS: 'numPlayers',
  GAME_TYPE: 'gameType',
  QUESTION_TAGS: 'questionTags',
  DIFFICULTY_LEVEL: 'difficultyLevel',
  MAX_TIME_PER_QUESTION: 'maxTimePerQuestion',
  OPEN_TO_ALL: 'openToAll',
  RATING_RANGE: 'ratingRange',
};

const CLUB_ANNOUNCEMENT_CREATION_FIELD_KEYS = {
  ANNOUNCEMENT_TITLE: 'title',
  ANNOUNCEMENT_DESCRIPTION: 'content',
};

const FORUM_THREAD_CREATION_FIELD_KEYS = {
  THREAD_TITLE: 'title',
  THREAD_DESCRIPTION: 'content',
};

export const EVENT_CREATION_TEXT_INPUT_FORM_FIELD = [
  {
    label: 'Club Name ',
    multiline: false,
    key: 'EVENT_TITLE_TEXT_INPUT',
    labelKey: 'title',
    noOfLines: 1,
  },
  {
    label: 'Club Description ',
    multiline: true,
    key: 'CLUB_DESCRIPTION_TEXT_INPUT',
    labelKey: 'description',
    noOfLines: 3,
  },
];

export const EVENT_TYPES_LIST = [
  { label: '80 in 8', value: 'CONTEST_80_IN_8' },
  { label: 'Swiss Style Tournament', value: 'SUMDAY_SHOWDOWN' },
];

export const GAME_TYPES_LIST = [
  { label: 'DMAS', value: 'PLAY_ONLINE' },
  // { label: "Fastest Finger First", value: 'FASTEST_FINGER_FIRST' },
];

export const TOURNAMENT_TYPES_LIST = [
  { label: 'Only for Club Members', value: 'PRIVATE' },
  { label: 'Public', value: 'PUBLIC' },
];

const TEXT_INPUT_STYLE = {
  style: styles.textFormField,
  focusedInputStyle: styles.focusedInputField,
  innerContainerStyle: styles.innerContainerStyle,
  mainContainerStyle: styles.mainContainerStyle,
  labelStyle: styles.labelStyle,
};

export const getClubCreationInfoWithDefaultValues = (defaultValues: any) => [
  {
    key: CLUB_CREATION_FIELD_KEYS.CLUB_NAME,
    label: 'Club Name',
    name: 'Club Name',
    required: true,
    defaultValue: clubReader.name(defaultValues) ?? '',
    visible: (e: any) => true,
    rules: {
      minLength: 5,
      maxLength: 30,
    },
    type: FORM_INPUT_TYPES.TEXT,
    ...TEXT_INPUT_STYLE,
  },
  {
    key: CLUB_CREATION_FIELD_KEYS.CLUB_DESCRIPTION,
    label: 'Club Description',
    name: 'Club Description',
    required: true,
    defaultValue: clubReader.description(defaultValues) ?? '',
    visible: (e: any) => true,
    rules: {
      minLength: 10,
      maxLength: 150,
    },
    ...TEXT_INPUT_STYLE,
    type: FORM_INPUT_TYPES.TEXTAREA,
    multiline: true,
    numberOfLines: 4,
  },
  {
    key: CLUB_CREATION_FIELD_KEYS.CLUB_VISIBILITY,
    label: 'Club Type',
    name: 'Club Type',
    required: true,
    defaultValue: clubReader.visibility(defaultValues) ?? clubOptions[0].value,
    visible: (e: any) => true,
    rules: {},
    ...TEXT_INPUT_STYLE,
    options: clubOptions,
    type: FORM_INPUT_TYPES.SINGLE_SELECT_OPTION,
  },
];

export const EVENT_CREATION_FORM_FIELDS = [
  {
    key: EVENT_CREATION_FIELD_KEYS.EVENT_CATEGORY,
    label: 'Event Category',
    name: 'Event Category',
    required: false,
    search: false,
    defaultValue: EVENT_TYPES_LIST[0].value,
    visible: (e: any) => true,
    data: EVENT_TYPES_LIST,
    type: FORM_INPUT_TYPES.DROPDOWN,
    ...TEXT_INPUT_STYLE,
  },
  {
    key: EVENT_CREATION_FIELD_KEYS.EVENT_VISIBILITY,
    label: 'Event Visibility',
    name: 'Event Visibility',
    required: false,
    search: false,
    defaultValue: TOURNAMENT_TYPES_LIST[0].value,
    visible: (e: any) => true,
    data: TOURNAMENT_TYPES_LIST,
    type: FORM_INPUT_TYPES.DROPDOWN,
    ...TEXT_INPUT_STYLE,
  },
  {
    key: EVENT_CREATION_FIELD_KEYS.EVENT_TITLE,
    label: 'Event Title',
    name: 'Event Title',
    required: true,
    defaultValue: '',
    visible: (e: any) => true,
    rules: {
      minLength: 5,
      maxLength: 50,
    },
    ...TEXT_INPUT_STYLE,
    placeholder: 'Event Title',
    placeholderTextColor: dark.colors.textDark,
    type: FORM_INPUT_TYPES.TEXT,
  },
  {
    key: EVENT_CREATION_FIELD_KEYS.EVENT_DESCRIPTION,
    label: 'Event Description',
    name: 'Event Description',
    required: true,
    defaultValue: '',
    visible: (e: any) => true,
    rules: {
      minLength: 10,
      maxLength: 150,
    },
    ...TEXT_INPUT_STYLE,
    type: FORM_INPUT_TYPES.TEXTAREA,
    multiline: true,
    noOfLines: 4,
    placeholder: 'Event Description',
    placeholderTextColor: dark.colors.textDark,
  },
  {
    key: EVENT_CREATION_FIELD_KEYS.EVENT_START_TIME,
    label: 'Event Start Time',
    name: 'Event StartTime',
    required: false,
    defaultValue: '',
    visible: (e: any) => true,
    ...TEXT_INPUT_STYLE,
    type: FORM_INPUT_TYPES.DATE_TIME_PICKER,
  },
  // {
  //   key: EVENT_CREATION_FIELD_KEYS.EVENT_END_TIME,
  //   label: 'Event End Time',
  //   name: 'Event End Time',
  //   required: false,
  //   defaultValue: '',
  //   visible: (state: any) =>
  //     _isEqual(
  //       _get(state, [EVENT_CREATION_FIELD_KEYS.EVENT_CATEGORY], ''),
  //       EVENT_TYPES_LIST[0].value,
  //     ),
  //   ...TEXT_INPUT_STYLE,
  //   type: FORM_INPUT_TYPES.DATE_TIME_PICKER,
  //   multiline: true,
  //   noOfLines: 4,
  //   placeholder: 'Event Description',
  //   placeholderTextColor: dark.colors.textDark,
  // },
  {
    key: EVENT_CREATION_FIELD_KEYS.IS_RATED_EVENT,
    label: 'Is Rated Event',
    name: 'Is Rated Event',
    required: false,
    defaultValue: false,
    visible: (state: any) =>
      !_isEqual(
        _get(state, [EVENT_CREATION_FIELD_KEYS.EVENT_CATEGORY], ''),
        EVENT_TYPES_LIST[0].value,
      ),
    ...TEXT_INPUT_STYLE,
    type: FORM_INPUT_TYPES.TOGGLE_BUTTON,
  },
  {
    key: EVENT_CREATION_FIELD_KEYS.TIME_LIMIT,
    label: 'Game Duration (in minutes)',
    name: 'Game Duration (in minutes)',
    required: false,
    defaultValue: 1,
    visible: (state: any) =>
      !_isEqual(
        _get(state, [EVENT_CREATION_FIELD_KEYS.EVENT_CATEGORY], ''),
        EVENT_TYPES_LIST[0].value,
      ),
    type: FORM_INPUT_TYPES.NUMBER_INPUT,
    mainContainerStyle: { marginVertical: 12 },
    rules: {
      minValue: 1,
      maxValue: 5,
    },
    additional: {
      incrementBy: 1,
    },
  },
  {
    key: EVENT_CREATION_FIELD_KEYS.GAME_TYPE,
    label: 'Game Type',
    name: 'Game Type',
    required: false,
    search: false,
    defaultValue: GAME_TYPES_LIST[0].value,
    visible: (state: any) =>
      !_isEqual(
        _get(state, [EVENT_CREATION_FIELD_KEYS.EVENT_CATEGORY], ''),
        EVENT_TYPES_LIST[0].value,
      ),
    data: GAME_TYPES_LIST,
    type: FORM_INPUT_TYPES.DROPDOWN,
    ...TEXT_INPUT_STYLE,
  },
  {
    key: EVENT_CREATION_FIELD_KEYS.OPEN_TO_ALL,
    label: 'Open to all',
    name: 'Open to All',
    required: false,
    defaultValue: true,
    visible: (state: any) =>
      !_isEqual(
        _get(state, [EVENT_CREATION_FIELD_KEYS.EVENT_CATEGORY], ''),
        EVENT_TYPES_LIST[0].value,
      ),
    ...TEXT_INPUT_STYLE,
    type: FORM_INPUT_TYPES.TOGGLE_BUTTON,
  },
  {
    key: EVENT_CREATION_FIELD_KEYS.RATING_RANGE,
    minValueKey: EVENT_CREATION_FIELD_KEYS.MIN_RATING,
    maxValueKey: EVENT_CREATION_FIELD_KEYS.MAX_RATING,
    placeholder: 'Rating',
    label: 'Users Rating Range',
    name: 'Rating Range',
    required: true,
    defaultValue: false,
    visible: (state: any) =>
      !_get(state, [EVENT_CREATION_FIELD_KEYS.OPEN_TO_ALL], true),
    ...TEXT_INPUT_STYLE,
    type: FORM_INPUT_TYPES.RANGE_TEXT_INPUT,
  },
];

export const ANNOUNCEMENT_CREATION_FORM_FIELDS = [
  {
    key: CLUB_ANNOUNCEMENT_CREATION_FIELD_KEYS.ANNOUNCEMENT_TITLE,
    label: 'Title',
    name: 'Title',
    required: true,
    defaultValue: '',
    visible: (e: any) => true,
    rules: {
      minLength: 5,
      maxLength: 50,
    },
    ...TEXT_INPUT_STYLE,
    type: FORM_INPUT_TYPES.TEXT,
    placeholder: 'Title for the Announcement',
    placeholderTextColor: dark.colors.textDark,
  },
  {
    key: CLUB_ANNOUNCEMENT_CREATION_FIELD_KEYS.ANNOUNCEMENT_DESCRIPTION,
    label: 'Description',
    name: 'Description',
    required: true,
    defaultValue: '',
    visible: (e: any) => true,
    rules: {
      minLength: 10,
      maxLength: 150,
    },
    ...TEXT_INPUT_STYLE,
    type: FORM_INPUT_TYPES.TEXTAREA,
    multiline: true,
    noOfLines: 5,
    placeholder: 'Description for the Announcement',
    placeholderTextColor: dark.colors.textDark,
  },
];

export const CREATE_THREAD_FORM_FIELDS = [
  {
    key: FORUM_THREAD_CREATION_FIELD_KEYS.THREAD_TITLE,
    label: 'Title',
    name: 'Title',
    required: true,
    defaultValue: '',
    visible: (e: any) => true,
    rules: {
      minLength: 5,
      maxLength: 50,
    },
    ...TEXT_INPUT_STYLE,
    type: FORM_INPUT_TYPES.TEXT,
    placeholder: 'Topic of the thread',
    placeholderTextColor: dark.colors.textDark,
  },
  {
    key: FORUM_THREAD_CREATION_FIELD_KEYS.THREAD_DESCRIPTION,
    label: 'Description',
    name: 'Description',
    required: true,
    defaultValue: '',
    visible: (e: any) => true,
    rules: {
      minLength: 10,
      maxLength: 150,
    },
    ...TEXT_INPUT_STYLE,
    type: FORM_INPUT_TYPES.TEXTAREA,
    multiline: true,
    noOfLines: 5,
    placeholder: 'Description for the Thread',
    placeholderTextColor: dark.colors.textDark,
  },
];
