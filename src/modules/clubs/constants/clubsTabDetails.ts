export const TAB_KEYS = {
  EVENTS: 'EVENTS',
  CHAT_ROOM: 'CHAT_ROOM',
  ANNOUNCEMENTS: 'ANNOUNCEMENTS',
  FORUM: 'FORUM',
  LEADERBOARD: 'LEADERBOARD',
};

export const TAB_LIST = [
  { key: TAB_KEYS.EVENTS, title: 'Events' },
  { key: TAB_KEYS.CHAT_ROOM, title: 'Chat Room' },
  { key: TAB_KEYS.ANNOUNCEMENTS, title: 'Announcements' },
  { key: TAB_KEYS.FORUM, title: 'Threads' },
  { key: TAB_KEYS.LEADERBOARD, title: 'Leaderboard' },
];

export const COMPACT_VIEW_TAB_LIST = [
  { key: TAB_KEYS.EVENTS, title: 'Events' },
  { key: TAB_KEYS.ANNOUNCEMENTS, title: 'Announcements' },
  { key: TAB_KEYS.FORUM, title: 'Threads' },
  { key: TAB_KEYS.LEADERBOARD, title: 'Leaderboard' },
];

export enum CLUB_MEMBER_ROLE {
  ADMIN = 'ADMIN',
  MEMBER = 'MEMBER',
  OWNER = 'OWNER',
}

export enum CLUB_MEMBERSHIP_STATUS {
  ACCEPTED = 'ACCEPTED',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
}

export const ADMIN_TAB_KEYS = {
  MEMBERS: 'MEMBERS',
  PENDING_REQUEST: 'PENDING_REQUEST',
};

export const ADMIN_TAB_LIST = [
  {
    key: ADMIN_TAB_KEYS.MEMBERS,
    title: 'Members',
    status: CLUB_MEMBERSHIP_STATUS.ACCEPTED,
  },
  {
    key: ADMIN_TAB_KEYS.PENDING_REQUEST,
    title: 'Requests',
    status: CLUB_MEMBERSHIP_STATUS.PENDING,
  },
];
