import React, { useCallback, useState } from 'react';
import { TabBar, TabView } from 'react-native-tab-view';
import dark from 'core/constants/themes/dark';
import { Dimensions, Text, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Header from '@/src/components/shared/Header';
import _findIndex from 'lodash/findIndex';
import { router, useLocalSearchParams } from 'expo-router';
import APP_LAYOUT_CONSTANTS from 'core/constants/appLayout';
import {
  ADMIN_TAB_KEYS,
  ADMIN_TAB_LIST,
} from '../../constants/clubsTabDetails';
import MembersListView from '../../components/MembersListView';
import useAdminPanelPageStyles from './AdminPanelPage.style';

const initialLayout = { width: Dimensions.get('window').width };

const getActiveTabIndex = (tab?: string) => {
  const activeIndex = _findIndex(
    ADMIN_TAB_LIST,
    (tabItem) => tabItem.key === tab,
  );
  if (activeIndex === -1) {
    return 0;
  }
};

const AdminPanelPage = ({ clubId }: { clubId: string }) => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const styles = useAdminPanelPageStyles();
  const routes = ADMIN_TAB_LIST;

  const { tab: currentTab }: { tab: string } = useLocalSearchParams();

  const [index, setIndex] = useState(getActiveTabIndex(currentTab));

  const [pendingCount, setPendingCount] = useState(0);

  const onIndexChange = useCallback(
    (updatedIndex: number) => {
      setIndex(updatedIndex);
      router.setParams({
        tab: routes[updatedIndex].key,
      });
    },
    [setIndex, routes],
  );

  const renderTabBar = (props: any) => (
    <View style={styles.tabBarContainer}>
      <TabBar
        {...props}
        indicatorStyle={styles.indicator}
        style={styles.tabBar}
        tabStyle={[styles.tabStyle, !isCompactMode && styles.expandedTabStyle]}
        labelStyle={styles.label}
        activeColor={dark.colors.secondary}
        inactiveColor={dark.colors.textDark}
        renderLabel={({ route, color }) => (
          <View style={styles.tabBarLabelRow}>
            <Text style={{ ...styles.label, color }}>{route.title}</Text>
            {route.key === ADMIN_TAB_KEYS.PENDING_REQUEST &&
            pendingCount > 0 ? (
              <View style={styles.pendingCount}>
                <Text style={styles.pendingCountText}>{pendingCount}</Text>
              </View>
            ) : null}
          </View>
        )}
      />
    </View>
  );

  const renderScene = useCallback(
    ({ route }: { route: any }) => (
      <View style={{ marginHorizontal: isCompactMode ? 16 : 0, flex: 1 }}>
        <MembersListView
          status={route.status}
          key={route.key}
          onPendingCountFetched={
            route.key === ADMIN_TAB_KEYS.PENDING_REQUEST
              ? setPendingCount
              : undefined
          }
          clubId={clubId}
        />
      </View>
    ),
    [isCompactMode, setPendingCount, clubId],
  );

  const renderTabView = () => (
    <TabView
      navigationState={{ index, routes }}
      renderScene={renderScene}
      onIndexChange={onIndexChange}
      initialLayout={initialLayout}
      renderTabBar={renderTabBar}
    />
  );

  if (isCompactMode) {
    return (
      <View style={styles.tabMainContainer}>
        <Header title="Admin" />
        {renderTabView()}
      </View>
    );
  }

  return (
    <View
      style={[
        styles.tabMainContainer,
        {
          maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
          paddingHorizontal: 40,
        },
      ]}
    >
      <View>
        {!isCompactMode && (
          <View style={{ alignItems: 'flex-start', paddingVertical: 30 }}>
            <Text style={styles.adminViewText}>Admin View</Text>
          </View>
        )}
        {renderTabView()}
      </View>
    </View>
  );
};

export default React.memo(AdminPanelPage);
