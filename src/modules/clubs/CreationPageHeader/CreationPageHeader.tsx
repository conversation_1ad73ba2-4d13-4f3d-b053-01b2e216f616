import React, { useCallback } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import Dark from '@/src/core/constants/themes/dark';
import useMediaQuery from 'core/hooks/useMediaQuery';
import useGoBack from 'navigator/hooks/useGoBack';
import _size from 'lodash/size';

import Entypo from '@expo/vector-icons/Entypo';
import PrimaryButton from '@/src/components/atoms/PrimaryButton';
import { closeRightPane } from 'molecules/RightPane/RightPane';
import styles from './CreationPageHeader.style';

interface CreationPageHeaderProps {
  title: string;
  renderTitle?: Function;
  buttonLabel: string;
  isTransparentBg?: boolean;
  goBack?: Function;
  onPress: Function;
  buttonDisabled?: boolean;
}

const CreationPageHeader = ({
  title,
  renderTitle: renderTitleFromProps,
  isTransparentBg = false,
  buttonLabel = 'Create',
  onPress,
  goBack: goBackFromProps,
  buttonDisabled = false,
}: CreationPageHeaderProps) => {
  const { isMobile } = useMediaQuery();

  const { goBack } = useGoBack();

  const onClosePress = useCallback(() => {
    if (!isMobile) {
      closeRightPane();
      return;
    }
    goBack();
  }, [goBack, isMobile]);

  const renderTitle = () => {
    if (renderTitleFromProps) {
      return renderTitleFromProps();
    }
    if (_size(title) <= 0) return null;
    return <Text style={styles.headerTitle}>{title}</Text>;
  };

  return (
    <View
      style={{
        ...styles.header,
        backgroundColor: isTransparentBg
          ? 'transparent'
          : Dark.colors.background,
      }}
    >
      <View style={styles.headerLeft}>
        <TouchableOpacity
          onPress={goBackFromProps ?? onClosePress}
          hitSlop={{ left: 12, right: 12 }}
          style={{ width: 20 }}
        >
          <Entypo name="cross" size={20} color="white" />
        </TouchableOpacity>
        {renderTitle()}
      </View>
      <PrimaryButton
        buttonStyle={[
          styles.joinButtonStyle,
          buttonDisabled && styles.disabledButton,
        ]}
        labelStyle={[
          styles.joinButtonText,
          buttonDisabled && styles.disabledButtonText,
        ]}
        label={buttonLabel}
        onPress={buttonDisabled ? () => {} : onPress}
      />
    </View>
  );
};

export default React.memo(CreationPageHeader);
