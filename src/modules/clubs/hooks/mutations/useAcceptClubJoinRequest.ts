import { gql, useMutation } from '@apollo/client';
import { useCallback, useState } from 'react';
import _get from 'lodash/get';

const ACCEPT_CLUB_JOIN_REQUEST = gql`
  mutation AddClubMember($clubId: ID!, $userId: ID!) {
    addClubMember(clubId: $clubId, userId: $userId)
  }
`;

const useAcceptClubJoinRequest = () => {
  const [addClubMemberMutation] = useMutation(ACCEPT_CLUB_JOIN_REQUEST);

  const [isAcceptingRequest, setIsAcceptingRequest] = useState(false);

  const acceptClubJoinRequest = useCallback(
    async ({ clubId, userId }: { clubId: string; userId: string }) => {
      try {
        setIsAcceptingRequest(true);
        const { data } = await addClubMemberMutation({
          variables: {
            clubId,
            userId,
          },
        });
        setIsAcceptingRequest(false);
        return _get(data, 'addClubMember', false);
      } catch (error) {
        setIsAcceptingRequest(false);
      } finally {
        setIsAcceptingRequest(false);
      }
    },
    [addClubMemberMutation],
  );

  return {
    acceptClubJoinRequest,
    isAcceptingRequest,
  };
};

export default useAcceptClubJoinRequest;
