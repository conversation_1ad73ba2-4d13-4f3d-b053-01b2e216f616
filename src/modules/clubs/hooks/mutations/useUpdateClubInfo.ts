import { gql, useMutation } from '@apollo/client';
import { useCallback, useState } from 'react';

const UPDATE_CLUB_MUTATION = gql`
  mutation UpdateClub($input: UpdateClubInput!) {
    updateClub(input: $input)
  }
`;

export type Visibility = 'PUBLIC' | 'PRIVATE';

interface UpdateClubInfoInput {
  clubId: string;
  name: string;
  description?: string;
  visibility: Visibility;
}

const useUpdateClubInfo = () => {
  const [updateClubMutationQuery, { error }] =
    useMutation(UPDATE_CLUB_MUTATION);

  const [isUpdatingClub, setIsUpdatingClub] = useState<boolean>(false);

  const updateClubInfo = useCallback(
    async ({ clubInfo }: { clubInfo: UpdateClubInfoInput }) => {
      if (isUpdatingClub) {
        return false;
      }

      try {
        setIsUpdatingClub(true);
        await updateClubMutationQuery({
          variables: {
            input: {
              ...clubInfo,
            },
          },
        });
        return true;
      } catch (e) {
        setIsUpdatingClub(false);
        return false;
      } finally {
        setIsUpdatingClub(false);
      }
    },
    [updateClubMutationQuery, isUpdatingClub],
  );

  return {
    updateClubInfo,
    isUpdatingClub,
    error,
  };
};

export default useUpdateClubInfo;
