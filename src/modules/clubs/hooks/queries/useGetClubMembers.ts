import { gql, useLazyQuery } from '@apollo/client';
import { useCallback } from 'react';
import _filter from 'lodash/filter';
import _isEmpty from 'lodash/isEmpty';
import _includes from 'lodash/includes';
import _size from 'lodash/size';

const DEFAULT_PAGE_SIZE = 50;

const GET_CLUB_MEMBERS_BY_STATUS = gql`
  query ClubMembers(
    $clubId: ID!
    $clubMembershipStatus: ClubMembershipStatus!
    $page: Int
    $pageSize: Int
  ) {
    clubMembers(
      clubId: $clubId
      clubMembershipStatus: $clubMembershipStatus
      page: $page
      pageSize: $pageSize
    ) {
      results {
        id
        clubId
        userId
        role
        joinedAt
        clubMembershipStatus
        memberInfo {
          username
          profileImageUrl
          rating
        }
      }
      pageNumber
      pageSize
      hasMore
      totalResults
    }
  }
`;

interface MemberInfo {
  username: string;
  profileImageUrl: string;
  rating: number;
}

export interface ClubMember {
  id: string;
  clubId: string;
  userId: string;
  role: string;
  joinedAt: string;
  clubMembershipStatus: string;
  memberInfo: MemberInfo;
}

const useGetClubMembersByStatus = ({
  pageSize = DEFAULT_PAGE_SIZE,
  clubMembershipStatus,
  clubId,
}: {
  pageSize?: number;
  clubMembershipStatus?: string;
  clubId: string;
}) => {
  const [fetchClubMembersQuery, { loading, error, client, data }] =
    useLazyQuery(GET_CLUB_MEMBERS_BY_STATUS, {
      fetchPolicy: 'cache-first',
      notifyOnNetworkStatusChange: true,
    });

  const fetchClubMembers = useCallback(
    async ({ pageNumber }: { pageNumber: number }) => {
      if (loading) {
        return;
      }

      return fetchClubMembersQuery({
        variables: {
          page: pageNumber,
          pageSize,
          clubId,
          clubMembershipStatus,
        },
      });
    },
    [loading, fetchClubMembersQuery, pageSize, clubId, clubMembershipStatus],
  );

  const updateClubMembersCache = useCallback(
    async ({ addedItems = [], removedItemIds = [], pageNumber = 1 }) => {
      client.cache.updateQuery(
        {
          query: GET_CLUB_MEMBERS_BY_STATUS,
          variables: {
            page: pageNumber,
            pageSize,
            clubId,
            clubMembershipStatus,
          },
          broadcast: true,
          overwrite: true,
        },
        (data) => {
          const { clubMembers } = data ?? EMPTY_OBJECT;
          if (_isEmpty(clubMembers)) {
            return data;
          }

          let { results: updatedResults, totalResults } =
            clubMembers ?? EMPTY_OBJECT;

          if (!_isEmpty(addedItems)) {
            updatedResults = [...addedItems, ...updatedResults];
          }

          if (!_isEmpty(removedItemIds)) {
            updatedResults = _filter(
              updatedResults,
              (item) => !_includes(removedItemIds, item?._id),
            );
          }

          const updatedTotalItems =
            totalResults + _size(addedItems) - _size(removedItemIds);

          const updatedDataInCache = {
            ...data,
            clubMembers: {
              ...clubMembers,
              results: updatedResults,
              totalResults: updatedTotalItems,
            },
          };
          return updatedDataInCache;
        },
      );

      const updatedCache = client.cache.readQuery({
        query: GET_CLUB_MEMBERS_BY_STATUS,
        variables: { page: pageNumber, pageSize, clubId, clubMembershipStatus },
      });

      return updatedCache;
    },
    [client.cache, pageSize, clubId, clubMembershipStatus],
  );

  return {
    loading,
    error,
    data,
    fetchClubMembers,
    updateClubMembersCache,
  };
};

export default useGetClubMembersByStatus;
