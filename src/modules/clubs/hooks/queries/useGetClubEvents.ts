import { useCallback } from 'react';
import { gql, useLazyQuery } from '@apollo/client';
import _isEmpty from 'lodash/isEmpty';
import _filter from 'lodash/filter';
import _includes from 'lodash/includes';
import _size from 'lodash/size';
import _get from 'lodash/get';
import { ClubEventType } from '../mutations/useCreateEventForClub';

const EVENTS_QUERY = gql`
  query ClubEvents(
    $page: Int
    $pageSize: Int
    $clubId: ID
    $clubEventType: ClubEventType
    $from: Time
    $to: Time
  ) {
    clubEvents(
      page: $page
      pageSize: $pageSize
      clubId: $clubId
      clubEventType: $clubEventType
      from: $from
      to: $to
    ) {
      results {
        id
        clubEventPlayId
        participationCount
        clubId
        title
        visibility
        description
        clubEventType
        startTime
        ratedEvent
        openToAll
        playerSetting {
          minRating
          maxRating
        }
        createdBy
        createdAt
        updatedAt
      }
      pageNumber
      pageSize
      hasMore
      totalResults
    }
  }
`;

const DEFAULT_PAGE_SIZE = 20;

export interface ClubEvent {
  id: string;
  clubId: string;
  title: string;
  description: string;
  clubEventPlayId: string;
  clubEventType: ClubEventType;
  startTime: string;
  createdBy: string;
  createdAt: string;
}

interface GetEventsOfClubProps {
  clubId?: string;
  pageSize?: number;
  after?: string;
  clubEventType?: ClubEventType;
  from?: string;
  to?: string;
}

const useGetClubEvents = ({
  pageSize = DEFAULT_PAGE_SIZE,
  clubId,
  clubEventType,
  from,
  to,
}: GetEventsOfClubProps) => {
  const [fetchEvents, { loading, client, error, data }] = useLazyQuery(
    EVENTS_QUERY,
    {
      fetchPolicy: 'cache-first',
      notifyOnNetworkStatusChange: true,
    },
  );

  const fetchPage = useCallback(
    (pageNumber: number) => {
      if (loading) return;
      return fetchEvents({
        variables: {
          page: pageNumber,
          pageSize,
          clubId,
          clubEventType,
          from,
          to,
        },
      });
    },
    [loading, fetchEvents, pageSize, clubId, clubEventType, from, to],
  );

  const updateClubEventsCache = useCallback(
    ({
      updateCacheClubId,
      addedItems = [],
      removedItemIds = [],
      pageNumber = 1,
    }: {
      updateCacheClubId?: string;
      addedItems?: any[];
      removedItemIds?: string[];
      pageNumber?: number;
    }) => {
      client.cache.updateQuery(
        {
          query: EVENTS_QUERY,
          variables: {
            page: pageNumber,
            pageSize,
            clubId: updateCacheClubId,
            clubEventType,
            from,
            to,
          },
          broadcast: true,
          overwrite: true,
        },
        (data) => {
          const { clubEvents } = data ?? EMPTY_OBJECT;

          const { results, totalResults } = clubEvents ?? EMPTY_OBJECT;

          if (_isEmpty(clubEvents)) {
            return data;
          }

          let updatedResults = results;

          if (!_isEmpty(addedItems)) {
            updatedResults = [...addedItems, ...updatedResults];
          }

          if (!_isEmpty(removedItemIds)) {
            updatedResults = _filter(
              updatedResults,
              (item) => !_includes(removedItemIds, item?.id),
            );
          }

          const updatedTotalItems =
            totalResults + _size(addedItems) - _size(removedItemIds);

          return {
            ...data,
            clubEvents: {
              ...clubEvents,
              results: updatedResults,
              totalResults: updatedTotalItems,
            },
          };
        },
      );
    },
    [client.cache, pageSize, clubEventType, from, to],
  );

  return {
    events: _get(data, 'clubEvents.results', []),
    fetchPage,
    loading,
    error,
    updateClubEventsCache,
  };
};

export default useGetClubEvents;
