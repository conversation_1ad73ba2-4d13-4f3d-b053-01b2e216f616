import { FlatList } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import { Text, View } from 'react-native';
import RecentConfigCard from '../../RecentConfigCard/RecentConfigCard';
import PropTypes from 'prop-types';
import useMediaQuery from 'core/hooks/useMediaQuery';
import useRecentConfigTabStyles from './RecentConfigTab.style';
import Loading from 'atoms/Loading';
import React, { useEffect } from 'react';
import useUserStore from 'store/useUserStore';
import useNetworkStatus from 'core/hooks/useNetworkStatus';
import ErrorView from 'atoms/ErrorView';

const RecentConfigTab = (props) => {
  const styles = useRecentConfigTabStyles();
  const { controller } = props;
  const { isMobile: isCompactMode } = useMediaQuery();

  const { isNetworkReachable } = useNetworkStatus();

  const { fetchUserRecentPresets, userPresets, loading, error } = useUserStore(
    (state) => ({
      fetchUserRecentPresets: state.fetchUserRecentPresets,
      userPresets: state.recentPresets,
      loading: state.recentPresetsLoading,
      error: state.recentPresetsError,
    }),
  );

  useEffect(() => {
    if (isNetworkReachable) {
      fetchUserRecentPresets();
    }
  }, [fetchUserRecentPresets, isNetworkReachable]);

  if (loading) {
    return <Loading label={'Loading'} />;
  }

  if (error && isNetworkReachable) {
    return (
      <ErrorView
        isHeaderVisible={false}
        errorMessage="An error occurred while connecting to server."
        onRetry={fetchUserRecentPresets}
      />
    );
  }

  if (_isEmpty(userPresets)) {
    return (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
        }}
      >
        <Text style={{ fontFamily: 'Montserrat-500', color: 'white' }}>
          No Recent Presets !!
        </Text>
      </View>
    );
  }

  return (
    <FlatList
      keyExtractor={(item, index) => `${item?._id} - ${index}`}
      contentContainerStyle={{ gap: 14, padding: 16 }}
      renderItem={({ item }) => (
        <RecentConfigCard
          presetInfo={item}
          controller={controller}
          isRecentTab={true}
        />
      )}
      data={userPresets}
      showsVerticalScrollIndicator={false}
      numColumns={isCompactMode ? 1 : 2}
      columnWrapperStyle={isCompactMode ? null : styles.columnWrapperStyle}
    />
  );
};

RecentConfigTab.propTypes = {
  recentConfigItems: PropTypes.array,
  controller: PropTypes.object,
};

export default React.memo(RecentConfigTab);
