import PropTypes from "prop-types"
import { View, Text } from "react-native"
import useCardWithLabelAndValueStyles from "./CardWithLabelAndValue.style"
import _isNil from "lodash/isNil"

const CardWithLabelAndValue = (props) => {
    const { value, label, containerStyle, valueStyle, labelStyle } = props
    const styles = useCardWithLabelAndValueStyles()
    
    return (
        <View style={[_isNil(containerStyle) && styles.container, containerStyle]}>
            <Text style={[styles.valueText, valueStyle]}>
                {value}
            </Text>
            <Text style={[styles.labelText, labelStyle]}>
                {label}
            </Text>
        </View>
    )
}

CardWithLabelAndValue.propTypes = {
    value: PropTypes.string,
    label: PropTypes.string,
    containerStyle: PropTypes.object,
    valueStyle: PropTypes.object,
    labelStyle: PropTypes.object
}

export default CardWithLabelAndValue