import React, { useCallback, useEffect, useState } from 'react';
import { View, TextInput, TouchableOpacity, StyleSheet, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import InfoIcon from '../InfoIcon/InfoIcon';
import PropTypes from 'prop-types';
import styles from './DirectionOfInputField.style'

const SelectDirectionOfInput = (props) => {

    const { onInputDirectionChange, showIconsOnly = false, selectedDirection = 'ltr' ,hideBorder=false } = props

    const [isLeftToRight, setIsLeftToRight] = useState(selectedDirection === 'ltr');

    const handleDirectionChange = useCallback((direction) => {
        setIsLeftToRight(direction === 'ltr');
        onInputDirectionChange?.(direction);
    }, [setIsLeftToRight, onInputDirectionChange]);

    useEffect(() => {
        setIsLeftToRight(selectedDirection === 'ltr')
    }, [selectedDirection, setIsLeftToRight])

    return (
        <View style={[styles.container, (showIconsOnly || hideBorder) && { borderBottomWidth: 0 }]}>
            {!showIconsOnly && (<View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: "center" }}>
                <Text style={styles.label}>Input From </Text>
                {/* <InfoIcon /> */}
            </View>)}
            <View style={styles.rightIcons}>
                <TouchableOpacity
                    style={[
                        styles.icon,
                        isLeftToRight && styles.selectedIcon,
                    ]}
                    onPress={() => handleDirectionChange('ltr')}
                >
                    <Ionicons name="arrow-forward" size={15} color="white" />
                </TouchableOpacity>
                <TouchableOpacity
                    style={[
                        styles.icon,
                        !isLeftToRight && styles.selectedIcon,
                    ]}
                    onPress={() => handleDirectionChange('rtl')}
                >
                    <Ionicons name="arrow-back" size={15} color="white" />
                </TouchableOpacity>
            </View>
        </View>
    );
};

SelectDirectionOfInput.propTypes = {
    onInputDirectionChange: PropTypes.func,
    showIconsOnly: PropTypes.bool,
    selectedDirection: PropTypes.string,
    hideBorder:PropTypes.bool
}

export default SelectDirectionOfInput;
