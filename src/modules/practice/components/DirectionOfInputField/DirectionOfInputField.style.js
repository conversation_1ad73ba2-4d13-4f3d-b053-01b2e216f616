import { StyleSheet } from "react-native";
import dark from "../../../../core/constants/themes/dark";

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent:'space-between',
        paddingVertical: 16,
        borderBlockColor: dark.colors.tertiary,
        borderBottomWidth: 1,
    },
    label: {
        fontSize: 12,
        fontFamily: 'Montserrat-500',
        color: '#FFFFFF',
    },
    infoIcon: {
        paddingHorizontal: 8,
    },
    rightIcons: {
        gap:6,
        flexDirection: 'row',
        alignItems: 'center',
    },
    icon: {
        backgroundColor: dark.colors.primary,
        borderRadius: 6,
        height:28,
        width:36,
        borderWidth:1,
        borderColor:dark.colors.tertiary,
        justifyContent:'center',
        alignItems:"center"
    },
    selectedIcon: {
        borderColor:'white'
    },
});

export default styles