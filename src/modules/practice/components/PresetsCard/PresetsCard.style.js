import { StyleSheet } from "react-native";
import dark from "../../../../core/constants/themes/dark";
import useMediaQuery from 'core/hooks/useMediaQuery'
import { useMemo } from "react";

const createStyles = (isCompactMode) => StyleSheet.create({
    presetItem: {
        paddingVertical: 12,
        paddingRight: 10,
        borderBottomColor: dark.colors.tertiary,
        borderBottomWidth: 1,
        flexDirection: isCompactMode ? 'row' : 'column',
        justifyContent: "space-between"
    },
    presetName: {
        fontFamily: "Montserrat-500",
        color: '#FFFFFF',
        lineHeight: 17,
        fontSize: 14
    },
    presetQuestions: {
        fontFamily: "Montserrat-600",
        color: dark.colors.textDark,
        fontSize: 9,
        lineHeight: 11,
    },
    removeButton: {
        marginTop: isCompactMode ? 0 : 8,
        color: dark.colors.secondary,
        fontSize: 11,
        fontFamily: 'Montserrat-600',
        lineHeight: 20,
    },
    iconContainer: {
        width: 28,
        height: 28,
        justifyContent: "center",
        alignItems: "center",
        borderRadius: 4,
        borderWidth: 1,
        borderColor: dark.colors.tertiary
    }
});

const usePresetCardStyles = () => {
    const { isMobile } = useMediaQuery();
    const styles = useMemo(() => createStyles(isMobile), [isMobile]);

    return styles;
};

export default usePresetCardStyles

