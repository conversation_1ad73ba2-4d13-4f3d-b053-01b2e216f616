import React, {useCallback, useMemo, useState} from 'react'
import {Dimensions, ScrollView, Text, TouchableOpacity, View,} from 'react-native'
import dark from '../../../../core/constants/themes/dark'
import PropTypes from 'prop-types'

import Entypo from '@expo/vector-icons/Entypo'
import styles from './UserStats.style'

import Header from 'shared/Header'
import Loading from 'atoms/Loading'
import ErrorView from 'atoms/ErrorView'

import WavyGraphComponent from '../../../../components/shared/WavyGraphComponent'
import CardWithLabelAndValue from '../CardWithLabelAndValue'
import {getFormattedTimeWithMS} from '../../../../core/utils/general'

import _isEmpty from 'lodash/isEmpty'
import _isNil from 'lodash/isNil'
import _reduce from 'lodash/reduce'
import _sumBy from 'lodash/sumBy'
import _sum from 'lodash/sum'
import _map from 'lodash/map'

import useMediaQuery from 'core/hooks/useMediaQuery'
import {closeRightPane} from 'molecules/RightPane/RightPane'
import {getConfigTagsFromIdentifier} from '../../utils/getIdentifierStringFromConfig'
import ConfigTags from '../ConfigTags/ConfigTags'
import Analytics from '../../../../core/analytics'
import {ANALYTICS_EVENTS} from '../../../../core/analytics/const'
import useUserPresetStatsByDate from '../../hooks/queries/useUserPresetStatsByDate'

const DURATIONS_LIST = [
    {label: '01 D', duration: 1},
    {label: '01 W', duration: 7},
    {label: '01 M', duration: 30},
    {label: '06 M', duration: 180},
    {label: 'All', duration: 0},
]

const width = Dimensions.get('window').width

const UserStat = (props) => {
    const {
        loadedUserStats,
        identifier,
        selectedDuration,
        setSelectedDuration,
    } = props
    const {isMobile: isCompactMode} = useMediaQuery()

    const stats = useMemo(
        () => _map(loadedUserStats, 'userPresetStats'),
        [loadedUserStats]
    )

    const totalQuestionsSolved = useMemo(
        () => _sumBy(stats, 'questionsSolved'),
        [stats]
    )
    const avgTime = useMemo(
        () => (stats.length == 0 ? 0 : _sumBy(stats, 'avgTime') / stats.length),
        [stats]
    )
    const totalInaccuracies = useMemo(
        () => _sumBy(stats, (stat) => _sum(stat?.inaccuracyPerformanceTrend)),
        [stats]
    )
    const accuracyPercentage = useMemo(
        () =>
            totalQuestionsSolved == 0
                ? 0
                : ((totalQuestionsSolved - totalInaccuracies) /
                    totalQuestionsSolved) *
                100,
        [totalInaccuracies, totalQuestionsSolved]
    )

    const handleDurationChange = useCallback(
        ({newDurationObj}) => {
            Analytics.track(
                ANALYTICS_EVENTS.PRACTICE_MODULE.CHANGED_ANALYSIS_DURATION,
                newDurationObj
            )
            setSelectedDuration(newDurationObj)
        },
        [setSelectedDuration]
    )

    const avgTimeTrend = useMemo(() => {
        if (_isEmpty(loadedUserStats) || _isNil(loadedUserStats)) {
            return []
        }
        if (selectedDuration?.duration === 1) {
            return _reduce(
                loadedUserStats,
                (acc, stats) => {
                    const timePerformanceTrend =
                        stats?.userPresetStats?.timePerformanceTrend
                    if (timePerformanceTrend) {
                        acc.push(...timePerformanceTrend)
                    }
                    return acc
                },
                []
            )
        }
        return _map(loadedUserStats, (stats) => stats?.userPresetStats?.avgTime)
    }, [loadedUserStats])

    const configTags = useMemo(
        () => getConfigTagsFromIdentifier({identifier: identifier}),
        [identifier]
    )

    return (
        <View style={{flex: 1, overflow: 'hidden'}}>
            <Header title={'Analytics'}/>
            {!isCompactMode && (
                <View style={styles.headerRow}>
                    <Text style={styles.titleText}>Analytics</Text>
                    <Entypo
                        name="cross"
                        size={20}
                        color={'white'}
                        onPress={closeRightPane}
                    />
                </View>
            )}
            <View
                style={{
                    flexWrap: 'wrap',
                    flexDirection: 'row',
                    gap: 8,
                    marginVertical: 10,
                    marginHorizontal: 16,
                }}
            >
                {_map(configTags, (tag, index) => (
                    <ConfigTags key={`${index}`} label={tag}/>
                ))}
            </View>

            <View style={styles.durationOuterContainer}>
                {_map(DURATIONS_LIST, (item, index) => {
                    const isSelected =
                        item?.duration === selectedDuration.duration
                    return (
                        <TouchableOpacity
                            key={`${item?.label} - ${index}`}
                            onPress={() =>
                                handleDurationChange({newDurationObj: item})
                            }
                            style={[
                                styles.durationContainer,
                                isSelected && {
                                    backgroundColor: dark.colors.tertiary,
                                },
                            ]}
                        >
                            <Text
                                style={[
                                    styles.durationLabelText,
                                    isSelected && {
                                        color: dark.colors.secondary,
                                    },
                                ]}
                            >
                                {item?.label}
                            </Text>
                        </TouchableOpacity>
                    )
                })}
            </View>

            <View style={styles.container}>
                <Text style={styles.headerText}>Your Preset Stats</Text>
                <View style={{marginBottom: 16}}>
                    <View
                        style={{flexDirection: 'row', gap: 13, marginTop: 0}}
                    >
                        <CardWithLabelAndValue
                            label={'Total Questions '}
                            value={totalQuestionsSolved}
                        />
                        <CardWithLabelAndValue
                            label={'Avg. time/qn'}
                            value={getFormattedTimeWithMS(avgTime)}
                        />
                    </View>

                    <View
                        style={{flexDirection: 'row', gap: 13, marginTop: 12}}
                    >
                        <CardWithLabelAndValue
                            label={'Accuracy(%) '}
                            value={`${Math.floor(accuracyPercentage)}%`}
                        />
                        <View style={{flex: 1, paddingHorizontal: 12}}/>
                        {/* <CardWithLabelAndValue label={'Slowest Question'} value={getFormattedTimeWithMS(21 * 1000)} /> */}
                    </View>
                </View>
                {!_isEmpty(avgTimeTrend) && (
                    <>
                        <Text style={styles.headerText}>
                            Your Preset Progress
                        </Text>
                        <WavyGraphComponent
                            dataPoints={avgTimeTrend}
                            width={isCompactMode ? width * 0.85 : 450}
                        />
                    </>
                )}
                <View style={{height: 40}}/>
            </View>
        </View>
    )
}

UserStat.propTypes = {
    userStats: PropTypes.any,
    loadedUserStats: PropTypes.array,
    identifier: PropTypes.string,
}

const UserStatsContainer = (props) => {
    const {identifier} = props

    const [selectedDuration, setSelectedDuration] = useState(DURATIONS_LIST[0])

    const {
        loading,
        error,
        presetStats: loadedUserStats,
    } = useUserPresetStatsByDate({
        identifier,
        durationFilter: selectedDuration?.duration,
    })

    if (loading) {
        return <Loading label={'Loading Stats'}/>
    }

    if (error) {
        return <ErrorView errorMessage={'Something went wrong !!'}/>
    }

    return (
        <ScrollView showsVerticalScrollIndicator={false}>
            <UserStat
                loadedUserStats={loadedUserStats}
                {...props}
                setSelectedDuration={setSelectedDuration}
                selectedDuration={selectedDuration}
            />
        </ScrollView>
    )
}

export default React.memo(UserStatsContainer)
