export const NTH_ROOT_FIELDS_KEYS = {
  IS_PERFECT_POWER: 'IS_PERFECT_POWER',
  ROOT: 'ROOT',
  NO_OF_DIGITS: 'NO_OF_DIGITS',
  ROUND_OFF_TO_N_DECIMALS: 'ROUND_OFF_TO_N_DECIMALS',
  NUMBER_OF_QUESTIONS: 'noOfQuestions',
} as const;

export const EXPONENT_FIELDS_KEYS = {
  MIN_EXPONENT_VALUE: 'MIN_EXPONENT_VALUE',
  MAX_EXPONENT_VALUE: 'MAX_EXPONENT_VALUE',
  DIGITS_IN_BASE: 'DIGITS_IN_BASE',
  BASE_RANGE_VALUE: 'BASE_RANGE_VALUE',
  EXPONENT_RANGE_VALUE: 'EXPONENT_RANGE_VALUE',
  MAX_EXPONENT: 'MAX_EXPONENT',
  DECIMALS_IN_EXPONENT: 'DECIMALS_IN_EXPONENT',
  PRECISION_IN_ANSWER: 'PRECISION_IN_ANSWER',
  NUMBER_OF_QUESTIONS: 'noOfQuestions',
} as const;

export const MOD_FIELDS_KEYS = {
  DIGITS_ROW_1: 'DIGITS_ROW_1',
  DIGITS_ROW_2: 'DIGITS_ROW_2',
  NUMBER_OF_QUESTIONS: 'noOfQuestions',
} as const;

export const LCM_FIELDS_KEYS = {
  FIRST_NO_DIGITS: 'FIRST_NO_DIGITS',
  SECOND_NO_DIGITS: 'SECOND_NO_DIGITS',
  NUMBER_OF_QUESTIONS: 'noOfQuestions',
} as const;

export const HCF_FIELDS_KEYS = {
  FIRST_NO_DIGITS: 'FIRST_NO_DIGITS',
  SECOND_NO_DIGITS: 'SECOND_NO_DIGITS',
  NUMBER_OF_QUESTIONS: 'noOfQuestions',
} as const;
