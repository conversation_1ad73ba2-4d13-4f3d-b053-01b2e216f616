import { Redirect } from "expo-router";
import { useNavigation } from "@react-navigation/native";
import PropTypes from "prop-types";

import React, { cloneElement } from "react";

import _isEmpty from "lodash/isEmpty"
import { usePracticeContext } from "../../../../app/_layout";


const WithPracticeConfigContext = (props) => {
    const { children } = props
    const contextValue = usePracticeContext();
    const { practiceConfig, practiceSession } = contextValue ?? EMPTY_OBJECT;

    if (_isEmpty(contextValue) || _isEmpty(contextValue?.practiceConfig)) {
        return <Redirect href={'/practice'} />
    }

    return cloneElement(children, { practiceConfig, practiceSession })
}

WithPracticeConfigContext.propTypes = {
    children: PropTypes.node
}

export default React.memo(WithPracticeConfigContext)