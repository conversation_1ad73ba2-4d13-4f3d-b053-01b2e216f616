import React, { useCallback, useEffect } from 'react';
import { KeyboardAvoidingView, Platform, Text, View } from 'react-native';
import OperationSelector from './components/OperatorSelector/OperatorSelector';
import ConfigurationForm from './components/ConfigurationForm/ConfigurationForm';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import dark from '../../../../core/constants/themes/dark';
import styles from './PracticePresets.style';
import { PRACTICE_LEFT_PANE_TABS } from './components/OperatorSelector/constants/practice';
import PresetsContainer from './components/Presets';
import Analytics from '../../../../core/analytics';
import { ANALYTICS_EVENTS } from '../../../../core/analytics/const';
import RecentConfigSection from '../../components/RecentConfigSection/RecentConfigSection';
import usePracticePresetsController from '../../hooks/usePracticePresetsController';
import { PAGE_NAMES } from '../../../../core/constants/pageNames';
import { SafeAreaView } from 'react-native-safe-area-context';

const PracticePresets = () => {
  const practicePresetsControllerObject = usePracticePresetsController();

  const {
    selectedTabId,
    allSelectedPresets,
    removePreset,
    onLeftPaneTabChange,
  } = practicePresetsControllerObject ?? EMPTY_OBJECT;

  const { isMobile } = useMediaQuery();

  useEffect(() => {
    webengage?.screen?.(PAGE_NAMES.PRACTICE_PAGE);
    Analytics.track(ANALYTICS_EVENTS.PRACTICE_MODULE.VIEWED);
  }, []);

  const renderOperatorSelectorSection = useCallback(() => {
    return (
      <OperationSelector
        key={'operation-selector'}
        allSelectedPresets={allSelectedPresets}
        onLeftPaneTabChange={onLeftPaneTabChange}
        selectedTabId={selectedTabId}
      />
    );
  }, [onLeftPaneTabChange, allSelectedPresets, selectedTabId]);

  const renderConfigFormSection = useCallback(() => {
    return (
      <ConfigurationForm
        key={selectedTabId}
        controller={practicePresetsControllerObject}
      />
    );
  }, [practicePresetsControllerObject, selectedTabId]);

  const renderSelectedPresetsSection = useCallback(() => {
    return (
      <PresetsContainer
        key={'presets-container'}
        selectedPresets={allSelectedPresets}
        onRemove={removePreset}
      />
    );
  }, [removePreset, allSelectedPresets]);

  const renderPracticeConfigPageContent = () => {
    return (
      <View style={styles.container}>
        {renderOperatorSelectorSection()}
        {selectedTabId !== PRACTICE_LEFT_PANE_TABS.RECENT_AND_SAVED ? (
          renderConfigFormSection()
        ) : (
          <RecentConfigSection controller={practicePresetsControllerObject} />
        )}
        {renderSelectedPresetsSection()}
      </View>
    );
  };

  if (isMobile) {
    return (
      <View style={{ flex: 1 }}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{ flex: 1 }}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 48 : 24}
        >
          <View
            style={{
              paddingVertical: 24,
              borderBottomColor: dark.colors.tertiary,
              borderBottomWidth: 1,
              paddingTop: 24,
              // backgroundColor: '#fff',
            }}
          >
            <Text style={styles.headingText}>Nets</Text>
          </View>
          {renderPracticeConfigPageContent()}
        </KeyboardAvoidingView>
      </View>
    );
  }

  return renderPracticeConfigPageContent();
};

export default React.memo(PracticePresets);
