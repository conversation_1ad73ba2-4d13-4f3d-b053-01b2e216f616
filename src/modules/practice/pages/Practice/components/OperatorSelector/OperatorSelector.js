import React, { useCallback, useMemo, useState } from 'react';
import { View, Text, TouchableOpacity} from 'react-native';
import ScrollView from '@/src/components/atoms/Scrollview';

import { Image } from 'react-native';
import _isNil from 'lodash/isNil';
import _values from 'lodash/values';
import _map from 'lodash/map';
import _some from 'lodash/some';
import useMediaQuery from '@/src/core/hooks/useMediaQuery'
import useOperatorSelectorStyles from './OperatorSelector.style';
import PropTypes from 'prop-types';
import { PRACTICE_LEFT_PANE_TABS_ORDERED_LIST, PRACTICE_LEFT_PANE_TABS_CONFIGS } from './constants/practice';
import dark from '../../../../../../core/constants/themes/dark';
import AntDesign from '@expo/vector-icons/AntDesign';
import Analytics from "../../../../../../core/analytics";
import { ANALYTICS_EVENTS } from "../../../../../../core/analytics/const";
import { PAGE_NAME_KEY, PAGE_NAMES } from "../../../../../../core/constants/pageNames";

const OperationSelector = (props) => {
    const styles = useOperatorSelectorStyles()
    const { isMobile } = useMediaQuery()

    const { onLeftPaneTabChange, selectedTabId, allSelectedPresets } = props

    const selectedOperationConfig = PRACTICE_LEFT_PANE_TABS_CONFIGS[selectedTabId]

    const handleOnTabChange = useCallback((tabId) => {
        onLeftPaneTabChange?.(tabId)
        Analytics.track(ANALYTICS_EVENTS.PRACTICE_MODULE.CHANGED_PRACTICE_CONFIG_TAB, {
            category: tabId,
            [PAGE_NAME_KEY]: PAGE_NAMES.PRACTICE_PAGE
        })
        console.info(tabId)
    }, [onLeftPaneTabChange])

    const isPresetAddedForTab = useCallback(({ tabId }) => {
        const { categoryId } = PRACTICE_LEFT_PANE_TABS_CONFIGS[tabId];
        if (_isNil(categoryId)) return false;

        return _some(allSelectedPresets, selectedPreset => selectedPreset?.categoryId === categoryId);
    }, [allSelectedPresets])

    const renderPraticeLeftPaneTabs = useCallback(({ tabId }) => {

        const isSelectedTab = selectedTabId === tabId;

        const tabConfig = PRACTICE_LEFT_PANE_TABS_CONFIGS[tabId];

        const isAddedInPreset = isPresetAddedForTab({ tabId });

        return (
            <View style={styles.operatorBox} key={tabId}>
                <TouchableOpacity key={tabConfig.id} style={[styles.operationButton, isSelectedTab && styles.selectedOperation,]}
                    onPress={() => handleOnTabChange(tabConfig.id)}>
                    <View style={styles.operationIcon}>
                        <Image source={tabConfig.icon} style={{ height: 28, width: 28 }} />
                    </View>
                    {!isMobile && <Text style={styles.operationText}>{tabConfig.operationName}</Text>}
                </TouchableOpacity>
                {
                    isAddedInPreset && (<View style={styles.selectedBox} >
                        <AntDesign name="check" size={14} color='#1C1B1F' />
                    </View>)
                }
            </View>
        )
    }, [handleOnTabChange, selectedTabId, isMobile, selectedOperationConfig, isPresetAddedForTab])

    return (
        <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer} >
            {_map(PRACTICE_LEFT_PANE_TABS_ORDERED_LIST, (tabId) => (
                isMobile ? <View style={styles.mobileView} key={tabId}>
                    {renderPraticeLeftPaneTabs({ tabId })}
                    <Text style={styles.operationText} numberOfLines={2}>{PRACTICE_LEFT_PANE_TABS_CONFIGS[tabId]?.operationName}</Text>
                </View> : renderPraticeLeftPaneTabs({ tabId })))}
        </ScrollView>
    );
};

OperationSelector.propTypes = {
    allSelectedPresets: PropTypes.array,
    onLeftPaneTabChange: PropTypes.func,
    selectedTabId: PropTypes.string,
    checkIsCurrentOperationAnyPresetSelected: PropTypes.bool,
}

export default React.memo(OperationSelector);