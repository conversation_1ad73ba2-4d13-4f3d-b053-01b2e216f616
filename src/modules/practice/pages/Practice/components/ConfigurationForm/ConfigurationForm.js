import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import PropTypes from 'prop-types';

import _map from 'lodash/map';
import _filter from 'lodash/filter';

import _isEmpty from 'lodash/isEmpty';
import {
  PRACTICE_CATEGORIES,
  PRACTICE_LEFT_PANE_TABS_CONFIGS,
} from '../OperatorSelector/constants/practice';
import UpdateConfigModal from '../../../../components/UpdateConfigModal/UpdateConfigModal';
import useConfigurationFormStyles from './ConfigurationForm.style';
import ExpandablePresetConfigForm from '../../../../components/ExpandablePresetConfigForm';

import { getDefaultPresetConfig } from '../../../../utils/presetConfig';

import { PRESET_SAVED_STATE } from '../../../../constants/presetSavedState';
import FlashAnzanConfigForm from '../../../FlashAnzan/components/FlashAnzanConfigForm/FlashAnzanConfigForm';
import PresetConfigForm from '../../../PresetConfigForm';

const ConfigurationForm = (props) => {
  const styles = useConfigurationFormStyles();
  const { controller } = props;

  const {
    allSelectedPresets,
    selectedTabId,
    showUpdateModal,
    addPreset: addPresetFromProps,
    removePreset,
    onUnderStoodPressed,
    updatePreset: updatePresetFromProps,
  } = controller;

  const tabConfig = PRACTICE_LEFT_PANE_TABS_CONFIGS[selectedTabId];
  const { categoryId: categoryIdForSelectedTab } = tabConfig;
  const { tag } =
    PRACTICE_LEFT_PANE_TABS_CONFIGS[categoryIdForSelectedTab] ?? EMPTY_OBJECT;

  const [showNewForm, setShowNewForm] = useState(false);
  const [newPresetConfig, setNewPresetConfig] = useState(null);

  const selectedPresets = useMemo(
    () =>
      _filter(
        allSelectedPresets,
        (selectedPreset) =>
          selectedPreset?.categoryId === categoryIdForSelectedTab,
      ),
    [allSelectedPresets],
  );

  const createNewPreset = useCallback(() => {
    setNewPresetConfig(
      getDefaultPresetConfig({ categoryId: categoryIdForSelectedTab }),
    );
  }, [setNewPresetConfig, categoryIdForSelectedTab]);

  const adaptedPresetsList = useMemo(() => {
    const adaptedList = _map(selectedPresets, (preset) => ({
      ...preset,
      savedState: PRESET_SAVED_STATE.ADDED,
    }));

    if (!_isEmpty(newPresetConfig)) {
      adaptedList.push({
        ...newPresetConfig,
        savedState: PRESET_SAVED_STATE.NOT_ADDED,
      });
    }
    return adaptedList;
  }, [selectedPresets, newPresetConfig, showNewForm]);

  const addPreset = useCallback(() => {
    addPresetFromProps(newPresetConfig);
    setNewPresetConfig(null);
  }, [newPresetConfig, setNewPresetConfig, addPresetFromProps]);

  const updatePreset = useCallback(
    ({ presetId, udpatedPresetConfig }) => {
      if (newPresetConfig?.id === presetId) {
        setNewPresetConfig((prevPresetConfig) => ({
          ...prevPresetConfig,
          config: udpatedPresetConfig,
        }));
        return;
      }
      updatePresetFromProps({ presetId, udpatedPresetConfig });
    },
    [updatePresetFromProps, setNewPresetConfig, newPresetConfig],
  );

  const renderSelectedConfigForms = useCallback(
    () =>
      _map(adaptedPresetsList, (presetData, index) => (
        <ExpandablePresetConfigForm
          addPreset={addPreset}
          removePreset={removePreset}
          updatePreset={updatePreset}
          preset={presetData}
          index={index}
          key={presetData?.id}
        />
      )),
    [adaptedPresetsList, addPreset, removePreset, updatePreset],
  );

  const shouldShowCreateNewPresetButton = useMemo(
    () => selectedPresets.length === adaptedPresetsList.length,
    [selectedPresets, adaptedPresetsList],
  );

  useEffect(() => {
    if (_isEmpty(selectedPresets)) {
      createNewPreset();
    }
  }, [selectedPresets]);

  if (tag === PRACTICE_CATEGORIES.FLASH_ANZAN) {
    return <FlashAnzanConfigForm />;
  }
  if (
    tag === PRACTICE_CATEGORIES.ROOT ||
    tag === PRACTICE_CATEGORIES.EXPONENT ||
    tag === PRACTICE_CATEGORIES.MOD ||
    tag === PRACTICE_CATEGORIES.HCF ||
    tag === PRACTICE_CATEGORIES.LCM
  ) {
    return <PresetConfigForm category={categoryIdForSelectedTab} />;
  }

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.innerContainer}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
      >
        {renderSelectedConfigForms()}

        {shouldShowCreateNewPresetButton && (
          <TouchableOpacity
            onPress={createNewPreset}
            style={styles.addPresetButton}
          >
            <Text style={styles.presetButtonText}>Create new Preset +</Text>
          </TouchableOpacity>
        )}
        <UpdateConfigModal
          isVisible={showUpdateModal}
          onClose={onUnderStoodPressed}
        />
      </ScrollView>
    </View>
  );
};

ConfigurationForm.propTypes = {
  controller: PropTypes.object,
};

export default React.memo(ConfigurationForm);
