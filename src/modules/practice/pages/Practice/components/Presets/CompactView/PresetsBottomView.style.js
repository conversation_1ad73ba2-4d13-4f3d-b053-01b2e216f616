import { StyleSheet } from "react-native";
import dark from "../../../../../../../core/constants/themes/dark";

const styles = StyleSheet.create({
    overlayStyle: {
        position: "absolute",
        width: '100%',
        backgroundColor: "#1F1F1F",
        flex: 1,
        bottom: 60,
        padding: 0,
    },
    expandedOverlay: {
        borderTopLeftRadius: 25,
        borderTopRightRadius: 25,
    },
    expandedView: {
        backgroundColor: "#1F1F1F",
        minHeight: 200,
        // maxHeight: "50%",
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
        paddingHorizontal: 20,
        paddingVertical: 12,
        gap: 12,
    },
    categoryText: {
        lineHeight: 20,
        fontSize: 16,
        color: 'white',
        fontFamily: 'Montserrat-600'
    },
    sheetLook: {
        height: 4,
        width: 32,
        borderRadius: 2.5,
        backgroundColor: dark.colors.tertiary,
    },
    bottomSection :{
        justifyContent: "space-between", 
        flexDirection: "row", 
        height: 60, 
        alignItems: "center", 
        borderTopColor: dark.colors.primary, 
        borderTopWidth: 1, 
        paddingHorizontal: 16, 
        backgroundColor: dark.colors.gradientBackground
    },
    practiceButton :{
        width: 94, 
        height: 32, 
        borderRadius: 20, 
        justifyContent: "center", 
        alignItems: "center",
        backgroundColor: dark.colors.secondary 
    },
    selectedPresetImage:{
        backgroundColor: dark.colors.background, 
        height: 40, 
        marginLeft:-19,
        width: 40, 
        justifyContent: "center", 
        alignItems: 'center', 
        borderColor: dark.colors.tertiary, 
        borderRadius: 8, 
        borderWidth: 1
    },
    practiceText:{ 
        fontSize:14,
        color: dark.colors.background, 
        fontFamily: 'Montserrat-600' 
    },
    itemCountText: {
        lineHeight: 12,
        fontSize: 10,
        color: 'white',
        fontFamily: 'Montserrat-500'
    },
    itemStackDisplay:{
        alignItems:"center",
        flexDirection:"row",
        gap:8
    }
})

export default styles