import { StyleSheet } from "react-native";
import dark from "../../../../../../../core/constants/themes/dark";

const styles = StyleSheet.create({
    container: {
        flex: 2,
        paddingTop: 36,
        paddingLeft: 20,
        paddingRight: 36,
        borderLeftColor: dark.colors.tertiary,
        borderLeftWidth: 1,
    },
    header: {
        fontSize: 14,
        color: dark.colors.textDark,
        marginBottom: 10,
        fontFamily: "Montserrat-600"
    },
    practiceButton: {
        width: 90,
        height:34,
        justifyContent: "center",
        backgroundColor: dark.colors.secondary,
        borderRadius:24,
        alignItems: 'center',
        marginTop: 20,
    },
    practiceButtonText: {
        color: dark.colors.background,
        fontFamily: "Montserrat-500"
    },
});

export default styles
