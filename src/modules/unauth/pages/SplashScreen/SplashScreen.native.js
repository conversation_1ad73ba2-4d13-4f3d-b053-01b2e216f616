import { Dimensions, StyleSheet, View } from 'react-native';
import React, { useEffect } from 'react';
import { useRouter } from 'expo-router';
import Rive from 'atoms/Rive';

const { width } = Dimensions.get('window');

const SplashScreen = () => {
  const router = useRouter();

  useEffect(() => {
    const timer = setTimeout(() => {
      router.replace('/landing');
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <View style={styles.container}>
      <View style={styles.riveContainer}>
        <Rive
          resourceName="splash"
          stateMachineName="State Machine 1"
          autoPlay
          style={styles.animation}
          fallbackText="Loading Matiks..."
          style={{
            width: '175%',
            height: '100%',
          }}
          onStateChanged={(_, stateName) => {
            console.info('STATE', stateName);
          }}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, width: '100%' },
  riveContainer: {
    width,
    alignItems: 'center',
    justifyContent: 'flex-start',
    flex: 1,
    marginBottom: 0,
  },
});

export default SplashScreen;
