import { ScreenData, TextAnimationConfig, AnimationConstants } from '../components/OnboardingDetails/types';

export const ANIMATION_CONSTANTS: AnimationConstants = {
  animationDuration: 400,
  bottomStartPosition: 0,
  buttonAnimationDelay: 400,
  bottomStartLinearPosition: 0,
};

export const getButtonAnimationDelay = (currentAnimation: ScreenData): number => {
  return currentAnimation.resourceName === 'duels' ? 1000 : 400;
};

export const getTextAnimationConfig = (currentStep: number): TextAnimationConfig => ({
  duration: 800,
  startDelay: 200,
  direction: currentStep === 3 ? 'top-to-bottom' : 'bottom-to-top',
  distance: 50,
  afterAnimationDirection:
    currentStep === 2 || currentStep === 3
      ? 'top-to-bottom'
      : 'bottom-to-top',
  exitDistance: 50,
  exitDuration: 500,
  exitHorizontalOffset: currentStep === 2 ? 0 : -15,
});

export const shouldAnimateButtons = (
  buttonType: string | undefined,
  currentAnimation: ScreenData
): boolean => {
  return (
    buttonType === 'GET_STARTED' ||
    currentAnimation.resourceName === 'duels' ||
    currentAnimation?.buttonType === 'login'
  );
};

export const shouldAnimateText = (): boolean => {
  return true;
};

export const shouldAnimateLinearGradient = (
  isNewScreen: boolean
): boolean => {
  return isNewScreen;
};

export const getRiveAnimationDimensions = () => {
  const riveAnimationHeight = Math.min(
    400, // Increased max height
    400 * 0.6, // Increased to 50% of screen height
  );

  const riveAnimationWidth = Math.min(
    400, // Max width
    400, // 90% of screen width
  );

  return { riveAnimationHeight, riveAnimationWidth };
};

export const getInitialAnimationState = (
  screenData: ScreenData[],
  alreadyLogin?: string
): ScreenData => {
  if (alreadyLogin) {
    return screenData[screenData.length - 1];
  }
  return screenData[0];
};

export const getInitialStep = (
  screenData: ScreenData[],
  alreadyLogin?: string
): number => {
  if (alreadyLogin) {
    return screenData.length;
  }
  return 1;
};
