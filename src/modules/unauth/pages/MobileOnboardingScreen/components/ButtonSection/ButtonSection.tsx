import React, { useCallback, useState } from 'react';
import { Platform, View } from 'react-native';
import Animated from 'react-native-reanimated';

import AppleLogin from '@/src/core/oauth/components/AppleLogin';
import GoogleLoginButton from '@/src/core/oauth/components/GoogleLoginButton';
import { ICON_TYPES } from '@/src/components/atoms/Icon';
import InteractivePrimaryButton from 'atoms/InteractivePrimaryButton';
import SecondaryButton from '@/src/components/atoms/SecondaryButton';
import ProgressDots from '@/src/components/atoms/ProgressDots';

import { ButtonSectionProps } from '../OnboardingDetails/types';
import { ButtonType } from '../OnboardingDetails/types';
import styles from './ButtonSection.style';

const ButtonSection: React.FC<ButtonSectionProps> = ({
  buttonType,
  currentStep,
  lastScreen,
  currentAnimation,
  onGetStartedPress,
  onNextPress,
  onAlreadyLoginPress,
  nextOnPress,
  textAnimatedStyle,
}) => {
  const [isGetStartedDisabled, setIsGetStartedDisabled] = useState(false);

  const handleGetStartedPress = useCallback(() => {
    if (isGetStartedDisabled) return;
    setIsGetStartedDisabled(true);
    onGetStartedPress();
  }, [onGetStartedPress, isGetStartedDisabled]);
  const renderNewUserGetStartedButton = useCallback(
    () => (
      <View style={styles.getStartedButton}>
        <InteractivePrimaryButton
          label="GET STARTED"
          buttonContainerStyle={styles.newUserGetStartedButton}
          buttonStyle={styles.newUserGetStartedButtonStyle}
          labelStyle={styles.newUserGetStartedLabel}
          onPress={handleGetStartedPress}
          buttonBorderBackgroundStyle={styles.newUserGetStartedButtonBackground}
          trailingIconConfig={{
            name: 'arrow-right',
            type: ICON_TYPES.FONT_AWESOME_5,
            size: 15,
          }}
        />
      </View>
    ),
    [handleGetStartedPress, isGetStartedDisabled],
  );

  const renderNextButton = useCallback(
    () => (
      <View style={styles.nextButtonContainer}>
        <InteractivePrimaryButton
          label="NEXT"
          buttonContainerStyle={styles.newUserGetStartedButton}
          buttonStyle={styles.newUserGetStartedButtonStyle}
          labelStyle={styles.newUserGetStartedLabel}
          onPress={onNextPress}
          buttonBorderBackgroundStyle={styles.newUserGetStartedButtonBackground}
          trailingIconConfig={{
            name: 'arrow-right',
            type: ICON_TYPES.FONT_AWESOME_5,
            size: 15,
          }}
          buttonContentStyles={styles.nextButton}
          justifyContent="flex-start"
        />
        <View style={styles.progressDotsContainer}>
          <ProgressDots
            currentStep={currentStep}
            totalSteps={4}
            dotSize={12}
            spacing={4}
            activeColor="#00D9FF"
            inactiveColor="tranparent"
          />
        </View>
      </View>
    ),
    [onNextPress, currentStep],
  );

  const renderSocialLoginButtons = useCallback(
    () => (
      <Animated.View style={textAnimatedStyle}>
        <GoogleLoginButton onSuccess={nextOnPress} />
        {Platform.OS === 'ios' && (
          <View style={styles.appleLoginButton}>
            <AppleLogin onAppleLoginSuccess={nextOnPress} />
          </View>
        )}
      </Animated.View>
    ),
    [nextOnPress, textAnimatedStyle],
  );

  const renderGetStartedSection = useCallback(
    () => (
      <View style={styles.alreadyUserButton}>
        {renderNewUserGetStartedButton()}
        <SecondaryButton
          onPress={onAlreadyLoginPress}
          labelStyle={styles.alreadyUserTxt}
          label={'Already A user? Login'.toUpperCase()}
          buttonStyle={styles.alreadyUserBtn}
        />
      </View>
    ),
    [renderNewUserGetStartedButton, onAlreadyLoginPress],
  );

  return (
    <>
      {buttonType === ButtonType.GET_STARTED && renderGetStartedSection()}
      {buttonType === ButtonType.NEXT &&
        lastScreen + 1 !== currentStep &&
        renderNextButton()}
      {currentAnimation?.buttonType === 'login' && renderSocialLoginButtons()}
    </>
  );
};

export default React.memo(ButtonSection);
