import React from 'react';
import { View } from 'react-native';
import { Text } from '@rneui/themed';
import Animated from 'react-native-reanimated';
import _map from 'lodash/map';

import { ContentSectionProps } from '../OnboardingDetails/types';
import MatiksLogoSvg from '../OnboardingDetails/MatiksLogoSvg';
import styles from './ContentSection.style';

const ContentSection: React.FC<ContentSectionProps> = ({
  currentAnimation,
  shouldAnimateText,
  textAnimatedStyle,
}) => {
  if (!currentAnimation.title && !currentAnimation.subTitle) {
    return null;
  }

  return (
    <View style={styles.contentContainer}>
      <View style={styles.titleIcon}>
        {currentAnimation.resourceName === 'landing' && <MatiksLogoSvg />}
        {shouldAnimateText ? (
          <Animated.View style={textAnimatedStyle}>
            <Text
              style={[
                styles.contentTitle,
                {
                  letterSpacing:
                    currentAnimation.resourceName === 'landing' ? 4 : 1,
                },
              ]}
            >
              {currentAnimation?.title}
            </Text>
          </Animated.View>
        ) : (
          <Text style={styles.contentTitle}>{currentAnimation?.title}</Text>
        )}
      </View>
      {shouldAnimateText ? (
        <Animated.View style={textAnimatedStyle}>
          <Text style={styles.taglineText}>{currentAnimation?.subTitle}</Text>
        </Animated.View>
      ) : (
        <Text style={styles.taglineText}>{currentAnimation?.subTitle}</Text>
      )}
    </View>
  );
};

export default React.memo(ContentSection);
