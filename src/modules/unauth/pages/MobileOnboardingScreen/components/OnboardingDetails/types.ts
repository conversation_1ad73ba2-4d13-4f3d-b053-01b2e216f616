import * as React from 'react';

export const ButtonType = {
  GET_STARTED: 'GET_STARTED',
  NEXT: 'NEXT',
  SOCIAL: 'SOCIAL',
} as const;

export type ButtonTypeKeys = keyof typeof ButtonType;

export interface ScreenData {
  resourceName: string;
  title: string;
  subTitle: string;
  colouredTitle?: string;
  buttonType?: string;
  scale?: number;
}

export interface LandingDetailsProps {
  activeIndex?: number;
  title?: string;
  subTitle?: string;
  socialLogin?: boolean;
  buttonType?: string;
  alreadyUserOnpress?: () => void;
  getStartedOnPress?: () => void;
  nextOnPress?: () => void;
  showBackButton?: boolean;
  riveResourceName?: string;
  riveStateMachine?: string;
  riveBooleanState?: string;
  screenData: ScreenData[];
  loginAsGuest?: () => void;
  guestUserLoading?: boolean;
  alreadyLoginOnPress?: () => void;
  alreadyLogin?: string;
}

export interface TextAnimationConfig {
  duration: number;
  startDelay: number;
  direction: 'top-to-bottom' | 'bottom-to-top';
  distance: number;
  afterAnimationDirection: 'top-to-bottom' | 'bottom-to-top';
  exitDistance: number;
  exitDuration: number;
  exitHorizontalOffset: number;
}

export interface AnimationState {
  isTextExiting: boolean;
  currentStep: number;
  currentAnimation: ScreenData;
  shouldAnimate: boolean;
  shouldAnimateText: boolean;
  shouldAnimateLinearGradient: boolean;
}

export interface ButtonState {
  isNextButtonDisabled: boolean;
  isPreviousButtonDisabled: boolean;
}

export interface AnimationConstants {
  animationDuration: number;
  bottomStartPosition: number;
  buttonAnimationDelay: number;
  bottomStartLinearPosition: number;
}

export interface RiveAnimationProps {
  riveRef: React.RefObject<any>;
  currentStep: number;
  screenData: ScreenData[];
  alreadyLogin?: string;
  showBackButton?: boolean;
  onBackPress: () => void;
  isPreviousButtonDisabled: boolean;
}

export interface ContentSectionProps {
  currentAnimation: ScreenData;
  shouldAnimateText: boolean;
  textAnimatedStyle: any;
}

export interface ButtonSectionProps {
  buttonType?: string;
  currentStep: number;
  lastScreen: number;
  currentAnimation: ScreenData;
  onGetStartedPress: () => void;
  onNextPress: () => void;
  onAlreadyLoginPress: () => void;
  nextOnPress?: () => void;
  textAnimatedStyle: any;
}
