import React, { useCallback, useState } from 'react'
import { View, StyleSheet, Image, ImageBackground } from 'react-native'
import { Text } from '@rneui/themed'
import image from '@/assets/images/trackProgressFrame.png'
import APP_LAYOUT_CONSTANTS from "core/constants/appLayout";
import useMediaQuery from 'core/hooks/useMediaQuery'
import dark from '../../../../../core/constants/themes/dark';

const styles = StyleSheet.create({
    container: {
        width: '100%',
        marginHorizontal: 16,
        maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
        backgroundColor: dark.colors.primary,
        // height: 160,
        overflow: 'hidden',
        flexDirection: 'row',
        borderRadius: 16,
    },
    image: {
        resizeMode: 'cover',
        overflow: 'hidden',
        borderRadius: 16,
    },
    textContainer: {
        width: '52%',
        gap: 8,
    },
    title: {
        color: 'white',
        maxWidth: 300,
        fontSize: 20,
        fontFamily: 'Montserrat-700',
    },
    subtitle: {
        color: dark.colors.textDark,
        fontSize: 10,
    },
})

const TrackProgressSection = () => {
    const { isMobile } = useMediaQuery()
    const [containerWidth, setContainerWidth] = useState(0);
    const [containerHeight, setContainerHeight] = useState(0);

    const onContainerLayout = useCallback((event) => {
        const width = event.nativeEvent.layout.width;
        setContainerWidth(width);
        const height = (width * 393)/1010;
        setContainerHeight(height);
    }, []);

    return (
        <View style={styles.container} onLayout={onContainerLayout}>
        <ImageBackground source={image} resizeMode={'cover'} style={[styles.image, { flex: 1, width: containerWidth, height: containerHeight }]}>
                <View style={[styles.textContainer, { padding: isMobile ? 16 : 42, gap: isMobile ? 8 : 16 }]}>
                    <Text style={[styles.title, { fontSize: isMobile ? 24: 64 }]}>Track Your Progress</Text>
                    <Text style={[styles.subtitle, { fontSize: isMobile ? 10: 16 }]}>
                        See your performance in past games and track your performance every month.
                    </Text>
                </View>
        </ImageBackground>
        </View>
    )
}

export default React.memo(TrackProgressSection)
