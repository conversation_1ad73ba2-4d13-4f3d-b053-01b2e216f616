import { StyleSheet } from 'react-native'
import dark from '../../../../../../core/constants/themes/dark'

const styles = StyleSheet.create({
  appDownloadCard: {
    position: 'absolute',
    bottom: 16,
    right: 16,
  },
  appDownloadContentCard: {
    height: 86,
    width: 202,
    backgroundColor: dark.colors.primary,
    borderRadius: 4,
    borderWidth: 1,
    gap: 20,
    flexDirection: 'row',
    borderColor: '#2F2F2F',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 13,
  },
  downloadText: {
    fontSize: 14,
    color: 'white',
    fontFamily: 'Montserrat-600',
    lineHeight: 18,
    textAlign: 'center',
  },
  downloadTextBrandName: {
    fontSize: 20,
    color: 'white',
    fontFamily: 'Montserrat-700',
    lineHeight: 30,
    letterSpacing: 1,
    textAlign: 'center',
  },
})

export default styles
