import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  gradientBox: {
    width: '88%',
    borderRadius: 16,
    marginBottom: 5,
  },
  innerContainer: {
    borderRadius: 16,
    flex: 1,
    padding: 15,
    margin: 1,
    backgroundColor: '#2E2E2E',
    alignContent: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nameAndPosition: {
    display: 'flex',
    flexDirection: 'row',
    gap: 10,
  },
  itemContainer: {
    alignContent: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderColor: dark.colors.tertiary,
    borderWidth: 1,
    padding: 15,
    borderRadius: 16,
    marginVertical: 8,
    marginHorizontal: 15,
    backgroundColor: '#2E2E2E',
  },
  firstItem: {
    width: '86%',
    borderColor: '#F05F5F',
    borderWidth: 1,
  },
  secondItem: {
    borderColor: dark.colors.tertiary,
    width: '83%',
    borderWidth: 1,
  },
  thirdItem: {
    borderColor: dark.colors.tertiary,
    width: '80%',
    borderWidth: 1,
    shadowColor: 'white',
    shadowOffset: { width: 0, height: 9 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    backgroundColor: '#2F2F2F',
    transform: [{ translateY: 2 }],
  },
  position: {
    color: dark.colors.textLight,
    fontFamily: 'Montserrat-700',
    fontSize: 16,
  },
  name: {
    textAlign: 'left',
    maxWidth: 160,
    color: dark.colors.textLight,
    fontFamily: 'Montserrat-500',
    fontSize: 16,
  },
  score: {
    maxWidth: 100,
    color: dark.colors.textLight,
    fontFamily: 'Montserrat-500',
    fontSize: 16,
  },
});

export default styles;
