import React, { useCallback, useMemo } from 'react';
import { FlatList, Text, View } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import Loading from '@/src/components/atoms/Loading';
import _map from 'lodash/map';
import _find from 'lodash/find';
import _toString from 'lodash/toString';
import _padStart from 'lodash/padStart';
import useGetUserSubmissionInContest from '../../hooks/queries/useGetUserSubmissionInContest';
import styles from './ContestScoreAnalysis.style';
import { getStartTimeOfContestForUser } from '../../utils/contest';
import contestReader from 'core/readers/contestReader';
import ErrorView from 'atoms/ErrorView';
import _isNil from 'lodash/isNil';

const HEADER_ITEMS = ['Q No', 'Question', 'Time (sec:ms)'];

const ContestScoreAnalysis = ({
  submissionsListWithTimeTaken,
}: {
  submissionsListWithTimeTaken: any[];
}) => {
  const renderHeader = useCallback(
    () => (
      <View style={styles.headerContainer}>
        {_map(HEADER_ITEMS, (item, index) => (
          <View key={index} style={styles?.[`column${index + 1}`]}>
            <Text
              style={styles.headerLabel}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {item}
            </Text>
          </View>
        ))}
      </View>
    ),
    [],
  );

  const renderRow = useCallback(
    ({ item, index }: { item: any; index: number }) => (
      <View style={styles.rowContainer}>
        <View style={styles.column1}>
          <Text style={styles.rowLabel} numberOfLines={1} ellipsizeMode="tail">
            {index + 1}
          </Text>
        </View>
        <View style={styles.column2}>
          <Text style={styles.rowLabel} numberOfLines={1} ellipsizeMode="tail">
            {item.question.expression.join(' ')}
          </Text>
        </View>
        <View style={styles.column3}>
          <Text style={styles.rowLabel} numberOfLines={1} ellipsizeMode="tail">
            {item.submission ? item.timeTaken : '-'}
          </Text>
        </View>
      </View>
    ),
    [],
  );

  const renderSeparator = useCallback(
    () => <View style={styles.separator} />,
    [],
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={submissionsListWithTimeTaken}
        ListHeaderComponent={renderHeader()}
        renderItem={renderRow}
        keyExtractor={(item, index) => `${item.question.id}-${index}`}
        ItemSeparatorComponent={renderSeparator}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const ContestScoreAnalysisContainer = ({
  contestDetails,
}: {
  contestDetails: any;
}) => {
  const contestId = contestReader.id(contestDetails);
  const questionList = contestDetails?.questions;

  const { currentUserParticipation } = contestDetails;

  const { error, loading, refetch, userSubmission } =
    useGetUserSubmissionInContest({ contestId: contestId });

  const startTime = userSubmission?.startTime;

  const submissionsListWithTimeTaken = useMemo(() => {
    if (_isEmpty(userSubmission) || loading) return EMPTY_ARRAY;

    const { submissions } = userSubmission ?? EMPTY_OBJECT;

    if (_isEmpty(submissions)) return EMPTY_ARRAY;

    const defaultStartTime = getStartTimeOfContestForUser({
      contest: contestDetails,
      userSubmission,
    });

    return _map(questionList, (question, index) => {
      const submission = _find(
        submissions,
        (sub) => sub.questionId === question.question.id,
      );
      let timeTaken = '00:00';
      if (submission) {
        const submissionTime = new Date(submission.submissionTime).getTime();
        const previousTime =
          index === 0
            ? defaultStartTime
            : new Date(
                userSubmission.submissions[index - 1].submissionTime,
              ).getTime();
        const timeDiffInMilliSeconds = Math.floor(
          submissionTime - previousTime,
        );

        const seconds = _padStart(
          _toString(Math.floor(timeDiffInMilliSeconds / 1000)),
          2,
          '0',
        );
        const milliseconds = _padStart(
          _toString(Math.floor(timeDiffInMilliSeconds / 10) % 100),
          2,
          '0',
        );

        timeTaken = `${seconds}:${milliseconds}`;
      }
      return {
        ...question,
        submission,
        timeTaken,
      };
    });
  }, [questionList, userSubmission, startTime, contestDetails, loading]);

  if (_isEmpty(currentUserParticipation) || !_isNil(error)) {
    return (
      <ErrorView
        errorMessage="You have not registered for this contest"
        onRetry={refetch}
      />
    );
  }

  if (_isEmpty(submissionsListWithTimeTaken)) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No analysis data available</Text>
      </View>
    );
  }

  if (loading && _isEmpty(contestDetails)) {
    return <Loading label="Fetching User Analysis" />;
  }

  return (
    <ContestScoreAnalysis
      submissionsListWithTimeTaken={submissionsListWithTimeTaken}
    />
  );
};

export default React.memo(ContestScoreAnalysisContainer);
