import React, { useMemo } from 'react';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import _isNil from 'lodash/isNil';
import { Image, Text, View } from 'react-native';
import LinearGradient from 'atoms/LinearGradient';
import scoreIcon from 'assets/images/score.png';
import groupIcon from 'assets/images/group.png';
import timerIcon from 'assets/images/timer.png';
import { getFormattedTimeWithMS } from 'core/utils/general';
import CardWithoutCTA from 'shared/CardWithoutCTA';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import contestReader from 'core/readers/contestReader';
import dark from 'core/constants/themes/dark';
import TakeVirtualContestCard from '../TakeVirtualContestCard';
import { getTimeSpentByUser } from '../../utils/contest';
import UserImage from '../../../../components/atoms/UserImage';
import UserScoreCardShimmer from '../../shimmers/UserScoreCardShimmer';
import useGetUserRankInContest from '../../hooks/useGetUserRankInContest';
import styles from './CompactUserScoreCard.style';

const CompactUserScoreCard = ({ contest }: { contest: any }) => {
  const contestId = contestReader.id(contest);
  const endTime = contestReader.endTime(contest);

  const now = getCurrentTimeWithOffset();
  const { loading, error, userRankInfo } = useGetUserRankInContest({
    contestId,
  });

  const formattedTimeTaken = useMemo(() => {
    const timeTaken = getTimeSpentByUser({
      contest,
      participantSubmission: userRankInfo,
    });
    return getFormattedTimeWithMS(timeTaken);
  }, [contest, userRankInfo]);

  const hasEnded = now >= new Date(endTime).getTime();

  const { isVirtualParticipant, rank } = userRankInfo ?? EMPTY_OBJECT;

  if (loading && _isEmpty(userRankInfo)) {
    return <UserScoreCardShimmer />;
  }

  if (error) {
    return <View style={{ height: 20 }} />;
  }

  if (_isEqual(isVirtualParticipant, false) && _isNil(rank) && hasEnded) {
    return <TakeVirtualContestCard contestId={contestId} />;
  }

  if (_isEqual(isVirtualParticipant, true) && !_isNil(rank)) {
    return (
      <View style={{ marginHorizontal: 16, marginVertical: 20 }}>
        <CardWithoutCTA
          infoText={`Your Virtual Rank is ${rank}`}
          renderInfoIcon={() => (
            <Image source={scoreIcon} style={{ height: 14, width: 14 }} />
          )}
        />
      </View>
    );
  }

  if (_isNil(userRankInfo?.user)) {
    return null;
  }

  return (
    <LinearGradient
      colors={dark.colors.currentUserResultInContestColors}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      style={styles.gradientBox}
    >
      <View style={styles.rankCard}>
        <UserImage
          user={userRankInfo?.user}
          style={styles.userImage}
          rounded={false}
        />
        <View style={styles.rankInfo}>
          <Text style={styles.rankText}># {userRankInfo?.rank}</Text>
          <View style={styles.statsRow}>
            <View style={styles.statDisplay}>
              <Image source={scoreIcon} style={styles.iconStyle} />
              <Text style={styles.stat}>{userRankInfo?.questionsSolved}</Text>
            </View>
            <View style={styles.statDisplay}>
              <Image source={timerIcon} style={styles.iconStyle} />
              <Text style={styles.stat}>{formattedTimeTaken}</Text>
            </View>
            <View style={styles.statDisplay}>
              <Image source={groupIcon} style={styles.iconStyle} />
              <Text style={styles.stat}>{userRankInfo?.totalParticipants}</Text>
            </View>
          </View>
        </View>
        {/* <TouchableOpacity onPress={onShare} style={styles.shareButton}>
                <View style={{ flexDirection: 'row', gap: 10, justifyContent: 'center' }}>
                    <Text style={styles.shareText}>Share</Text>
                </View>
            </TouchableOpacity> */}
      </View>
    </LinearGradient>
  );
};

export default React.memo(CompactUserScoreCard);
