import React from 'react';

import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import CompactShowdownBanner from './CompactShowdownBanner/CompactShowdownBanner';
import ExpandedShowdownBanner from './ExpandedShowdownBanner/ExpandedShowdownBanner';


const ShowdownBannerLayout = (props) => {
    const { isMobile: isCompactDevice} = useMediaQuery();
  
    if(isCompactDevice){
      return <CompactShowdownBanner {...props}/> 
    }
  
    return <ExpandedShowdownBanner {...props}/>
  }
  
  export default React.memo(ShowdownBannerLayout);