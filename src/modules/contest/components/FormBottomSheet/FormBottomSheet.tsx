import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Modal,
  ScrollView,
  Switch,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import _reduce from 'lodash/reduce';
import _size from 'lodash/size';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import _isArray from 'lodash/isArray';
import _map from 'lodash/map';
import styles from './FormBottomSheet.style';
import { validateEmailField } from '../../utils/formValidator';
import { FIELD_TYPES } from '../../constants';

interface FormBottomSheetProps {
  isVisible: boolean;
  setIsVisible: (visible: boolean) => void;
  fields: any[];
  onSubmit: (formData: any) => void;
  isLoading: boolean;
  eventProperties: any;
}

const FormBottomSheet = ({
  isVisible,
  setIsVisible,
  fields,
  onSubmit,
  isLoading,
  eventProperties,
}: FormBottomSheetProps) => {
  const [formData, setFormData] = useState({});
  const [focusedField, setFocusedField] = useState(null);
  const firstInputRef = useRef(null);
  const bottomSheetRef = useRef(null);

  useEffect(() => {
    if (isVisible && firstInputRef.current) {
      firstInputRef?.current?.focus();
    }
  }, [isVisible, firstInputRef]);

  const handleInputChange = useCallback((name, value) => {
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  }, []);

  const adaptedFormData = useMemo(
    () =>
      _reduce(
        formData,
        (acc: any, value, key) => {
          acc.push({
            name: key,
            values: [value],
          });
          return acc;
        },
        [],
      ),
    [formData],
  );

  const closeBottomSheet = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.CONTEST.CLICKED_ON_BOTTOMSHEET_CANCEL, {
      ...eventProperties,
    });
    bottomSheetRef?.current?.close();
    setIsVisible(false);
  }, [setIsVisible, eventProperties]);

  const handleSubmit = useCallback(() => {
    let isValidForm = true;
    const allErrors = [];
    fields.forEach((field) => {
      if (field.type === FIELD_TYPES.EMAIL) {
        const { isValid, errors } = validateEmailField({
          field,
          value: formData?.[field?.name],
          suffix: field?.validation.emailSuffix,
          suffixes: field?.validation.emailSuffixes,
        });
        isValidForm = isValidForm && isValid;
        if (_size(errors) > 0) {
          allErrors.push(errors?.[0]);
          showToast({
            type: TOAST_TYPE.ERROR,
            description: `${errors?.[0]}`,
          });
        }
      }
      if (field.required && !formData?.[field?.name]) {
        isValidForm = false;
        showToast({
          type: TOAST_TYPE.ERROR,
          description: `${field?.label} is required`,
        });
      }
    });

    if (isValidForm) {
      onSubmit(adaptedFormData);
    }
  }, [fields, formData, adaptedFormData, onSubmit]);

  const headerContent = useMemo(
    () => (
      <View style={styles.header}>
        <Text style={styles.headerText}>Registration Details</Text>
        <TouchableOpacity onPress={closeBottomSheet}>
          <Text style={styles.closeIcon}>✕</Text>
        </TouchableOpacity>
      </View>
    ),
    [closeBottomSheet],
  );

  const buttonContainerContent = useMemo(
    () => (
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={closeBottomSheet}
        >
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.submitButton, isLoading && styles.disabledButton]}
          onPress={handleSubmit}
          disabled={isLoading}
        >
          <Text style={styles.submitButtonText}>
            {isLoading ? 'Registering...' : 'Register'}
          </Text>
        </TouchableOpacity>
      </View>
    ),
    [closeBottomSheet, handleSubmit, isLoading],
  );

  const renderField = useCallback(
    (field: any, index: number) => {
      const inputProps = {
        ref: index === 0 ? firstInputRef : null,
        style: [
          styles.inputField,
          focusedField === field.name && styles.focusedInputField,
        ],
        onFocus: () => setFocusedField(field.name),
        onBlur: () => setFocusedField(null),
        onChangeText: (value) => handleInputChange(field.name, value),
        placeholder: field.label,
        min: field.name,
        max: field.max,
      };

      let inputElement;
      switch (field.type) {
        case FIELD_TYPES.TEXT:
        case FIELD_TYPES.EMAIL:
          inputElement = (
            <TextInput
              {...inputProps}
              placeholderTextColor="grey"
              keyboardType={
                field.type === FIELD_TYPES.EMAIL ? 'email-address' : 'default'
              }
            />
          );
          break;
        case FIELD_TYPES.NUMBER:
        case FIELD_TYPES.MOBILE:
          inputElement = <TextInput {...inputProps} keyboardType="numeric" />;
          break;
        case FIELD_TYPES.SINGLE_SELECT:
          inputElement = (
            <Picker
              selectedValue={formData[field?.name]}
              onValueChange={(value) => handleInputChange(field.name, value)}
            >
              {field.options.map((option, idx) => (
                <Picker.Item key={idx} label={option} value={option} />
              ))}
            </Picker>
          );
          break;
        case FIELD_TYPES.CHECKBOX:
          inputElement = (
            <Switch
              value={formData[field?.name] || false}
              onValueChange={(value) => handleInputChange(field.name, value)}
            />
          );
          break;
        default:
          inputElement = <TextInput {...inputProps} />;
      }

      return (
        <View key={index} style={styles.inputContainer}>
          <View style={styles.inputLabelBox}>
            <Text style={styles.inputLabel}>{field.label}</Text>
            {field.required && (
              <Text style={styles.inputLabelMandatory}>*</Text>
            )}
          </View>
          {inputElement}
        </View>
      );
    },
    [focusedField, handleInputChange, firstInputRef, formData],
  );

  const renderFields = useCallback(() => {
    if (!_isArray(fields)) {
      return null;
    }
    return _map(fields, renderField);
  }, [fields, renderField]);

  return (
    <Modal
      style={{ marginTop: 30 }}
      visible={isVisible}
      animationType="slide"
      transparent={false}
      onRequestClose={() => setIsVisible(false)}
    >
      <View
        style={[
          styles.bottomSheetContent,
          { flex: 1, justifyContent: 'flex-end' },
        ]}
      >
        {headerContent}
        <ScrollView
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
        >
          {renderFields()}
        </ScrollView>
        {buttonContainerContent}
      </View>
    </Modal>
  );
};

export default React.memo(FormBottomSheet);
