import React, { useEffect, useState } from 'react';
import { Text, View } from 'react-native';
import styles from './CountdownTimer.style';

interface CountdownTimerProps {
  targetDate: Date;
  showOnlyDays?: boolean;
  prefix?: string;
}

function calculateTimeLeft(targetTime: number) {
  const difference = targetTime - new Date().getTime();
  if (difference <= 0) {
    return { days: 0, hours: 0, minutes: 0, seconds: 0 };
  }

  return {
    days: Math.floor(difference / (1000 * 60 * 60 * 24)),
    hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
    minutes: Math.floor((difference / 1000 / 60) % 60),
    seconds: Math.floor((difference / 1000) % 60),
  };
}

const CountdownTimer = ({
  targetDate,
  showOnlyDays = false,
  prefix = 'Starts in',
}: CountdownTimerProps) => {
  const targetTime = new Date(targetDate).getTime();

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft(targetTime));

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft(targetTime));
    }, 1000);

    return () => clearInterval(timer);
  }, [targetTime]);

  const formatTime = (time: number) => (time < 10 ? `0${time}` : time);

  const formatTimeLeft = () => {
    if (timeLeft.days > 0) {
      return `${timeLeft.days} days`;
    }
    if (timeLeft.hours > 0) {
      return `${formatTime(timeLeft.hours)} hrs`;
    }
    if (timeLeft.minutes > 0) {
      return `${formatTime(timeLeft.minutes)} mins`;
    }
    if (timeLeft.seconds > 0) {
      return `${formatTime(timeLeft.seconds)} secs`;
    }
    return null;
  };

  if (showOnlyDays) {
    return <Text style={styles.daysLeftDetails}>{formatTimeLeft()}</Text>;
  }

  const label =
    timeLeft.days > 0
      ? `${prefix} ${timeLeft.days} days`
      : `${prefix} ${formatTime(timeLeft.hours)}:${formatTime(timeLeft.minutes)}:${formatTime(timeLeft.seconds)}`;

  return (
    <View style={styles.startsInBox}>
      <Text style={styles.startsInText}>{label}</Text>
    </View>
  );
};

export default React.memo(CountdownTimer);
