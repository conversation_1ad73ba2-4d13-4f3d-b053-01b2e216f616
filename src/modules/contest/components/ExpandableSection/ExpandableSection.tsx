import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import HtmlRenderer from 'atoms/HtmlRenderer';
import styles from './ExpandableSection.style';
import dark from 'core/constants/themes/dark';

interface ExpandableSectionProps {
  title: string;
  expanded: boolean;
  onToggle: () => void;
  content: string;
}

const ExpandableSection = ({
  title,
  expanded,
  onToggle,
  content,
}: ExpandableSectionProps) => (
  <View>
    <TouchableOpacity style={styles.expandableHeader} onPress={onToggle}>
      <Text style={styles.expandableText}>{title}</Text>
      <Icon
        name={expanded ? 'chevron-up-outline' : 'chevron-down-outline'}
        size={15}
        color={dark.colors.secondary}
      />
    </TouchableOpacity>
    {expanded && (
      <View style={styles.expandedContent}>
        <HtmlRenderer html={content} />
      </View>
    )}
  </View>
);

export default React.memo(ExpandableSection);
