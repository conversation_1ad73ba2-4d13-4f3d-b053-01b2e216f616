import React, { useCallback } from 'react';
import { Text, View } from 'react-native';
import Header from 'shared/Header';
import PlaceholderRow from 'shared/PlaceholderRow';
import userReader from 'core/readers/userReader';
import { getFormattedTimeWithMS } from 'core/utils/general';
import dark from 'core/constants/themes/dark';
import PaginatedList from 'shared/PaginatedList';
import contestReader from 'core/readers/contestReader';
import _get from 'lodash/get';
import styles from './CompactLeaderboardPage.style';
import { getTimeSpentByUser } from '../../utils/contest';
import useGetContestLeaderboard from '../../hooks/useGetContestLeaderboard';
import { CONTEST_STATUS } from '../../constants/index';

const PAGE_SIZE = 50;

const CompactLeaderboardPage = ({ contest }: { contest: any }) => {
  const contestStatus = contestReader.status(contest);
  const isContestLive = contestStatus === CONTEST_STATUS.ONGOING;

  const contestId = contestReader.id(contest);

  const { fetchLeaderboard } = useGetContestLeaderboard({
    contestId,
    isLive: isContestLive,
    pageSize: PAGE_SIZE,
  });

  const fetchData = useCallback(
    async ({ pageNumber }: { pageNumber: number }) => {
      const response = await fetchLeaderboard({ pageNumber });
      const { data } = response ?? EMPTY_OBJECT;
      const { getContestLeaderboard: participantsObject } =
        data ?? EMPTY_OBJECT;
      const { participants, totalParticipants } =
        participantsObject ?? EMPTY_OBJECT;
      return { data: participants, totalItems: totalParticipants };
    },
    [fetchLeaderboard],
  );

  const renderParticipant = useCallback(
    ({ item }: { item: any }) => {
      const timeSpent = getTimeSpentByUser({
        contest,
        participantSubmission: item,
      });

      const formattedTime = getFormattedTimeWithMS(timeSpent);
      const rankOfParticipant = _get(item, 'rank', '-');
      const scoreOfParticipant = _get(item, 'score', '-');
      const participant = _get(item, 'user', EMPTY_OBJECT);
      const userName = userReader.username(participant);

      return (
        <View style={styles.row}>
          <Text style={styles.rank}>{rankOfParticipant}</Text>
          <Text style={styles.name} numberOfLines={1}>
            {userName}
          </Text>
          <Text style={styles.score} numberOfLines={1}>
            {scoreOfParticipant}
          </Text>
          <Text style={styles.time} numberOfLines={1}>
            {formattedTime}
          </Text>
        </View>
      );
    },
    [contest],
  );

  const renderHeader = useCallback(
    () => (
      <View style={styles.row}>
        <Text style={[styles.rank, { color: dark.colors.textDark }]}>#</Text>
        <Text
          style={[styles.name, { color: dark.colors.textDark }]}
          numberOfLines={1}
        >
          Name
        </Text>
        <Text style={[styles.score, { color: dark.colors.textDark }]}>
          Score
        </Text>
        <Text style={[styles.time, { color: dark.colors.textDark }]}>Time</Text>
      </View>
    ),
    [],
  );

  const renderPlaceHolder = useCallback(
    () => (
      <View>
        <PlaceholderRow />
        <PlaceholderRow />
        <PlaceholderRow />
      </View>
    ),
    [],
  );

  return (
    <View style={{ flex: 1, width: '100%' }}>
      <Header title={contest?.name} />
      <View style={{ paddingHorizontal: 16, flex: 1 }}>
        <PaginatedList
          placeholderComponent={renderPlaceHolder}
          fetchData={fetchData}
          renderItem={renderParticipant}
          renderHeader={renderHeader}
          pageSize={PAGE_SIZE}
          keyExtractor={(item, index) =>
            `${userReader.id(item?.user)} - ${index}`
          }
          contentContainerStyle={styles.list}
          listFooterComponent={<View style={{ height: 80 }} />}
        />
      </View>
    </View>
  );
};

export default React.memo(CompactLeaderboardPage);
