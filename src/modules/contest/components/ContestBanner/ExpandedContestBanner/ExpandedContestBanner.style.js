import { StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';
import { withOpacity } from 'core/utils/colorUtils';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    container: {
      flex: 1,
      padding: isCompactMode ? 10 : 16,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    contentContainer: {
      flex: 1,
      flexDirection: 'column',
      alignItems: 'flex-start',
      gap: 0,
      justifyContent: 'flex-start',
    },
    iconImage: {
      width: isCompactMode ? 75 : 90,
      height: isCompactMode ? 75 : 90,
    },
    startsInText: {
      color: '#ece5fc',
      fontFamily: 'Montserrat-500',
      fontSize: isCompactMode ? 8 : 10,
    },
    text80In8: {
      color: '#cfbbfc',
      fontFamily: 'Montserrat-800',
      lineHeight: isCompactMode ? 30 : 40,
      fontSize: isCompactMode ? 28 : 36,
    },
    text80In8Live: {
      color: '#cfbbfc',
      fontFamily: 'Montserrat-800',
      lineHeight: isCompactMode ? 22 : 26,
      fontSize: isCompactMode ? 20 : 24,
    },
    startsInDescription: {
      color: '#ece5fc',
      fontFamily: 'Montserrat-500',
      fontSize: isCompactMode ? 8 : 10,
    },
    registerNowButton: {
      paddingHorizontal: 10,
      backgroundColor: '#8F60F8',
      borderRadius: 20,
      height: 18,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: isCompactMode ? 6 : 8,
    },
    registerNowText: {
      color: '#fff',
      fontFamily: 'Montserrat-500',
      fontSize: 8,
    },

    // live
    timerContainer: {
      flexDirection: 'row',
      gap: isCompactMode ? 6 : 8,
    },
    singleTimerContainer: {
      gap: 2,
      alignItems: 'center',
      flexDirection: 'column',
    },
    timerValueContainer: {
      marginTop: 4,
      borderWidth: 1,
      borderRadius: 6,
      borderColor: '#8F60F8',
      width: 36,
      backgroundColor: withOpacity('#ece5fc', 0.25),
      padding: 4,
    },
    timerValue: {
      color: 'white',
      fontSize: 14,
      fontFamily: 'Montserrat-800',
      lineHeight: 14,
      textAlign: 'center',
    },
    timerLabel: {
      fontSize: 6,
      fontFamily: 'Montserrat-500',
      color: 'white',
      textAlign: 'center',
    },
    colonContainer: {
      alignItems: 'center',
      justifyContent: 'flex-start',
      marginTop: 10,
      gap: 3,
    },
    colonDot: {
      backgroundColor: 'white',
      height: 4,
      width: 4,
      minWidth: 4,
      minHeight: 4,
      borderRadius: 2,
    },
  });

const useExpandedContestBannerStyles = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(() => createStyles(isMobile), [isMobile]);

  return styles;
};

export default useExpandedContestBannerStyles;
