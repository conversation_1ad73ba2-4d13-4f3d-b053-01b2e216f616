import React from 'react';

import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import ExpandedContestBanner from './ExpandedContestBanner/ExpandedContestBanner';
import CompactContestBanner from './CompactContestBanner/CompactContestBanner';


const ContestBannerLayout = (props) => {
  const { isMobile: isCompactDevice} = useMediaQuery();

  if(isCompactDevice){
    return <CompactContestBanner {...props}/> 
  }

  return <ExpandedContestBanner {...props}/>
}

export default React.memo(ContestBannerLayout);