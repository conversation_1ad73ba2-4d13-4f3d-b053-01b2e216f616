import { gql, useQuery } from '@apollo/client';
import _includes from 'lodash/includes';

const GET_CONTEST_BY_STATUS = gql`
  query GetContestByStatusQuery(
    $statuses: [ContestStatus!]!
    $sortDirection: String
  ) {
    getContestsByStatus(statuses: $statuses, sortDirection: $sortDirection) {
      contests {
        _id
        name
        description
        startTime
        endTime
        contestDuration
        hostedByV2 {
          name
          logo
        }
        registrationStartTime
        registrationEndTime
        status
        currentUserParticipation {
          contestId
          userId
          registrationData {
            name
            values
          }
          score
          lastSubmissionTime
        }
      }
      totalCount
    }
  }
`;

const useGetContestByStatus = ({ statuses }: { statuses: string[] }) => {
  const { data, error, loading, refetch } = useQuery(GET_CONTEST_BY_STATUS, {
    variables: {
      statuses,
      sortDirection: _includes(statuses, 'ENDED') ? 'DESC' : 'ASC',
    },
  });

  return {
    contestDetail: data?.getContestsByStatus,
    error,
    loading,
    refetch,
  };
};

export default useGetContestByStatus;
