import { router } from 'expo-router'
import _includes from 'lodash/includes'

export const CONTESTS_TYPE_TAB_KEYS = {
  SHOWDOWNS: 'showdowns',
  CONTESTS: 'contests',
  LEAGUES: 'leagues',
}

const CONTESTS_TYPE_TAB_KEYS_LIST = [
  CONTESTS_TYPE_TAB_KEYS.CONTESTS,
  CONTESTS_TYPE_TAB_KEYS.SHOWDOWNS,
  CONTESTS_TYPE_TAB_KEYS.LEAGUES,
]

export const checkIsValidContestTypeTab = (tab) => {
  return _includes(CONTESTS_TYPE_TAB_KEYS_LIST, tab)
}
