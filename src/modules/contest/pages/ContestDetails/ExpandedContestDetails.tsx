import { View } from 'react-native-animatable';
import _isNil from 'lodash/isNil';
import React from 'react';
import WithClubAccess from '@/src/modules/clubs/components/WithClubAccess';
import ContestDetailCard from '../../components/ContestDetailCard';
import ContestTabBar from '../../components/ContestExpandedTabBar';
import ContestDetailsRightPane from './ContestRightPane';

interface ExpandedContestDetailsProps {
  contestDetails: any;
  loading: boolean;
  error: any;
  refetch: Function;
}

const ExpandedContestDetails = ({
  contestDetails,
  loading,
  error,
  refetch,
}: ExpandedContestDetailsProps) => (
  <View
    style={{
      flex: 1,
      flexDirection: 'row',
      paddingHorizontal: 36,
      paddingTop: 28,
    }}
  >
    <View style={{ width: '75%', flex: 1 }}>
      <ContestDetailCard
        contestId={contestDetails?._id}
        contestDetails={contestDetails}
        loading={loading}
        error={error}
        refetch={refetch}
      />
      <View style={{ flex: 1 }}>
        <ContestTabBar
          contestDetails={contestDetails}
          loading={loading}
          error={error}
          refetch={refetch}
        />
      </View>
    </View>
    <ContestDetailsRightPane
      contestDetails={contestDetails}
      loading={loading}
      error={error}
      refetch={refetch}
    />
  </View>
);

const ExpandedContestDetailsContainer = (props: any) => {
  const { contestDetails } = props ?? EMPTY_OBJECT;
  const { clubId } = contestDetails ?? EMPTY_OBJECT;

  if (!_isNil(clubId)) {
    return (
      <WithClubAccess clubId={clubId}>
        <ExpandedContestDetails {...props} />
      </WithClubAccess>
    );
  }

  return <ExpandedContestDetails {...props} />;
};

export default React.memo(ExpandedContestDetailsContainer);
