import { StyleSheet } from 'react-native';
import { useMemo } from 'react';
import APP_LAYOUT_CONSTANTS from 'core/constants/appLayout';
import dark from 'core/constants/themes/dark';
import useMediaQuery from 'core/hooks/useMediaQuery';

const createStyles = (isCompactMode: boolean) =>
  StyleSheet.create({
    container: {
      maxWidth: isCompactMode
        ? '100%'
        : APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
      flex: 1,
      overflow: 'hidden',
      gap: isCompactMode ? 10 : 48,
    },
    congratsText: {
      fontSize: isCompactMode ? 26 : 36,
      textAlign: 'center',
    },
    contestStatContainer: {
      borderRadius: 20,
      borderWidth: 1,
      borderColor: dark.colors.primary,
      paddingHorizontal: 20,
      paddingVertical: 16,
      minWidth: !isCompactMode ? 248 : '45%',
      height: isCompactMode ? 'auto' : 111,
      justifyContent: 'center',
      gap: isCompactMode ? 4 : 4,
    },
    statValue: {
      fontSize: isCompactMode ? 30 : 48,
      color: 'white',
      fontFamily: 'Montserrat-700',
    },
    statLabel: {
      fontSize: isCompactMode ? 12 : 16,
      lineHeight: 20,
      color: dark.colors.textDark,
      fontFamily: 'Montserrat-400',
    },
    statsContainer: {
      marginHorizontal: isCompactMode ? 16 : 0,
      marginVertical: isCompactMode ? 15 : 0,
      flexDirection: 'row',
      justifyContent: isCompactMode ? 'space-between' : 'flex-start',
      gap: isCompactMode ? 10 : 24,
    },
    errorContainer: {
      alignItems: 'center',
      gap: 24,
    },
    errorMessage: {
      fontSize: 24,
      color: 'white',
    },
    congratsOrNotAttemptedText: {
      fontSize: isCompactMode ? 26 : 36,
      fontFamily: 'Montserrat-700',
      marginTop: isCompactMode ? 0 : 20,
      textAlign: !isCompactMode ? 'left' : 'center',
    },
  });

const useContestCompletionStyles = () => {
  const { isMobile } = useMediaQuery();

  return useMemo(() => createStyles(isMobile), [isMobile]);
};

export default useContestCompletionStyles;
