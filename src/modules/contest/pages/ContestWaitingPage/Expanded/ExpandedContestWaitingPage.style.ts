import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    alignItems: 'center',
    display: 'flex',
    gap: 15,
    marginTop: 10,
  },
  title: {
    textAlign: 'center',
    color: 'white',
    fontSize: 18,
    fontFamily: 'Montserrat-700',
  },
  infoRow: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 32,
  },
  infoBox: {
    gap: 10,
  },
  fabText: {
    fontSize: 16,
    fontFamily: 'Montserrat-600',
    color: dark.colors.secondary,
  },
  infoTitle: {
    color: dark.colors.textDark,
    fontSize: 14,
    fontFamily: 'Montserrat-400',
  },
  infoDetails: {
    color: dark.colors.textLight,
    fontSize: 15,
    fontFamily: 'Montserrat-700',
  },
  gradientBox: {
    width: 55,
    height: 55,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  hostDetail: {
    display: 'flex',
    flexDirection: 'column',
    gap: 5,
    marginBottom: 20,
  },
  hostedBy: {
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-400',
    fontSize: 14,
  },
  hostName: {
    textAlign: 'center',
    fontFamily: 'Montserrat-700',
    color: dark.colors.textDark,
    fontSize: 14,
  },
  timerContainer: {
    gap: 36,
  },
  timerContentContainer: {
    borderBottomColor: dark.colors.tertiary,
    borderBottomWidth: 2,
    borderTopColor: dark.colors.tertiary,
    borderTopWidth: 2,
    width: '100%',
    paddingVertical: 24,
    flexDirection: 'row',
    marginTop: 25,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  startingIn: {
    fontSize: 24,
    fontFamily: 'Montserrat-700',
    color: 'white',
  },
  leaveChallengeLabel: {
    fontSize: 32,
    textAlign: 'center',
    fontFamily: 'Montserrat-700',
    color: '#FA9191',
  },
});

export default styles;
