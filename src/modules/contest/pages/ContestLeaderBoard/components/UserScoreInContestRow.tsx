import _isEqual from 'lodash/isEqual';
import _isNil from 'lodash/isNil';
import React, { useMemo } from 'react';
import { Image, Text, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import UserImage from 'atoms/UserImage';
import scoreIcon from 'assets/images/score.png';
import styles from './ContestLeaderboardRow/ContestLeaderboardRow.style';
import useGetUserRankInContest from '../../../hooks/useGetUserRankInContest';
import UserScoreInContestRowShimmer from '../../../shimmers/UserScoreInContestRowShimmer';
import { getFormattedTimeWithMS } from 'core/utils/general';
import { getTimeSpentByUser } from '../../../utils/contest';
import CardWithoutCTA from 'shared/CardWithoutCTA';
import TakeVirtualContestCard from '../../../components/TakeVirtualContestCard/TakeVirtualContestCard';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import contestReader from 'core/readers/contestReader';

interface UserScoreInContestRowProps {
  contest: any;
  userRankInfo: any;
}

const UserScoreInContestRow = ({
  contest,
  userRankInfo,
}: UserScoreInContestRowProps) => {
  const { isMobile: isCompactMode } = useMediaQuery();

  const formattedTimeTaken = useMemo(() => {
    const timeTaken = getTimeSpentByUser({
      contest,
      participantSubmission: userRankInfo,
    });
    return getFormattedTimeWithMS(timeTaken);
  }, [contest, userRankInfo]);

  return (
    <View
      style={[
        styles.userScoreRowContainer,
        isCompactMode && styles.compactRowContainer,
      ]}
      key={userRankInfo?._id}
    >
      <View style={styles.rankColumn}>
        <Text style={[styles.rowLabel]}>{userRankInfo.rank}</Text>
      </View>

      <View style={styles.profileInfoColumn}>
        {!isCompactMode && (
          <UserImage user={userRankInfo.user} size={isCompactMode ? 24 : 30} />
        )}
        <View
          style={[
            styles.usernameContainer,
            isCompactMode && styles.usernameContainerCompact,
          ]}
        >
          <Text
            style={[styles.rowLabel, isCompactMode && styles.rowLabelCompact]}
            numberOfLines={1}
          >
            {'You'}
          </Text>
          <Text style={[styles.rating, isCompactMode && styles.ratingCompact]}>
            ({userRankInfo?.user?.rating})
          </Text>
        </View>
      </View>

      {!isCompactMode && (
        <View style={styles.correctSubmissionColumn}>
          <Text
            style={[styles.rowLabel, isCompactMode && styles.rowLabelCompact]}
          >
            {userRankInfo.correctSubmission}
          </Text>
        </View>
      )}
      <View style={styles.scoreColumn}>
        <Text
          style={[styles.rowLabel, isCompactMode && styles.rowLabelCompact]}
        >
          {userRankInfo?.totalScore}
        </Text>
      </View>
      {!isCompactMode && (
        <View style={styles.timeTakenColumn}>
          <Text
            style={[styles.rowLabel, isCompactMode && styles.rowLabelCompact]}
          >
            {formattedTimeTaken}
          </Text>
        </View>
      )}
    </View>
  );
};

const UserScoreInContestRowContainer = ({ contest }: { contest: any }) => {
  const now = getCurrentTimeWithOffset();
  const contestId = contestReader.id(contest);
  const endTime = contestReader.endTime(contest);

  const hasEnded = now >= new Date(endTime).getTime();

  const { loading, error, userRankInfo } = useGetUserRankInContest({
    contestId,
  });

  const { isVirtualParticipant, rank } = userRankInfo ?? EMPTY_OBJECT;

  if (loading) {
    return <UserScoreInContestRowShimmer />;
  }

  if (error) {
    return null;
  }

  if (_isEqual(isVirtualParticipant, false) && _isNil(rank) && hasEnded) {
    return <TakeVirtualContestCard contestId={contestId} />;
  }

  if (_isEqual(isVirtualParticipant, true) && !_isNil(rank)) {
    return (
      <CardWithoutCTA
        infoText={`Your Virtual Rank is ${rank}`}
        renderInfoIcon={() => (
          <Image source={scoreIcon} style={{ height: 14, width: 14 }} />
        )}
      />
    );
  }

  if (_isNil(userRankInfo?.user)) {
    return null;
  }

  return (
    <UserScoreInContestRow contest={contest} userRankInfo={userRankInfo} />
  );
};

export default React.memo(UserScoreInContestRowContainer);
