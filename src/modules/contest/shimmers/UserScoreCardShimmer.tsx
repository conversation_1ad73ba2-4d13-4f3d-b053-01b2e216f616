import React, { useEffect } from 'react';
import { Animated, Dimensions, View } from 'react-native';
import useContestBannerShimmerStyles from './ContestBannerShimmer.style';
import LinearGradient from 'atoms/LinearGradient';

const UserScoreCardShimmer = () => {
  const { width } = Dimensions.get('window');
  const animatedValue = new Animated.Value(0);
  const styles = useContestBannerShimmerStyles();

  useEffect(() => {
    Animated.loop(
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      }),
    ).start();
  }, []);

  const translateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [-width, width],
  });

  return (
    <View
      style={{
        borderWidth: 1,
        borderColor: '#D7C6FD',
        borderRadius: 16,
        padding: 16,
        height: 100,
        marginVertical: 15,
        marginHorizontal: 15,
        width: '92%',
        alignSelf: 'center',
        overflow: 'hidden',
      }}
    >
      <Animated.View
        style={[
          styles.shimmerOverlay,
          {
            transform: [{ translateX }],
          },
        ]}
      >
        <LinearGradient
          colors={[
            'rgba(255, 255, 255, 0)',
            'rgba(255, 255, 255, 0.3)',
            'rgba(255, 255, 255, 0)',
          ]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={{ flex: 1 }}
        />
      </Animated.View>
    </View>
  );
};

export default UserScoreCardShimmer;
