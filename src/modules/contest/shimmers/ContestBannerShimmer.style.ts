import { StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    card: {
      borderWidth: 1,
      borderColor: '#D7C6FD',
      borderRadius: 16,
      padding: 16,
      marginVertical: 15,
      width: '100%',
      marginLeft: 0,
      alignSelf: 'center',
      overflow: 'hidden',
    },
    contentContainer: {
      position: 'relative',
      zIndex: 1,
    },
    header: {
      height: 24,
      width: '30%',
      marginBottom: 10,
      backgroundColor: 'rgba(215, 198, 253, 0.3)',
      borderRadius: 4,
    },
    description: {
      marginBottom: 15,
    },
    descriptionLine: {
      height: 20,
      width: '70%',
      marginBottom: 5,
      backgroundColor: 'rgba(215, 198, 253, 0.3)',
      borderRadius: 4,
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    footerLeft: {
      flex: 1,
      gap: 20,
      flexDirection: 'row',
      alignContent: 'flex-start',
      alignItems: 'flex-start',
    },
    footerLeftText: {
      height: 20,
      width: '40%',
      backgroundColor: 'rgba(215, 198, 253, 0.3)',
      borderRadius: 4,
      marginBottom: 8,
    },
    participants: {
      flexDirection: 'row',
      marginTop: 8,
      marginLeft: -10,
    },
    participant: {
      height: 24,
      width: 24,
      borderRadius: 12,
      marginRight: 5,
      backgroundColor: 'rgba(215, 198, 253, 0.3)',
    },
    button: {
      width: 130,
      height: 36,
      borderRadius: 20,
      backgroundColor: 'rgba(215, 198, 253, 0.3)',
    },
    shimmerOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 2,
    },
  });

const useContestBannerShimmerStyles = () => {
  const { isMobile } = useMediaQuery();

  return useMemo(() => createStyles(isMobile), [isMobile]);
};

export default useContestBannerShimmerStyles;
