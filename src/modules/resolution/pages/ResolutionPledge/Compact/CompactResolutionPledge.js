import _map from "lodash/map"
import _isEqual from "lodash/isEqual"
import React from "react"
import { View, Text, Image, Dimensions, ImageBackground } from "react-native"
import matiksLogo from 'assets/images/LinearGradientIcons/matiksBolt.png'
import useResolutionPledgePageStyles from "./../ResolutionPledge.style"
import PLEDGE_DURATIONS_WITH_INFO from "../../../constants/pledgesDurations"
import DailyActiveTimeTargetSelection from "../../../components/DailyActiveTimeTargetSelection"
import PrimaryButton from "atoms/PrimaryButton"
import BackgroundImage from "assets/images/backgrounds/background_blocks.png"
import Rive from 'atoms/Rive';
import PropTypes from "prop-types"

const CompactResolutionPledgePage = (props) => {
    const styles = useResolutionPledgePageStyles()

    const { onDurationChange, selectedDuration, animationUrl, onSubmitPress, isSubmiting } = props

    return (
        <ImageBackground style={styles.container} source={BackgroundImage}>
            <View style={styles.innerContainer}>
                <View>
                    <View style={styles.headerRow}>
                        <Image style={styles.matiksLogo} source={matiksLogo} />
                    </View>
                    <View style={{ paddingHorizontal: 32, paddingVertical: 10 }}>
                        <Text style={styles.titleText}>
                            LET’S BEAT "BRAIN ROT" IN 2025!
                        </Text>
                        <Text style={styles.subTitleText}>
                            NEW YEAR, SHARPER MIND
                        </Text>
                    </View>
                </View>
                <View style={{ width: "100%", justifyContent: "center", alignItems: "center" }}>
                    <Rive
                        url={animationUrl}
                        autoPlay={true}
                        style={{ width: 300, height: 160 }}
                    />

                </View>
                <View style={{ gap: 32, marginBottom: 10 }}>
                    <View style={{ gap: 10 }}>
                        <Text style={styles.queText}>
                            How much time will you devote to your goal?
                        </Text>
                        <View style={styles.durationSelectionContainer}>
                            {
                                _map(PLEDGE_DURATIONS_WITH_INFO, (pledgeInfo) => <DailyActiveTimeTargetSelection
                                    {...pledgeInfo}
                                    isSelected={_isEqual(selectedDuration, pledgeInfo.duration)}
                                    onPress={() => onDurationChange({ duration: pledgeInfo.duration })}
                                />)
                            }
                        </View>
                    </View>

                    <PrimaryButton
                        onPress={onSubmitPress}
                        label={isSubmiting ? "Commiting...." : "Commit to the goal"}
                        radius={50}
                        buttonStyle={{ height: 40, width: Dimensions.get("window").width - 32 }} />
                </View>
            </View>
        </ImageBackground>
    )
}

CompactResolutionPledgePage.propTypes = {
    animationUrl: PropTypes.any,
    isSubmiting: PropTypes.bool,
    onDurationChange: PropTypes.func,
    selectedDuration: PropTypes.number,
    onSubmitPress: PropTypes.func
}

export default React.memo(CompactResolutionPledgePage)