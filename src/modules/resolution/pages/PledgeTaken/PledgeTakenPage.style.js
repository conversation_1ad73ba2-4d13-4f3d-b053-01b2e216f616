import { StyleSheet } from "react-native";
import useMediaQuery from "core/hooks/useMediaQuery";
import { useMemo } from "react";

const createStyles = (isCompactMode) => StyleSheet.create({
    container: {

    }
})

const usePledgeTakenPageStyles = () => {
    const { isMobile } = useMediaQuery()

    const styles = useMemo(() => createStyles(isMobile),[isMobile])

    return styles
}

export default usePledgeTakenPageStyles