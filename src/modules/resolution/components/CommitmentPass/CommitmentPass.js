import React, { useCallback, useRef } from "react";
import { View, Text, Platform } from "react-native";
import { captureRef } from "react-native-view-shot";
import * as Sharing from "expo-sharing";
import { Image } from "expo-image";
import _isNil from "lodash/isNil"
import styles from "./CommitmentPass.style";
import PrimaryButton from "atoms/PrimaryButton"
import { closePopover } from "molecules/Popover/Popover"
import { useSession } from "../../../auth/containers/AuthProvider";
import userReader from "core/readers/userReader";
import useCaptureView from "../../../../core/hooks/useCaptureView";
import commitmentPassUrl from '../../../../../assets/images/commitmentPass/commitment_pass.jpg'

// const COMMITMENT_PASS_URL = "https://matiks.com/commitment_pass.jpg"

const CommitmentPass = (props) => {
    const { duration } = props
    const imageRef = useRef(null);
    const { user } = useSession()

    const { captureView } = useCaptureView();

    const handleShare = useCallback(async () => {
        try {
            await captureView({ viewRef: imageRef, message: '', fileName: 'commitmentPass.png' })
        } catch (error) {
            console.error("Error sharing the image:", error);
        } finally {
            closePopover?.()
        }
    }, [imageRef, captureView])

    return (
        <View style={styles.container}>
            <View ref={imageRef} style={styles.imageContainer}>
                <Image
                    source={commitmentPassUrl}
                    style={styles.image}
                    contentFit="contain"
                />
                <View style={styles.userName}>
                    <Text style={styles.userNameText} numberOfLines={1}>{userReader.username(user)}</Text>
                </View>
                <View style={styles.duration}>
                    <Text style={[styles.userNameText, { fontSize: 10 }]} numberOfLines={1}>{duration}</Text>
                </View>
            </View>
            <PrimaryButton label="Share Your Commitment" onPress={handleShare} radius={40} buttonStyle={{ paddingHorizontal: 20 }} />
        </View>
    );
};


export default React.memo(CommitmentPass);
