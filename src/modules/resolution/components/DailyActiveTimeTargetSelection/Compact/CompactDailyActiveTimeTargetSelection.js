import React from "react"
import { View, Text } from "react-native"
import useDailyActiveTimeTargetSelectionStyles from "./../DailyActiveTimeTargetSelection.style"
import PropTypes from "prop-types"
import { TouchableOpacity } from "react-native"

const CompactDailyActiveTimeTargetSelection = (props) => {
    
    const { title, duration, durationText, isSelected, onPress, isPopular = false } = props

    const styles = useDailyActiveTimeTargetSelectionStyles()

    return (
        <View style={{ height:isPopular? 90:"auto", justifyContent: "center", alignItems: "center" }}>
            <TouchableOpacity style={[styles.container, isSelected && styles.selectedContainer]} onPress={onPress}>
                <Text style={styles.titleText}>
                    {title}
                </Text>
                <Text style={styles.durationText}>
                    {durationText}
                </Text>


            </TouchableOpacity>
            {
                isPopular && (<View style={styles.selectedBadge}>
                    <Text style={styles.selectionLabelText}>
                        Most Popular
                    </Text>
                </View>)
            }
        </View>

    )
}

CompactDailyActiveTimeTargetSelection.propTypes = {
    title: PropTypes.string,
    duration: PropTypes.number,
    durationText: PropTypes.string,
    isSelected: PropTypes.bool,
    isPopular: PropTypes.bool,
    onPress: PropTypes.func
}


export default React.memo(CompactDailyActiveTimeTargetSelection)