import _get from 'lodash/get';
import { LeagueType } from '../utils';
import _ from 'lodash';

export interface Feed {
  _id: string;
  title: string;
  description: string;
  sentFor: string;
  isLiked?: boolean;
  additionalInfo?: {
    badge: string;
    connectionRequest: {
      sentBy: string;
    };
    leagueType: LeagueType;
    feedStacked: boolean;
    puzzleInfo?: {
      puzzleId: string;
      timeTaken: number;
      puzzleType: string;
    };
    dailyChallengeInfo?: {
      division: number;
      score: number;
    };
    streakShieldApplied: boolean;
  };
  feedForFriends: {
    title: string;
    body: string;
  };
  sentAt: string;
  likesCount: number;
}

const feedReader = {
  id: (feed: Feed) => _get(feed, '_id'),
  title: (feed: Feed) => _get(feed, 'title'),
  description: (feed: Feed) => _get(feed, 'description'),
  feedForFriendsTitle: (feed: Feed) => _get(feed, ['feedForFriends', 'title']),
  feedForFriendsBody: (feed: Feed) => _get(feed, ['feedForFriends', 'body']),
  sentAt: (feed: Feed) => _get(feed, 'sentAt'),
  isLiked: (feed: Feed) => _get(feed, 'isLiked', false),
  likesCount: (feed: Feed) => _get(feed, 'likesCount'),
  connectionRequestSentBy: (feed: Feed) =>
    _get(feed, ['additionalInfo', 'connectionRequest', 'sentBy'], ''),
  feedStacked: (feed: Feed) =>
    _get(feed, ['additionalInfo', 'feedStacked'], false),
  leagueType: (feed: Feed) =>
    _get(feed, ['additionalInfo', 'leagueType'], 'RUBY') as LeagueType,
  puzzleTimeTaken: (feed: Feed) =>
    _get(feed, ['additionalInfo', 'puzzleInfo', 'timeTaken'], 0),
  puzzleType: (feed: Feed) =>
    _get(feed, ['additionalInfo', 'puzzleInfo', 'type'], 'CrossMath'),
  applyStreakShield: (feed: Feed) =>
    _get(feed, ['additionalInfo', 'streakShieldApplied'], false),
  dailyChallengeDivision: (feed: Feed) =>
    _get(feed, ['additionalInfo', 'dailyChallengeInfo', 'division'], 0),
  dailyChallengeScore: (feed: Feed) =>
    _get(feed, ['additionalInfo', 'dailyChallengeInfo', 'score'], 0),
  badgeName: (feed: Feed) => _get(feed, ['additionalInfo', 'badge'], 'NOVICE'),
};

export default feedReader;
