/* eslint-disable arrow-body-style */
import React, { useCallback, useMemo } from 'react';
import { View, Text } from 'react-native';
import FallbackImage from '@/src/components/atoms/helpers/fallbackImage';
import Logo from 'assets/images/notificationCentre/logo.png';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { FEED_TYPES } from '@/src/core/constants/notificationTypes';
import { getFormattedTimeWithMS } from '@/src/core/utils/general';
import styles from './FeedCard.style';
import SideImage from '../SideImage';
import CardFooter from '../CardFooter';
import feedReader from '../../reader/feedReader';
import { getFormattedDate } from '../../utils';
import Pressable from '@/src/components/atoms/Pressable';
import { useRouter } from 'expo-router';
import _split from 'lodash/split';
import _join from 'lodash/join';
import _concat from 'lodash/concat';

// create a func to get date into 5 min ago 10 min ago 10 day ago like this

const FeedCard = ({
  feedId,
  feedType,
  feed,
  imageUrl,
  isLiked,
}: {
  feedId: string;
  feedType: FEED_TYPES;
  feed: any;
  imageUrl: string;
  isLiked: boolean;
}) => {
  const { userId } = useSession();
  const leagueType = feedReader.leagueType(feed);
  const puzzleType = feedReader.puzzleType(feed);
  const router = useRouter();
  const { title, description } = useMemo(() => {
    if (userId === feed?.sentFor) {
      return {
        title: feedReader.title(feed),
        description: feedReader.description(feed),
      };
    }
    return {
      title: feedReader.feedForFriendsTitle(feed),
      description: feedReader.feedForFriendsBody(feed),
    };
  }, [userId, feed]);

  const updatedDescription = useMemo(() => {
    switch (feedType) {
      case FEED_TYPES.DAILY_PUZZLE:
        return _concat(
          description,
          ` ${getFormattedTimeWithMS(feedReader.puzzleTimeTaken(feed))}`,
        );
      case FEED_TYPES.DAILY_CHALLENGE:
        return _concat(
          description,
          ` ${getFormattedTimeWithMS(feedReader.dailyChallengeScore(feed))}`,
        );
      case FEED_TYPES.BADGE:
        return _concat(
          description,
          ` ${_join(_split(feedReader.badgeName(feed), '_'), ' ')} Badge`,
        );
      default:
        return description;
    }
  }, [description, feedType, feed]);

  const onPress = useCallback(() => {
    if (feedType === FEED_TYPES.WELCOME) {
      return;
    }
    router.push(`/profile/${title?.split('@')?.[1]}`);
  }, [feedType, router, title]);

  return (
    <View style={[styles.container]}>
      <View style={styles.mainContainer}>
        <View style={styles.contentContainer}>
          <View style={styles.imageContainer}>
            <FallbackImage
              source={{
                uri: imageUrl,
              }}
              fallbackSource={Logo}
              style={styles.image}
            />
          </View>
          <View style={styles.content}>
            <View style={styles.titleContainer}>
              <Pressable onPress={onPress}>
                <Text style={styles.title}>{title}</Text>
              </Pressable>
              <Text style={styles.time}>
                {getFormattedDate(feedReader.sentAt(feed))}
              </Text>
            </View>
            <Text style={styles.description}>{updatedDescription}</Text>
          </View>
        </View>
        <SideImage type={feedType} leagueType={leagueType} />
      </View>
      <View style={styles.buttonContainer}>
        <CardFooter
          feed={feed}
          puzzleType={puzzleType}
          title={title}
          feedType={feedType}
          isLiked={isLiked}
          feedId={feedId}
          likesCount={feedReader.likesCount(feed)}
        />
      </View>
    </View>
  );
};

export default React.memo(FeedCard);
