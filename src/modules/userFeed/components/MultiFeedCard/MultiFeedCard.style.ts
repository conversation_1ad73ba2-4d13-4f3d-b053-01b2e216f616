import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    padding: 12,
    paddingBottom: 16,
    borderColor: dark.colors.tertiary,
    borderRadius: 24,
    // maxWidth: 328,
    marginBottom: 12,
  },
  mainContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 24,
  },
  contentContainer: {
    flex: 1,
    gap: 8,
    flexDirection: 'row',
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    width: 60,
    height: 60,
  },
  image: {
    width: 34,
    height: 34,
    overflow: 'hidden',
  },
  content: {
    flex: 1,
    gap: 8,
  },
  titleContainer: {
    // alignItems: 'center',
    flex: 1,
    gap: 4,
  },
  title: {
    fontSize: 13,
    fontFamily: 'Montserrat-600',
    letterSpacing: 0.15,
    color: dark.colors.textLight,
    lineHeight: 16,
  },
  time: {
    fontSize: 9,
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-500',
    lineHeight: 11,
  },
  description: {
    fontSize: 12,
    color: dark.colors.textLight,
    fontFamily: 'Montserrat-500',
    letterSpacing: 0.15,
    lineHeight: 16,
  },
  buttonContainer: {
    // paddingLeft: 40,
    marginTop: 16,
  },
  imageContainer: {
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    width: 32,
    height: 32,
    borderRadius: 16,
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default styles;
