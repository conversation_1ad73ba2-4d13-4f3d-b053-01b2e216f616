import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    height: 56,
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 4,
  },
  crossButton: {
    height: 40,
    width: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerText: {
    fontSize: 17,
    fontFamily: 'Montserrat-500',
    lineHeight: 24,
    letterSpacing: -0.15,
    color: dark.colors.textLight,
  },
  sliderContainer: {
    marginTop: 4,
    marginBottom: 12,
  },
  sliderItem: {
    height: 32,
    paddingHorizontal: 12,
    borderWidth: 2,
    borderRadius: 16,
    borderColor: dark.colors.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sliderText: {
    fontSize: 14,
    fontFamily: 'Montserrat-500',
    lineHeight: 20,
    color: dark.colors.textDark,
  },
  selected: {
    borderColor: dark.colors.secondary,
  },
  selectedText: {
    color: dark.colors.secondary,
  },
});

export default styles;
