/* eslint-disable arrow-body-style */
import React, { useCallback, useEffect, useRef } from 'react';
import { View } from 'react-native';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import useFeedStore from '@/src/store/useFeedStore';
import { FlashList } from '@shopify/flash-list';
import EmptyComponent from '@/src/components/shared/EmptyComponent';
import _isEmpty from 'lodash/isEmpty';
import Loading from 'atoms/Loading';
import Header from 'shared/Header';
import ErrorView from 'atoms/ErrorView/ErrorView';
import styles from './styles';
import FeedCard from '../../components/FeedCard';
import MultiFeedCard from '../../components/MultiFeedCard';
import feedReader from '../../reader/feedReader';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';

const Feed = () => {
  const {
    feeds,
    loading,
    fetchFeeds,
    error,
    updateLastReadFeedId,
    isRead,
    refetchFeeds,
  } = useFeedStore((state) => ({
    feeds: state.feeds,
    loading: state.loading,
    fetchFeeds: state.fetchFeeds,
    error: state.error,
    updateLastReadFeedId: state.updateLastReadFeedId,
    isRead: state.isRead,
    refetchFeeds: state.refetchFeeds,
  }));

  const refetchFeedsRef = useRef(refetchFeeds);
  refetchFeedsRef.current = refetchFeeds;

  const { isMobile: isCompactMode } = useMediaQuery();

  const renderFeed = useCallback(({ item }: any) => {
    const feedStacked = feedReader.feedStacked(item?.feedData);
    if (feedStacked) {
      return (
        <MultiFeedCard
          feedId={item?._id}
          imageUrl={item?.imageUrl}
          isLiked={item?.isLiked}
          feedType={item?.feedType}
          feed={item?.feedData}
        />
      );
    }
    return (
      <FeedCard
        feedId={item?._id}
        imageUrl={item?.imageUrl}
        isLiked={item?.isLiked}
        feedType={item?.feedType}
        feed={item?.feedData}
      />
    );
  }, []);

  useEffect(() => {
    Analytics.track(ANALYTICS_EVENTS.FEEDS.VIEW_FEED);
    refetchFeedsRef.current();
  }, []);

  useEffect(() => {
    if (feeds.length > 0 && !isRead) {
      updateLastReadFeedId();
    }
  }, [feeds.length, isRead, updateLastReadFeedId]);

  if (loading) {
    return <Loading />;
  }
  if (!feeds && error) {
    return (
      <ErrorView
        errorMessage={error?.message ?? 'Failed to load notifications'}
      />
    );
  }

  if (_isEmpty(feeds)) {
    return (
      <View style={{ flex: 1 }}>
        <Header title="Feed" showBackInWeb />
        <EmptyComponent
          title="No Feed!"
          subTitle="There is no feed right now for you"
        />
      </View>
    );
  }

  return (
    <View
      style={[styles.container, !isCompactMode && { paddingHorizontal: 12 }]}
    >
      <Header title="Feed" showBackInWeb />
      <View style={{ paddingHorizontal: 12, flex: 1 }}>
        <FlashList
          estimatedItemSize={100}
          data={feeds}
          renderItem={renderFeed}
          onEndReached={fetchFeeds}
          onEndReachedThreshold={0.5}
          keyExtractor={(_, index) => `feed-${index}`}
          style={{ paddingHorizontal: 16 }}
          showsVerticalScrollIndicator={false}
          decelerationRate="normal"
        />
      </View>
    </View>
  );
};

export default Feed;
