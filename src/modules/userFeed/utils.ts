import SilverLeague from '@/assets/images/feed/silver_league.webp';
import GoldLeague from '@/assets/images/feed/gold_league.webp';
import DiamondLeague from '@/assets/images/feed/diamond_league.webp';
import RubyLeague from '@/assets/images/feed/league.webp';
import MatikanLeague from '@/assets/images/feed/matikan_league.webp';

export type LeagueType =
  | 'BRONZE'
  | 'SILVER'
  | 'GOLD'
  | 'DIAMOND'
  | 'RUBY'
  | 'MATIKAN';

export const getFormattedDate = (date: string) => {
  const now = new Date();
  const notificationDate = new Date(date);
  const timeDiff = now.getTime() - notificationDate.getTime();
  const seconds = Math.floor(timeDiff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  if (seconds < 60) {
    return `${seconds} sec ago`;
  }
  if (minutes < 60) {
    return `${minutes} min ago`;
  }
  if (hours < 24) {
    return `${hours} hr ago`;
  }
  return `${days} day ago`;
};

export const getLeagueImage = (leagueType: LeagueType) => {
  switch (leagueType) {
    case 'SILVER':
      return SilverLeague;
    case 'GOLD':
      return GoldLeague;
    case 'DIAMOND':
      return DiamondLeague;
    case 'RUBY':
      return RubyLeague;
    case 'MATIKAN':
      return MatikanLeague;
    default:
      return RubyLeague;
  }
};
