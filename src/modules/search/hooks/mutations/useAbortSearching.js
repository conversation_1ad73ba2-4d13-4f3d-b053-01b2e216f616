import { useMutation, gql } from '@apollo/client';
import { useCallback } from 'react';

const START_SEARCHING_MUTATION = gql`
  mutation AbortSearching {
    abortSearching
  }
`;

const useAbortMutation = () => {
  const [abortSearchingMutation, { data }] = useMutation(
    START_SEARCHING_MUTATION,
  );

  const abortSearching = useCallback(
    () => abortSearchingMutation(),
    [abortSearchingMutation],
  );

  return {
    ...data?.abortSearching,
    abortSearching,
  };
};

export default useAbortMutation;
