import { gql, useLazyQuery } from '@apollo/client';
import { useCallback, useEffect, useState } from 'react';
import _size from 'lodash/size';
import _get from 'lodash/get';
import useMessageEvents from './useMessageEvents';
import groupReader from '../readers/groupReader';

const GET_MESSAGE_GROUP = gql`
  query GetAllMessageGroups($input: GetAllMessageGroupsInput) {
    getAllMessageGroups(input: $input) {
      groups {
        _id
        groupName
        createdAt
        updatedAt
        groupType
        userInfoIfIndividual {
          _id
          name
          profileImageUrl
          rating
        }
        members
        lastMessage {
          _id
          groupId
          content
          attachment {
            type
            url
          }
          createdAt
          sender
        }
      }
      nextPage
      hasMore
    }
  }
`;

const PAGE_SIZE = 50;

const useGetMessageGroup = () => {
  const [fetchMessageGroups, { loading, error, client, data }] = useLazyQuery(
    GET_MESSAGE_GROUP,
    {
      fetchPolicy: 'cache-first',
      notifyOnNetworkStatusChange: true,
    },
  );

  const [groups, setGroups] = useState([]);
  const [params, setParams] = useState({
    nextPage: 1,
    hasMore: true,
  });

  const { message } = useMessageEvents();

  useEffect(() => {
    if (message) {
      setGroups((prevGroups) =>
        prevGroups.map((group) => {
          if (groupReader.id(group) === message.groupId) {
            return {
              ...group,
              lastMessage: message,
            };
          }
          return group;
        }),
      );
    }
  }, [message]);

  const getMessageGroups = useCallback(
    async (pageToFetch) => {
      if (loading) return;
      const response = await fetchMessageGroups({
        variables: {
          input: {
            page: pageToFetch,
            pageSize: PAGE_SIZE,
          },
        },
      });

      const { data } = response;
      const { getAllMessageGroups: messageGroupsObject } = data ?? EMPTY_OBJECT;
      const groups = _get(messageGroupsObject, 'groups', []);
      if (_size(groups) === 0) return;
      const nextPage = _get(messageGroupsObject, 'nextPage', pageToFetch);
      const hasMore = _get(messageGroupsObject, 'hasMore', false);
      setGroups((prevGroups) => [...prevGroups, ...groups]);
      setParams((prevParams) => ({ ...prevParams, nextPage, hasMore }));
    },
    [fetchMessageGroups, loading, setParams, setGroups],
  );

  useEffect(() => {
    getMessageGroups(params.nextPage);
  }, []);

  const loadMore = useCallback(() => {
    if (loading) return;
    if (!params.hasMore) return;
    getMessageGroups(params.nextPage);
  }, [getMessageGroups, loading, params]);

  return {
    loading,
    error,
    loadMore,
    hasMore: params.hasMore,
    groups,
  };
};

export default useGetMessageGroup;
