import { createContext } from 'react';
import { MessagingContextType } from '../types';

const defaultContext: MessagingContextType = {
  messages: [],
  messagesLoading: false,
  messagesError: '',
  loadMoreMessages: () => {},
  hasMoreMessages: true,
  sendNewMessage: (message: CreateMessageInput) => {},

  groups: [],
  groupsLoading: false,
  groupsError: null,
  loadMoreGroups: () => {},
  hasMoreGroups: true,

  currentGroupId: null,
  setCurrentGroupId: (groupId: string) => {},
  currentGroup: {},
};

const ChatContext = createContext<MessagingContextType>(defaultContext);
export const ChatContextProvider = ChatContext.Provider;
export default ChatContext;