import Dark from '@/src/core/constants/themes/dark';
import { ActivityIndicator, StyleSheet, View } from 'react-native';

const styles = StyleSheet.create({
  loadingContainer: {
    height: 120,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

const ListFooter = ({ loading = false }: { loading: boolean }) => {
  if (!loading) return null;

  return (
    <View style={styles.loadingContainer}>
      <ActivityIndicator color={Dark.colors.secondary} />
    </View>
  );
};

export default ListFooter;
