/* eslint-disable no-nested-ternary */
import React, { useCallback, useEffect, useState } from 'react';
import { SafeAreaView } from 'react-native';
import _filter from 'lodash/filter';
import { FlashList } from '@shopify/flash-list';
import _size from 'lodash/size';
import Loading from '@/src/components/atoms/Loading';
import { Group } from '../../types/groups';
import ChatListHeader from '../ChatListHeader';
import ChatListItem from '../ChatListItem';
import styles from './CompactChatsGroupList.style';
import groupReader from '../../readers/groupReader';
import ListFooter from '../ListFooter';
import useChatContext from '../../hooks/useChatContext';
import EmptyScreen from '../../pages/Chat/EmptyScreen';

const CompactChatsGroupList = () => {
  const {
    loadMoreGroups: loadMore,
    groups,
    setCurrentGroupId,
    groupsLoading: messageGroupLoading,
  } = useChatContext();

  const [messageGroupsShown, setMessageGroupsShown] = useState<Group[]>(groups);
  const [search, setSearch] = useState('');



  const Search = useCallback(() => {
    if (search === '') {
      return;
    }
    const searchGroups: Group[] =
      _filter(
        groups,
        (group: Group) =>
          group.groupName?.toLowerCase().includes(search.toLowerCase()) ||
          groupReader
            .individual(group)
            ?.name?.toLowerCase()
            .includes(search.toLowerCase()),
      ) ?? [];
    setMessageGroupsShown(searchGroups);
  }, [groups, search]);

  useEffect(() => {
    Search();
  }, [Search]);

  const renderChatItem = useCallback(
    ({ item }: { item: Group }) => (
      <ChatListItem
        item={item}
        onPress={() => {
          Analytics.track(ANALYTICS_EVENTS.MESSAGES.CLICKED_ON_INDIVIDUAL_CHAT)
          setCurrentGroupId(item?._id);
          setSearch('');
        }}
      />
    ),
    [setCurrentGroupId],
  );

  const ListFooterComponent = useCallback(
    () => <ListFooter loading={messageGroupLoading} />,
    [messageGroupLoading],
  );

  return (
    <SafeAreaView style={styles.container}>
      <ChatListHeader search={search} setSearch={setSearch} />
      {_size(groups) === 0 ? (
        messageGroupLoading ? (
          <Loading />
        ) : (
          <EmptyScreen />
        )
      ) : (
        <FlashList
          data={(search === '' ? groups : messageGroupsShown) as any[]}
          renderItem={renderChatItem}
          keyExtractor={(item, index) => `${item._id}__${index}`}
          contentContainerStyle={styles.listContainer}
          onEndReached={loadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={ListFooterComponent as any}
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
};

export default React.memo(CompactChatsGroupList);
