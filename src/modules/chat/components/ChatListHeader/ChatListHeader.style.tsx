// import Dark from '@/src/core/constants/themes/Dark';
import Dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  headerContainer: {
    height: 56,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // justifyContent: 'center',
    // paddingTop: 10,
    // borderBottomColor: Dark.colors.tertiary,
    // borderBottomWidth: 1,
    backgroundColor: Dark.colors.background,
    // elevation: 5,
    // shadowColor: '#000',
    // shadowOffset: { width: 0, height: 2 },
    // shadowOpacity: 0.2,
    // shadowRadius: 2,
  },
  backButton: {
    height: 40,
    width: 30,
    justifyContent: 'center',
    alignItems: 'flex-start',
    // backgroundColor:'#ffffff0f'
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    // gap: 20,
    position: 'relative',
    // paddingBottom: 16,
  },
  headerContent: {
    flex: 1,
    paddingHorizontal: 16,
    // paddingTop: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#fff',
  },
  searchContainer: {
    height: 40,
    backgroundColor: Dark.colors.cardBackground,
    borderRadius: 12,
    paddingHorizontal: 15,
    justifyContent: 'center',
    position: 'absolute',
    left: 30,
    right: 0,
    overflow: 'hidden',
  },
  searchInput: {
    color: '#fff',
    fontSize: 16,
    height: '100%',
    padding: 0,
    width: '100%',
  },
});

export default styles;
