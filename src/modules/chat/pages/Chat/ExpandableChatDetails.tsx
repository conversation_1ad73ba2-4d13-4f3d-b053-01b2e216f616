import React from 'react';
import { View } from 'react-native';
import _size from 'lodash/size';
import styles from './Chat.Style';
import ExpandableChatGroupList from '../../components/ExpandableChatGroupList';
import ExpandableChatsList from '../../components/ExpandableChatsList';
import { Group } from '../../types/groups';
import useChatContext from '../../hooks/useChatContext';
import EmptyComponent from '../../../../components/shared/EmptyComponent/EmptyComponent';
import EmptyScreen from './EmptyScreen';

const ExpandableChatDetails = () => {
  const { groups, currentGroup } = useChatContext();

  if (_size(groups) === 0) {
    return <EmptyScreen />;
  }

  return (
    <View style={styles.expandedWrapper}>
      <View style={styles.expandedContainer}>
        <ExpandableChatGroupList />
        {currentGroup ? (
          <ExpandableChatsList activeGroup={currentGroup as Group} />
        ) : null}
      </View>
    </View>
  );
};

export default React.memo(ExpandableChatDetails);
