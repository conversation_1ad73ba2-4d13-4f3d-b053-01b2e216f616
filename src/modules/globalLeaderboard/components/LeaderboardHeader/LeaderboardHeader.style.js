import { StyleSheet } from 'react-native';
import Dark from "core/constants/themes/dark";

const styles = StyleSheet.create({
    rowContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        gap: 6,
    },
    rankColumn: {
        width: 36,
    },
    headerLabelStyle: {
        color: Dark.colors.textDark,
        fontSize: 10,
        fontFamily: 'Montserrat-700',
        lineHeight: 20,
        letterSpacing: 1
    },
    profileInfoColumn: {
        flex: 1,
        flexBasis: 1,
        flexDirection: 'row',
        gap: 6,
        alignItems: 'center',
    },
    ratingColumn: {
        width: 100,
        alignItems: 'flex-end'
    },

})

export default styles;
