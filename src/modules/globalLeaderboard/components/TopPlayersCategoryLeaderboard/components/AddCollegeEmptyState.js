import React from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import dark from 'core/constants/themes/dark';

const schoolImage = require('assets/images/school.png');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: dark.colors.background,
  },
  image: {
    width: 150,
    height: 150,
    resizeMode: 'contain',
  },
  title: {
    fontSize: 12,
    fontFamily: 'Montserrat-400',
    color: dark.colors.text,
    textAlign: 'center',
    marginBottom: 20,
    width: 191,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 22,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
  },
  buttonText: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: dark.colors.secondary,
    marginLeft: 8,
    lineHeight: 20,
  },
});

const AddCollegeEmptyState = ({ onAddCollege }) => (
  <View style={styles.container}>
    <Image source={schoolImage} style={styles.image} />
    <Text style={styles.title}>
      Add your college and connect with your friends!
    </Text>
    <TouchableOpacity style={styles.button} onPress={onAddCollege}>
      <MaterialIcons name="add" size={20} color={dark.colors.secondary} />
      <Text style={styles.buttonText}>Add College</Text>
    </TouchableOpacity>
  </View>
);

export default React.memo(AddCollegeEmptyState);
