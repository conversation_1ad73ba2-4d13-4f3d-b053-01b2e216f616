import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  contentContainer: {
    width: '100%',
    backgroundColor: dark.colors.gradientBackground,
    paddingVertical: 24,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginBottom: 12,
  },
  expandedContainer: {
    paddingHorizontal: 36,
    paddingTop: 24,
    backgroundColor: dark.colors.dark,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  category: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  categoryText: {
    fontFamily: 'Montserrat-800',
    fontSize: 11,
    color: dark.colors.textLight,
    letterSpacing: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    gap: 8,
  },
  icon: {
    width: 24,
    height: 24,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: dark.colors.text,
  },
});

export default styles;
