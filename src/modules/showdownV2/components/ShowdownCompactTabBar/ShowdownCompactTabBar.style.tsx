import dark from 'core/constants/themes/dark';
import { StyleSheet } from 'react-native';
import fonts from 'core/constants/fonts';

const styles = StyleSheet.create({
  tabBarContainer: {
    width: '100%',
    alignSelf: 'flex-start',
    justifyContent: 'space-between',
  },
  tabBar: {
    width: '100%',
    backgroundColor: 'transparent',
    elevation: 0,
    borderBottomColor: dark.colors.tertiary,
    borderBottomWidth: 2,
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
  },
  indicator: {
    height: 4,
    justifyContent: 'center',
    borderTopRightRadius: 5,
    borderTopLeftRadius: 5,
    backgroundColor: dark.colors.secondary,
  },
  tabStyle: {
    flexGrow: 1,
    alignItems: 'center',
    overflow: 'hidden',
    justifyContent: 'space-between',
  },
  label: {
    fontFamily: fonts.MONTSERRAT_MEDIUM,
    fontSize: 14,
    lineHeight: 20,
  },
});

export default styles;
