import React, { useMemo } from 'react';
import { Image, Text, View } from 'react-native';

import _isEmpty from 'lodash/isEmpty';

import Confetti from 'assets/images/Confetti_SumdayShowdown.png';
import useCountDownTimer from '@/src/modules/showdownV2/hooks/useCountDownTimer';
import styles from './FixtureCantPlayInfo.style';

const Timer = React.memo(({ timer }: { timer: number }) => {
  const { formattedTime } = useCountDownTimer({
    targetTimeStamp: timer,
  });
  return <Text>{formattedTime}</Text>;
});

const FixtureCantPlayInfo = ({
  currentRound,
  userCantPlayConfig,
  isRoundActive,
}: {
  currentRound: number;
  userCantPlayConfig: any;
  isRoundActive: boolean;
}) => {
  const roundNumber = useMemo(() => {
    if (currentRound < 1) return 1;
    if (isRoundActive) return currentRound;
    return currentRound + 1;
  }, [currentRound, isRoundActive]);

  if (_isEmpty(userCantPlayConfig)) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        <Text style={styles.roundInfoText}> ROUND {roundNumber}</Text>
        <Text style={[styles.cardTitle, { color: userCantPlayConfig.color }]}>
          {userCantPlayConfig.title}{' '}
        </Text>
        <Text style={styles.cardDescription}>
          {userCantPlayConfig.desc}
          {(userCantPlayConfig.timer ?? 0) > 0 ? (
            <Timer timer={userCantPlayConfig.timer} />
          ) : null}
        </Text>
        <View style={styles.confettiContainer}>
          <Image source={Confetti} style={styles.confettiImage} />
        </View>
      </View>
    </View>
  );
};

export default React.memo(FixtureCantPlayInfo);
