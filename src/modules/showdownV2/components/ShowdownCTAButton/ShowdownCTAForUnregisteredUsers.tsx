import { Text, TouchableOpacity } from 'react-native';
import React, { useCallback } from 'react';
import useShowdownStore from 'store/useShowdownStore';
import { FontAwesome } from '@expo/vector-icons';
import dark from '@/src/core/constants/themes/dark';
import { TOAST_TYPE } from '@/src/components/molecules/Toast';
import SHOWDOWN_ACTIONS from '../../constants/showdownActions';
import useShowdownActionHandler from '../../hooks/useShowdownActionHandler';
import styles, { useUnRegisteredButtonStyles } from './ShowdownCTAButton.style';

const ShowdownCTAForUnregisteredUsers = ({ isGuest }: { isGuest: boolean }) => {
  const buttonStyles = useUnRegisteredButtonStyles();
  const { onAction } = useShowdownActionHandler();

  const { isRegisteringForShowDown, hasRegistrationEnded, hasEnded } =
    useShowdownStore((state) => ({
      isRegisteringForShowDown: state.isRegisteringForShowDown,
      hasRegistrationEnded: state.otherStates.hasRegistrationEnded,
      hasEnded: state.otherStates.hasEnded,
    }));

  const onPressRegisterByGuest = useCallback(() => {
    try {
      onAction?.({ type: SHOWDOWN_ACTIONS.REGISTER_GUEST_USER });
    } catch (error) {
      console.log(error);
    }
  }, [onAction]);

  const onPressRegister = useCallback(() => {
    try {
      onAction?.({ type: SHOWDOWN_ACTIONS.REGISTER_FOR_SHOWDOWN });
    } catch (error) {
      console.log(error);
    }
  }, [onAction]);

  const onPressShowToast = useCallback(
    (message: string, type: any) => {
      try {
        onAction?.({
          type: SHOWDOWN_ACTIONS.SHOW_TOAST,
          payload: { message, type },
        });
      } catch (error) {
        console.log(error);
      }
    },
    [onAction],
  );

  const renderInactiveButton = useCallback(
    ({ label, message }: { label: string; message: string }) => (
      <TouchableOpacity
        style={[buttonStyles.registerButton, buttonStyles.inactiveButton]}
        onPress={() => onPressShowToast(message, TOAST_TYPE.ERROR)}
      >
        <Text style={buttonStyles.inactiveButtonText}>{label}</Text>
      </TouchableOpacity>
    ),
    [buttonStyles, onPressShowToast],
  );

  const renderRegisterButtonForGuest = useCallback(
    () => (
      <TouchableOpacity
        style={buttonStyles.lockedRegisterButton as any}
        onPress={onPressRegisterByGuest}
      >
        <FontAwesome name="lock" size={20} color={dark.colors.text} />
        <Text style={styles.lockedRegisterText}>Register now</Text>
      </TouchableOpacity>
    ),
    [buttonStyles, onPressRegisterByGuest],
  );

  const renderRegisterButton = useCallback(
    () => (
      <TouchableOpacity
        onPress={onPressRegister}
        style={[buttonStyles.registerButton as any]}
        disabled={isRegisteringForShowDown}
      >
        <Text style={styles.registerText}>
          {isRegisteringForShowDown ? 'Registering...' : 'Register Now'}
        </Text>
      </TouchableOpacity>
    ),
    [buttonStyles, onPressRegister, isRegisteringForShowDown],
  );

  if (hasEnded) {
    return renderInactiveButton({
      label: 'Showdown Ended',
      message: 'Showdown has ended, You cant participate in this Showdown',
    });
  }

  if (hasRegistrationEnded) {
    return renderInactiveButton({
      label: 'Registration Ended',
      message: 'Registration has ended, You cant participate in this Showdown',
    });
  }

  if (isGuest) {
    return renderRegisterButtonForGuest();
  }

  return renderRegisterButton();
};

export default React.memo(ShowdownCTAForUnregisteredUsers);
