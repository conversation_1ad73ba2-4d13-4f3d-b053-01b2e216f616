/* eslint-disable react/require-default-props */
import { TouchableOpacity, Text } from 'react-native';
import React, { useCallback } from 'react';
import {
  showToast,
  hideToast,
  TOAST_TYPE,
} from '@/src/components/molecules/Toast';
import useCountDownTimer from 'modules/showdownV2/hooks/useCountDownTimer';

const TextWithTimer = React.memo(
  ({
    text,
    timer,
    textStyle,
  }: {
    text: string;
    timer: number;
    textStyle: any;
  }) => {
    const { formattedTime } = useCountDownTimer({ targetTimeStamp: timer });
    return (
      <Text style={textStyle}>
        {text} ({formattedTime})
      </Text>
    );
  },
);

const InactiveButton = ({
  label,
  message,
  type = TOAST_TYPE.INFO,
  buttonStyle,
  textStyle,
  timer,
}: {
  label: string;
  message: string;
  type?: any;
  buttonStyle: any;
  textStyle: any;
  timer?: number;
}) => {
  const onPressInactiveButton = useCallback(() => {
    hideToast();
    showToast({ description: message, type });
  }, [message, type]);

  return (
    <TouchableOpacity style={buttonStyle} onPress={onPressInactiveButton}>
      {timer && timer > 0 ? (
        <TextWithTimer text={label} timer={timer} textStyle={textStyle} />
      ) : (
        <Text style={textStyle}>{label}</Text>
      )}
    </TouchableOpacity>
  );
};

export default React.memo(InactiveButton);
