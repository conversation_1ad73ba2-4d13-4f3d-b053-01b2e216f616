/* eslint-disable no-unused-expressions */
import { useEffect, useRef, useState } from 'react';
import { formatTime } from '../utils/timer';

const useCountDownTimer = ({
  targetTime,
  targetTimeStamp,
}: {
  targetTime?: string;
  targetTimeStamp?: number;
}) => {
  const _targetTimeStamp =
    targetTimeStamp ?? new Date(targetTime ?? '').getTime();

  const [TimeLeft, setTimeLeft] = useState({
    timer: _targetTimeStamp - getCurrentTime(),
    formattedTime: formatTime(_targetTimeStamp - getCurrentTime()),
  });

  const timerIntervalRef = useRef<any | null>(null);

  useEffect(() => {
    if (!_targetTimeStamp) return;
    if (timerIntervalRef.current) clearInterval(timerIntervalRef.current);

    timerIntervalRef.current = setInterval(() => {
      const timeLeft = _targetTimeStamp - getCurrentTime();
      if (timeLeft > 0) {
        setTimeLeft({ timer: timeLeft, formattedTime: formatTime(timeLeft) });
      } else {
        timerIntervalRef.current && clearInterval(timerIntervalRef.current);
      }
    }, 1000);

    return () => {
      if (timerIntervalRef.current) clearInterval(timerIntervalRef.current);
    };
  }, [_targetTimeStamp]);

  return TimeLeft;
};

export default useCountDownTimer;
