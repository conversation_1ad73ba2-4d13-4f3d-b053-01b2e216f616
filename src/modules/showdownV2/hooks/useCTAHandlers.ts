import useShowdownStore from 'store/useShowdownStore';
import { useCallback, useRef } from 'react';
import { hideToast, showToast, TOAST_TYPE } from 'molecules/Toast';
import _get from 'lodash/get';
import { useRouter } from 'expo-router';
import _isEmpty from 'lodash/isEmpty';
import { handleAsync } from 'core/utils/asyncUtils';
import { ShowdownState } from 'store/useShowdownStore/types';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';

type UseCTAHandlersProps = {
  getShowdownPropertiesToTrack: ShowdownState['getShowdownPropertiesToTrack'];
  registerForShowdown: ShowdownState['registerForShowdown'];
  unregisterFromShowdown: ShowdownState['unregisterFromShowdown'];
  fetchShowdown: ShowdownState['fetchShowdown'];
  isLive: ShowdownState['otherStates']['isLive'];
  hasUserRegistered: ShowdownState['otherStates']['hasUserRegistered'];
  currentGameIdForUser: string;
  showdownId: string;
  markAttendance: ShowdownState['markAttendance'];
  isRegisteringForShowDown: ShowdownState['isRegisteringForShowDown'];
};

const useShowdownRegistration = () => {
  const {
    showdownId,
    isRegisteringForShowDown,
    registerForShowdown,
    unregisterFromShowdown,
    fetchShowdown,
    isLive,
    hasUserRegistered,
    currentGameIdForUser,
    markAttendance,
    getShowdownPropertiesToTrack,
  }: UseCTAHandlersProps = useShowdownStore((state) => ({
    showdownId: state.showdownId,
    isRegisteringForShowDown: state.isRegisteringForShowDown,
    registerForShowdown: state.registerForShowdown,
    unregisterFromShowdown: state.unregisterFromShowdown,
    fetchShowdown: state.fetchShowdown,
    isLive: state.otherStates.isLive,
    hasUserRegistered: state.otherStates.hasUserRegistered,
    markAttendance: state.markAttendance,
    currentGameIdForUser: _get(
      state.showdown,
      ['currentUserParticipation', 'currentGame'],
      '',
    ),
    getShowdownPropertiesToTrack: state.getShowdownPropertiesToTrack,
  }));
  const router = useRouter();

  const fetchShowdownRef = useRef(fetchShowdown);
  fetchShowdownRef.current = fetchShowdown;
  const reFetchShowDown = useCallback(() => {
    fetchShowdownRef?.current?.(showdownId);
  }, [showdownId]);

  const onPressRegisterGuestUser = useCallback(() => {
    showToast({
      type: TOAST_TYPE.ERROR,
      description:
        'Guest Users or Users with less than 3 games played are not allowed to participate in Showdown!',
    });
  }, []);

  const onPressRegister = useCallback(async () => {
    if (isRegisteringForShowDown) {
      return;
    }
    Analytics.track(ANALYTICS_EVENTS.SHOWDOWN.CLICK_ON_REGISTER_FOR_SHOWDOWN, {
      ...(getShowdownPropertiesToTrack() ?? EMPTY_OBJECT),
      [PAGE_NAME_KEY]: PAGE_NAMES.SHOWDOWN_DETAILS,
    });
    const [isUserRegistered, error] = await handleAsync(
      registerForShowdown,
      showdownId,
    );
    if (error) {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: `Something went wrong while Registering`,
      });
      Analytics.track(ANALYTICS_EVENTS.SHOWDOWN.SHOWDOWN_REGISTRATION_FAILED, {
        ...(getShowdownPropertiesToTrack() ?? EMPTY_OBJECT),
        error,
        [PAGE_NAME_KEY]: PAGE_NAMES.SHOWDOWN_DETAILS,
      });
    }
    if (isUserRegistered) {
      showToast({
        type: TOAST_TYPE.SUCCESS,
        description: 'Successfully registered for the showdown!',
      });
      reFetchShowDown();
    } else {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: `Something went wrong while Registering`,
      });
      Analytics.track(ANALYTICS_EVENTS.SHOWDOWN.SHOWDOWN_REGISTRATION_FAILED, {
        ...(getShowdownPropertiesToTrack() ?? EMPTY_OBJECT),
        [PAGE_NAME_KEY]: PAGE_NAMES.SHOWDOWN_DETAILS,
      });
    }
  }, [
    isRegisteringForShowDown,
    showdownId,
    registerForShowdown,
    getShowdownPropertiesToTrack,
    reFetchShowDown,
  ]);

  const onPressUnRegister = useCallback(async () => {
    try {
      const result = await unregisterFromShowdown(showdownId);
      Analytics.track(
        ANALYTICS_EVENTS.SHOWDOWN.CLICK_ON_UNREGISTERED_FROM_SHOWDOWN,
        {
          ...(getShowdownPropertiesToTrack() ?? EMPTY_OBJECT),
          [PAGE_NAME_KEY]: PAGE_NAMES.SHOWDOWN_DETAILS,
        },
      );
      if (result) {
        showToast({
          type: TOAST_TYPE.SUCCESS,
          description: 'Un-Registered from showdown!',
        });
        reFetchShowDown();
      } else {
        showToast({
          type: TOAST_TYPE.ERROR,
          description: `Something went wrong while removing the registration`,
        });
      }
    } catch (error) {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: `Something went wrong while removing the registration`,
      });
    }
  }, [
    getShowdownPropertiesToTrack,
    reFetchShowDown,
    showdownId,
    unregisterFromShowdown,
  ]);

  const onPressMarkAttendance = useCallback(async () => {
    const [_, error] = await handleAsync(markAttendance, showdownId);
    if (error) {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: `Something went wrong while marking attendance`,
      });
    }
    showToast({
      type: TOAST_TYPE.SUCCESS,
      description: 'Attendance marked successfully!',
    });
  }, [showdownId, markAttendance]);

  const handleJoinNowGame = useCallback(() => {
    if (!isLive || !hasUserRegistered) {
      hideToast();
      showToast({
        type: TOAST_TYPE.ERROR,
        description: `You are not eligible to join the game`,
      });
      return;
    }

    if (_isEmpty(currentGameIdForUser)) {
      hideToast();
      showToast({
        type: TOAST_TYPE.ERROR,
        description: `Something went wrong while joining the game`,
      });
      return;
    }

    Analytics.track(ANALYTICS_EVENTS.CONTEST.CLICKED_ON_JOIN_NOW, {
      ...(getShowdownPropertiesToTrack() ?? EMPTY_OBJECT),
      [PAGE_NAME_KEY]: PAGE_NAMES.SHOWDOWN_DETAILS,
    });

    router.replace(`/showdown/play/${currentGameIdForUser}`);
  }, [
    isLive,
    hasUserRegistered,
    currentGameIdForUser,
    getShowdownPropertiesToTrack,
    router,
  ]);

  return {
    isRegisteringForShowDown,
    onPressRegister,
    onPressUnRegister,
    onPressRegisterGuestUser,
    handleJoinNowGame,
    reFetchShowDown,
    onPressMarkAttendance,
  };
};

export default useShowdownRegistration;
