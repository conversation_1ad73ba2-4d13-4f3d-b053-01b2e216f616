/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
import useShowdownStore from 'store/useShowdownStore';
import { useCallback, useEffect, useRef } from 'react';
import { handleAsync } from '@/src/core/utils/asyncUtils';
import useRefetchOnAppFocus from '@/src/core/hooks/useRefetchOnAppFocus';
import useShowdownRoundLifecycle from './useShowdownRoundLifecycle';
import useShowdownWebsocketListener from './useShowdownWebsocketListener';
import useEdgeLifecycleHandler from './useEdgeLifecycleHandler';

const useShowdownLifeCycle = (showdownId: string) => {
  const { refetchData } = useShowdownStore((state) => ({
    refetchData: state.refetchData,
  }));

  const _refetchData = useCallback(async () => {
    const [_, error] = await handleAsync(refetchData, showdownId);
    if (error) {
      console.error(error);
    }
  }, [refetchData, showdownId]);

  const refetchDataRef = useRef(_refetchData);
  refetchDataRef.current = _refetchData;

  useShowdownRoundLifecycle(refetchDataRef);
  useShowdownWebsocketListener();
  useEdgeLifecycleHandler();

  useRefetchOnAppFocus(() => refetchDataRef.current());

  useEffect(() => {
    if (!showdownId) return;
    refetchDataRef.current();
  }, [showdownId]);
};

export default useShowdownLifeCycle;
