import { useMemo } from 'react';
import { TAB_KEYS } from '../constants/showdownDetails';

const useTabRoutes = (
  isLive: boolean,
  hasUserRegistered: boolean,
  hasEnded: boolean,
) =>
  useMemo(() => {
    const availableRoutes = [];
    if (!hasEnded && hasUserRegistered) {
      availableRoutes.push({
        key: TAB_KEYS.FIXTURES,
        title: 'Fixtures',
      });
    }
    if (isLive || hasEnded) {
      availableRoutes.push({
        key: TAB_KEYS.LEADERBOARD,
        title: 'Leaderboard',
      });
    }
    availableRoutes.push({ key: TAB_KEYS.DETAILS, title: 'Details' });
    return availableRoutes;
  }, [isLive, hasUserRegistered, hasEnded]);

export default useTabRoutes;
