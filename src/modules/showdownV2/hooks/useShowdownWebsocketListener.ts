/* eslint-disable react-hooks/exhaustive-deps */
import useShowdownStore from 'store/useShowdownStore';
import { useEffect, useCallback, useRef } from 'react';
import EventManager from '@/src/core/event';
import { events, listenersNamespace } from '@/src/core/event/constants';
import _isNaN from 'lodash/isNaN';
import { WEBSOCKET_CHANNELS } from '@/src/core/constants/websocket';
import useWebsocketStore from '@/src/store/useWebSocketStore';

const useShowdownWebsocketListener = () => {
  const {
    showdownId,
    updateShowdown,
    fetchLeaderboard,
    fetchFixtures,
    leaderboardPageNumber,
  } = useShowdownStore((state) => ({
    showdownId: state.showdownId,
    updateShowdown: state.updateShowdown,
    fetchLeaderboard: state.fetchLeaderboard,
    fetchFixtures: state.fetchFixtures,
    leaderboardPageNumber: state.leaderboardPageNumber,
  }));

  const { joinChannel, leaveChannel } = useWebsocketStore((state) => ({
    joinChannel: state.joinChannel,
    leaveChannel: state.leaveChannel,
  }));

  const leaderboardPageNumberRef = useRef(leaderboardPageNumber);
  leaderboardPageNumberRef.current = leaderboardPageNumber;

  const showdownIdRef = useRef(showdownId);
  showdownIdRef.current = showdownId;

  const _fetchFixtures = useCallback(async () => {
    await fetchFixtures(showdownIdRef.current);
  }, [fetchFixtures]);

  const _fetchLeaderboard = useCallback(async () => {
    await fetchLeaderboard(
      showdownIdRef.current,
      leaderboardPageNumberRef.current,
    );
  }, [fetchLeaderboard]);

  const fetchFixturesRef = useRef(_fetchFixtures);
  fetchFixturesRef.current = _fetchFixtures;
  const fetchLeaderboardRef = useRef(_fetchLeaderboard);
  fetchLeaderboardRef.current = _fetchLeaderboard;

  useEffect(() => {
    if (!showdownId) return;
    const showdownChannel = WEBSOCKET_CHANNELS.ShowdownEvents(showdownId);
    joinChannel(showdownChannel);
    return () => {
      leaveChannel(showdownChannel);
    };
  }, [showdownId]);

  useEffect(() => {
    const eventManager = new EventManager();
    const subscriptionFixturesCreated = eventManager.on(
      events.ShowdownFicturesCreated,
      listenersNamespace.ShowdownFicturesCreated,
      (payload) => {
        const _showdownDetails = payload?.showdown ?? EMPTY_OBJECT;
        const participantDetails = payload?.participant ?? EMPTY_OBJECT;
        if (showdownIdRef.current !== _showdownDetails?._id) {
          return;
        }
        updateShowdown((prev: any) => ({
          ...(prev ?? EMPTY_OBJECT),
          ..._showdownDetails,
          currentUserParticipation: {
            ...(prev?.currentUserParticipation ?? EMPTY_OBJECT),
            ...participantDetails,
          },
        }));
        fetchFixturesRef.current();
        fetchLeaderboardRef.current();
      },
    );
    const subscriptionParticipantUpdatedEvent = eventManager.on(
      events.ShowdownParticipantUpdatedEvent,
      listenersNamespace.ShowdownParticipantUpdatedEvent,
      (payload) => {
        const _participant = payload?.participant ?? EMPTY_OBJECT;
        if (showdownIdRef.current !== _participant?.showdownId) {
          return;
        }
        updateShowdown((prev: any) => ({
          ...(prev ?? EMPTY_OBJECT),
          currentUserParticipation: {
            ...(prev?.currentUserParticipation ?? EMPTY_OBJECT),
            ..._participant,
          },
        }));
      },
    );

    const subscriptionOnLeaderboardUpdatedEvent = eventManager.on(
      'LEADERBOARD_UPDATE',
      'LEADERBOARD_UPDATE',
      (payload) => {
        const _leaderboard = payload ?? EMPTY_OBJECT;
        if (showdownIdRef.current !== _leaderboard?.showdownId) {
          return;
        }
        if (
          _isNaN(leaderboardPageNumberRef.current) ||
          leaderboardPageNumberRef.current !== _leaderboard?.page
        ) {
          return;
        }

        fetchLeaderboardRef.current();
      },
    );

    return () => {
      subscriptionFixturesCreated.unsubscribe();
      subscriptionParticipantUpdatedEvent.unsubscribe();
      subscriptionOnLeaderboardUpdatedEvent.unsubscribe();
    };
  }, []);
};

export default useShowdownWebsocketListener;
