import React from 'react';
import useShowdownStore from 'store/useShowdownStore';
import Loading from '@/src/components/atoms/Loading';
import ErrorView from '@/src/components/atoms/ErrorView/ErrorView';
import _isEmpty from 'lodash/isEmpty';
import CompactShowdownDetails from '../../components/CompactShowdownDetails';

const CompactShowdownDetailsPage = () => {
  const { showdown, isShowdownLoading } = useShowdownStore((state) => ({
    showdown: state.showdown,
    isShowdownLoading: state.showdownLoading,
  }));

  if (isShowdownLoading) {
    return <Loading label="Loading Showdown Details" />;
  }

  if (_isEmpty(showdown)) {
    return (
      <ErrorView errorMessage="Something went Wrong while fetching showdown." />
    );
  }

  return <CompactShowdownDetails />;
};

export default React.memo(CompactShowdownDetailsPage);
