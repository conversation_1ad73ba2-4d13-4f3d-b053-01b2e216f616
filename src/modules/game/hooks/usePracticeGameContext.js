import React, { useContext } from 'react';
import useFetchGameQuery from './queries/useFetchGameQuery';
import GameContext, { GameContextProvider } from '../context/gameContext';

const useGame = ({ gameId }) => {
  const {
    game: initialGame,
    loading,
    error,
    reFetchGame,
  } = useFetchGameQuery({ gameId });

  return {
    game: initialGame,
    updatedGame: initialGame,
    reFetchGame,
    gameMeta: {
      loading,
      error,
    },
  };
};

export const WithPracticeGameContext = (Component) => {
  const GameContextWrapper = (props) => {
    const { gameId } = props;

    const contextValue = useGame({ gameId });
    return (
      <GameContextProvider value={contextValue}>
        <Component {...props} />
      </GameContextProvider>
    );
  };

  return React.memo(GameContextWrapper);
};

const usePracticeGameContext = () => useContext(GameContext);

export default usePracticeGameContext;
