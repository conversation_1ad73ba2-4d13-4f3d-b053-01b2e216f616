import { useCallback, useEffect, useRef } from 'react';
import useWebsocketStore from '@/src/store/useWebSocketStore';
import { WEBSOCKET_CHANNELS } from '@/src/core/constants/websocket';
import {
  GROUP_PLAY_EVENTS,
  groupPlayEventManager,
  GroupPlayEventType,
  registerGroupPlayEventHandlers,
} from '@/src/store/useGroupPlayStore/events';
import {
  GroupPlayEvent,
  GroupPlayEventHandlers,
} from '@/src/store/useGroupPlayStore/types';
import GroupPlayEventService from '@/src/modules/game/services/GroupPlayEventService';
import { useSession } from 'modules/auth/containers/AuthProvider';

interface UseGroupPlayEventsProps {
  gameId: string;
  onGameEvent?: (event: GroupPlayEvent) => void;
  eventHandlers?: Partial<GroupPlayEventHandlers>;
  enableWebSocketIntegration?: boolean;
}

interface UseGroupPlayEventsReturn {
  // Event emission
  emitEvent: (eventType: GroupPlayEventType, data: any) => void;

  // Event listening
  addEventListener: (
    eventType: GroupPlayEventType,
    handler: (event: GroupPlayEvent) => void,
  ) => () => void;
  removeEventListener: (
    eventType: GroupPlayEventType,
    handler: (event: GroupPlayEvent) => void,
  ) => void;

  // WebSocket integration
  sendWebSocketMessage: (message: any) => void;
  isWebSocketConnected: boolean;

  // Event history and debugging
  getEventHistory: (eventType?: GroupPlayEventType) => GroupPlayEvent[];
  clearEventHistory: () => void;
  enableDebugMode: () => void;
  disableDebugMode: () => void;

  // Event statistics
  getEventStats: () => {
    totalEvents: number;
    eventsByType: Record<string, number>;
    recentEvents: GroupPlayEvent[];
  };
}

const useGroupPlayEvents = ({
  gameId,
  onGameEvent,
  eventHandlers = {},
  enableWebSocketIntegration = true,
}: UseGroupPlayEventsProps): UseGroupPlayEventsReturn => {
  const { userId } = useSession();
  const eventServiceRef = useRef<GroupPlayEventService>();

  // WebSocket integration
  const {
    lastMessage,
    sendMessage,
    joinChannel,
    leaveChannel,
    isConnected: isWebSocketConnected,
  } = useWebsocketStore((state) => ({
    lastMessage: state.lastMessage,
    sendMessage: state.sendMessage,
    joinChannel: state.joinChannel,
    leaveChannel: state.leaveChannel,
    isConnected: state.isConnected,
  }));

  // Initialize event service
  useEffect(() => {
    if (!eventServiceRef.current) {
      eventServiceRef.current = GroupPlayEventService.getInstance();
    }
  }, []);

  // WebSocket channel management
  useEffect(() => {
    if (!enableWebSocketIntegration || !gameId) return;

    const channel = WEBSOCKET_CHANNELS.GameEvents(gameId);

    if (isWebSocketConnected) {
      joinChannel(channel);
    }

    return () => {
      leaveChannel(channel);
    };
  }, [
    gameId,
    isWebSocketConnected,
    enableWebSocketIntegration,
    joinChannel,
    leaveChannel,
  ]);

  // Process WebSocket messages
  useEffect(() => {
    if (!enableWebSocketIntegration || !gameId || !eventServiceRef.current)
      return;

    const channel = WEBSOCKET_CHANNELS.GameEvents(gameId);
    const message = lastMessage[channel];

    if (message) {
      eventServiceRef.current.processWebSocketEvent({
        type: 'websocket_message',
        channel,
        data: message,
        game: message.game,
        event: message.event,
        timestamp: Date.now(),
      });
    }
  }, [lastMessage, gameId, enableWebSocketIntegration]);

  // Register event handlers
  useEffect(() => {
    if (!gameId) return;

    const unsubscribe = registerGroupPlayEventHandlers(gameId, {
      ...eventHandlers,
      // Add global event handler if provided
      ...(onGameEvent && {
        onGameEvent: (event: GroupPlayEvent) => {
          if (event.gameId === gameId) {
            onGameEvent(event);
          }
        },
      }),
    });

    return unsubscribe;
  }, [gameId, eventHandlers, onGameEvent]);

  // Event emission
  const emitEvent = useCallback(
    (eventType: GroupPlayEventType, data: any) => {
      groupPlayEventManager.emit(eventType, data, gameId, userId);
    },
    [gameId, userId],
  );

  // Event listening
  const addEventListener = useCallback(
    (
      eventType: GroupPlayEventType,
      handler: (event: GroupPlayEvent) => void,
    ): (() => void) => {
      const wrappedHandler = (event: GroupPlayEvent) => {
        if (event.gameId === gameId) {
          handler(event);
        }
      };

      groupPlayEventManager.on(eventType, wrappedHandler);

      return () => {
        groupPlayEventManager.off(eventType, wrappedHandler);
      };
    },
    [gameId],
  );

  const removeEventListener = useCallback(
    (
      eventType: GroupPlayEventType,
      handler: (event: GroupPlayEvent) => void,
    ) => {
      groupPlayEventManager.off(eventType, handler);
    },
    [],
  );

  // WebSocket message sending
  const sendWebSocketMessage = useCallback(
    (message: any) => {
      if (!enableWebSocketIntegration) return;

      const channel = WEBSOCKET_CHANNELS.GameEvents(gameId);
      sendMessage({
        ...message,
        channel,
        gameId,
        userId,
        timestamp: Date.now(),
      });
    },
    [gameId, userId, sendMessage, enableWebSocketIntegration],
  );

  // Event history and debugging
  const getEventHistory = useCallback(
    (eventType?: GroupPlayEventType) =>
      groupPlayEventManager.getEventHistory(gameId, eventType),
    [gameId],
  );

  const clearEventHistory = useCallback(() => {
    groupPlayEventManager.clearHistory(gameId);
  }, [gameId]);

  const enableDebugMode = useCallback(() => {
    groupPlayEventManager.enableDebugMode();
  }, []);

  const disableDebugMode = useCallback(() => {
    groupPlayEventManager.disableDebugMode();
  }, []);

  // Event statistics
  const getEventStats = useCallback(() => {
    const history = getEventHistory();
    const eventsByType: Record<string, number> = {};

    history.forEach((event) => {
      eventsByType[event.type] = (eventsByType[event.type] || 0) + 1;
    });

    const recentEvents = history.slice(-10); // Last 10 events

    return {
      totalEvents: history.length,
      eventsByType,
      recentEvents,
    };
  }, [getEventHistory]);

  // Cleanup on unmount
  useEffect(
    () => () => {
      if (gameId) {
        clearEventHistory();
        groupPlayEventManager.removeAllListeners();
      }
    },
    [gameId, clearEventHistory],
  );

  return {
    // Event emission
    emitEvent,

    // Event listening
    addEventListener,
    removeEventListener,

    // WebSocket integration
    sendWebSocketMessage,
    isWebSocketConnected,

    // Event history and debugging
    getEventHistory,
    clearEventHistory,
    enableDebugMode,
    disableDebugMode,

    // Event statistics
    getEventStats,
  };
};

export default useGroupPlayEvents;

// Convenience hooks for specific event types
export const useGroupPlayQuestionEvents = (gameId: string) => {
  const { addEventListener, removeEventListener, emitEvent } =
    useGroupPlayEvents({ gameId });

  const onQuestionStart = useCallback(
    (handler: (event: GroupPlayEvent) => void) =>
      addEventListener(GROUP_PLAY_EVENTS.QUESTION_STARTED, handler),
    [addEventListener],
  );

  const onQuestionEnd = useCallback(
    (handler: (event: GroupPlayEvent) => void) =>
      addEventListener(GROUP_PLAY_EVENTS.QUESTION_ENDED, handler),
    [addEventListener],
  );

  const onAnswerSubmitted = useCallback(
    (handler: (event: GroupPlayEvent) => void) =>
      addEventListener(GROUP_PLAY_EVENTS.ANSWER_SUBMITTED, handler),
    [addEventListener],
  );

  const emitQuestionStart = useCallback(
    (questionId: string, data?: any) => {
      emitEvent(GROUP_PLAY_EVENTS.QUESTION_STARTED, { questionId, ...data });
    },
    [emitEvent],
  );

  const emitAnswerSubmission = useCallback(
    (questionId: string, answer: string, data?: any) => {
      emitEvent(GROUP_PLAY_EVENTS.ANSWER_SUBMITTED, {
        questionId,
        answer,
        ...data,
      });
    },
    [emitEvent],
  );

  return {
    onQuestionStart,
    onQuestionEnd,
    onAnswerSubmitted,
    emitQuestionStart,
    emitAnswerSubmission,
  };
};

export const useGroupPlayPlayerEvents = (gameId: string) => {
  const { addEventListener, removeEventListener, emitEvent } =
    useGroupPlayEvents({ gameId });

  const onPlayerJoined = useCallback(
    (handler: (event: GroupPlayEvent) => void) =>
      addEventListener(GROUP_PLAY_EVENTS.PLAYER_JOINED, handler),
    [addEventListener],
  );

  const onPlayerLeft = useCallback(
    (handler: (event: GroupPlayEvent) => void) =>
      addEventListener(GROUP_PLAY_EVENTS.PLAYER_LEFT, handler),
    [addEventListener],
  );

  const onPlayerScoreUpdated = useCallback(
    (handler: (event: GroupPlayEvent) => void) =>
      addEventListener(GROUP_PLAY_EVENTS.PLAYER_SCORE_UPDATED, handler),
    [addEventListener],
  );

  const emitPlayerJoined = useCallback(
    (userId: string, data?: any) => {
      emitEvent(GROUP_PLAY_EVENTS.PLAYER_JOINED, { userId, ...data });
    },
    [emitEvent],
  );

  const emitPlayerScoreUpdate = useCallback(
    (userId: string, score: number, data?: any) => {
      emitEvent(GROUP_PLAY_EVENTS.PLAYER_SCORE_UPDATED, {
        userId,
        score,
        ...data,
      });
    },
    [emitEvent],
  );

  return {
    onPlayerJoined,
    onPlayerLeft,
    onPlayerScoreUpdated,
    emitPlayerJoined,
    emitPlayerScoreUpdate,
  };
};

export const useGroupPlayGameEvents = (gameId: string) => {
  const { addEventListener, removeEventListener, emitEvent } =
    useGroupPlayEvents({ gameId });

  const onGameStarted = useCallback(
    (handler: (event: GroupPlayEvent) => void) =>
      addEventListener(GROUP_PLAY_EVENTS.GAME_STARTED, handler),
    [addEventListener],
  );

  const onGameEnded = useCallback(
    (handler: (event: GroupPlayEvent) => void) =>
      addEventListener(GROUP_PLAY_EVENTS.GAME_ENDED, handler),
    [addEventListener],
  );

  const onPhaseChanged = useCallback(
    (handler: (event: GroupPlayEvent) => void) =>
      addEventListener(GROUP_PLAY_EVENTS.PHASE_CHANGED, handler),
    [addEventListener],
  );

  const emitGameStart = useCallback(
    (data?: any) => {
      emitEvent(GROUP_PLAY_EVENTS.GAME_STARTED, data);
    },
    [emitEvent],
  );

  const emitGameEnd = useCallback(
    (data?: any) => {
      emitEvent(GROUP_PLAY_EVENTS.GAME_ENDED, data);
    },
    [emitEvent],
  );

  const emitPhaseChange = useCallback(
    (phase: string, data?: any) => {
      emitEvent(GROUP_PLAY_EVENTS.PHASE_CHANGED, { phase, ...data });
    },
    [emitEvent],
  );

  return {
    onGameStarted,
    onGameEnded,
    onPhaseChanged,
    emitGameStart,
    emitGameEnd,
    emitPhaseChange,
  };
};
