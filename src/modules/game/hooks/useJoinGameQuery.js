import { useMutation, gql } from '@apollo/client';

import { useCallback } from 'react';
import { GAME_FRAGMENT } from 'core/graphql/fragments/game';

const JOIN_GAME_QUERY = gql`
  ${GAME_FRAGMENT}
  mutation JoinGame($joinGameInput: JoinGameInput) {
    joinGame(joinGameInput: $joinGameInput) {
      ...CoreGameFields
    }
  }
`;

const useJoinGameQuery = () => {
  const [joinGameQuery] = useMutation(JOIN_GAME_QUERY);

  const joinGame = useCallback(
    ({ gameId }) => {
      return joinGameQuery({ variables: { joinGameInput: { gameId } } });
    },
    [joinGameQuery],
  );

  return {
    joinGame,
  };
};

export default useJoinGameQuery;
