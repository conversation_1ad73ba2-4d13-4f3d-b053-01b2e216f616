import { useEffect, useCallback, useMemo, useRef } from 'react';
import { useSession } from '@/src/core/contexts/SessionContext';
import useGroupPlayStore from '@/src/store/useGroupPlayStore';
import {
  GroupPlayGameState,
  GroupPlayQuestion,
  GroupPlayPlayer,
  GroupPlayGamePhase,
} from '@/src/store/useGroupPlayStore/types';

interface UseGroupPlayStoreProps {
  gameId: string;
  gameData?: any;
  autoInitialize?: boolean;
}

interface UseGroupPlayStoreReturn {
  // Game State
  gameState: GroupPlayGameState;
  isLoading: boolean;
  error: string | null;
  
  // Current Game Info
  currentQuestion: GroupPlayQuestion | null;
  currentPhase: GroupPlayGamePhase;
  timeRemaining: number;
  questionTimeRemaining: number;
  
  // Players & Leaderboard
  players: GroupPlayPlayer[];
  currentPlayer: GroupPlayPlayer | null;
  leaderboard: GroupPlayPlayer[];
  
  // Game Progress
  gameProgress: {
    current: number;
    total: number;
    percentage: number;
    solved: number;
    remaining: number;
  };
  
  // Actions
  initializeGame: (gameData: any) => void;
  submitAnswer: (answer: string) => Promise<boolean>;
  resetGame: () => void;
  
  // Phase Checks
  isQuestionPhase: boolean;
  isWaitingPhase: boolean;
  isGameReady: boolean;
  isGameActive: boolean;
  showLeaderboard: boolean;
  
  // Timing
  formatTime: (ms: number) => string;
  getCurrentServerTime: () => number;
}

const useGroupPlayStoreHook = ({
  gameId,
  gameData,
  autoInitialize = true,
}: UseGroupPlayStoreProps): UseGroupPlayStoreReturn => {
  const { userId } = useSession();
  
  // Store selectors
  const gameState = useGroupPlayStore((state) => state);
  const initializeGame = useGroupPlayStore((state) => state.initializeGame);
  const submitAnswer = useGroupPlayStore((state) => state.submitAnswer);
  const resetGame = useGroupPlayStore((state) => state.resetGame);
  const updateCurrentQuestion = useGroupPlayStore((state) => state.updateCurrentQuestion);
  const updateCycleTime = useGroupPlayStore((state) => state.updateCycleTime);
  
  // Refs for stable references
  const gameIdRef = useRef(gameId);
  const userIdRef = useRef(userId);
  
  // Initialize game when gameData is available
  useEffect(() => {
    if (autoInitialize && gameData && gameId && gameId !== gameState.gameId) {
      initializeGame(gameData);
      
      // Set current user ID
      if (userId && userId !== gameState.currentUserId) {
        useGroupPlayStore.setState((state) => {
          state.currentUserId = userId;
        });
      }
    }
  }, [gameData, gameId, userId, autoInitialize, initializeGame, gameState.gameId, gameState.currentUserId]);

  // Update current question periodically
  useEffect(() => {
    if (!gameState.isGameActive || !gameState.isGameReady) return;
    
    const interval = setInterval(() => {
      updateCurrentQuestion();
      updateCycleTime();
    }, 1000);
    
    return () => clearInterval(interval);
  }, [gameState.isGameActive, gameState.isGameReady, updateCurrentQuestion, updateCycleTime]);

  // Cleanup on unmount or game change
  useEffect(() => {
    return () => {
      if (gameIdRef.current !== gameId) {
        resetGame();
      }
    };
  }, [gameId, resetGame]);

  // Derived state
  const currentQuestion = useMemo(() => {
    if (!gameState.currentQuestionId) return null;
    return gameState.questions[gameState.currentQuestionId] || null;
  }, [gameState.currentQuestionId, gameState.questions]);

  const currentPlayer = useMemo(() => {
    if (!userId) return null;
    return gameState.players[userId] || null;
  }, [userId, gameState.players]);

  const players = useMemo(() => {
    return Object.values(gameState.players);
  }, [gameState.players]);

  const gameProgress = useMemo(() => {
    const total = gameState.totalQuestions;
    const current = gameState.currentQuestionIndex + 1;
    const solved = Object.values(gameState.questions).filter(q => q.hasSolved).length;
    const remaining = total - current;
    const percentage = total > 0 ? (current / total) * 100 : 0;

    return {
      current,
      total,
      percentage,
      solved,
      remaining,
    };
  }, [gameState.totalQuestions, gameState.currentQuestionIndex, gameState.questions]);

  // Phase checks
  const isQuestionPhase = gameState.currentPhase.type === 'QUESTION';
  const isWaitingPhase = gameState.currentPhase.type === 'WAITING';
  const isGameReady = gameState.isGameReady;
  const isGameActive = gameState.isGameActive;
  const showLeaderboard = gameState.showLeaderboard;

  // Time calculations
  const timeRemaining = useMemo(() => {
    const currentTime = Date.now() + gameState.serverTimeOffset;
    const gameEndTime = gameState.startTime + (gameState.config.timeLimit * 1000);
    return Math.max(0, gameEndTime - currentTime);
  }, [gameState.startTime, gameState.config.timeLimit, gameState.serverTimeOffset]);

  const questionTimeRemaining = useMemo(() => {
    if (!isQuestionPhase) return 0;
    const currentTime = Date.now() + gameState.serverTimeOffset;
    return Math.max(0, gameState.currentPhase.endTime - currentTime);
  }, [isQuestionPhase, gameState.currentPhase.endTime, gameState.serverTimeOffset]);

  // Utility functions
  const formatTime = useCallback((ms: number): string => {
    const seconds = Math.ceil(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes > 0) {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${remainingSeconds}`;
  }, []);

  const getCurrentServerTime = useCallback((): number => {
    return Date.now() + gameState.serverTimeOffset;
  }, [gameState.serverTimeOffset]);

  // Enhanced submit answer with optimistic updates
  const handleSubmitAnswer = useCallback(async (answer: string): Promise<boolean> => {
    if (!currentQuestion || gameState.isSubmitting) {
      return false;
    }

    try {
      const result = await submitAnswer(answer);
      return result;
    } catch (error) {
      console.error('Error submitting answer:', error);
      return false;
    }
  }, [currentQuestion, gameState.isSubmitting, submitAnswer]);

  return {
    // Game State
    gameState,
    isLoading: gameState.loading,
    error: gameState.error,
    
    // Current Game Info
    currentQuestion,
    currentPhase: gameState.currentPhase,
    timeRemaining,
    questionTimeRemaining,
    
    // Players & Leaderboard
    players,
    currentPlayer,
    leaderboard: gameState.leaderboard,
    
    // Game Progress
    gameProgress,
    
    // Actions
    initializeGame,
    submitAnswer: handleSubmitAnswer,
    resetGame,
    
    // Phase Checks
    isQuestionPhase,
    isWaitingPhase,
    isGameReady,
    isGameActive,
    showLeaderboard,
    
    // Timing
    formatTime,
    getCurrentServerTime,
  };
};

export default useGroupPlayStoreHook;
