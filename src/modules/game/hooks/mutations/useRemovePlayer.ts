import { gql, useMutation } from '@apollo/client';
import { useCallback, useState } from 'react';

const REMOVE_PLAYER_MUTATION = gql`
  mutation RemovePlayer($gameId: ID!, $playerId: ID!) {
    removePlayer(gameId: $gameId, playerId: $playerId)
  }
`;

const useRemovePlayerFromGame = () => {
  const [removePlayerQuery] = useMutation(REMOVE_PLAYER_MUTATION);
  const [removingPlayer, setIsRemovingPlayer] = useState(false);

  const removePlayerFromGame = useCallback(
    async ({ gameId, playerId }: { playerId: string; gameId: string }) => {
      if (removingPlayer) {
        return false;
      }
      try {
        setIsRemovingPlayer(true);
        const responseOfRemovePlayer = await removePlayerQuery({
          variables: {
            gameId: gameId,
            playerId: playerId,
          },
        });
        const { data } = responseOfRemovePlayer ?? EMPTY_OBJECT;

        setIsRemovingPlayer(false);

        return data?.removePlayer ?? false;
      } catch (e) {
        setIsRemovingPlayer(false);
        return false;
      } finally {
        setIsRemovingPlayer(false);
      }
    },
    [removePlayerQuery, removingPlayer],
  );

  return {
    removePlayerFromGame,
    removingPlayer,
  };
};

export default useRemovePlayerFromGame;
