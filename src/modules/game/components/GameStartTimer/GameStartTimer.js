import React, { useEffect, useRef, useState } from 'react'
import { View } from 'react-native'
import { Text } from '@rneui/themed'
import { useNavigation } from '@react-navigation/native'
import _toNumber from 'lodash/toNumber'
import PropTypes from 'prop-types'
import styles from './GameStartTimer.style'
import dark from '../../../../core/constants/themes/dark'
// import GameLobbyPlayerCards from '../GameLobbyPlayerCards/GameLobbyPlayerCards'

const GameStartTimer = (props) => {
  const { time = 0 } = props
  const [timer, setTimer] = useState(Math.ceil(_toNumber(time) / 1000))
  const navigation = useNavigation()

  const currTimeRef = useRef()

  useEffect(() => {
    if (currTimeRef.current) {
      clearInterval(currTimeRef.current)
    }

    currTimeRef.current = setInterval(() => {
      setTimer((prevTimer) => Math.max(prevTimer - 1, 0))
    }, 1000)

    return () => clearInterval(currTimeRef.current)
  }, [timer, navigation])

  return (
    <View
      style={{
        padding: 0,
        margin: 0,
        flex: 1,
        backgroundColor: dark.colors.background,
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Text style={styles.timerText}>
        Starting in <Text style={styles.time}>{timer}</Text>
      </Text>
    </View>
  )
}

GameStartTimer.propTypes = {
  isVisible: PropTypes.bool,
  time: PropTypes.number,
}

export default React.memo(GameStartTimer)
