import React, { useCallback, useEffect, useRef } from 'react';
import { Animated, Pressable, StyleSheet, View } from 'react-native';
import { Text } from '@rneui/themed';
import Dark from '@/src/core/constants/themes/dark';
import _isEmpty from 'lodash/isEmpty';

import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import UserImage from 'atoms/UserImage/UserImage';
import userReader from 'core/readers/userReader';

import { useRouter } from 'expo-router';
import { GAME_TYPES } from 'modules/game/constants/game';

const GameHeaderPlayerCard = ({ user = EMPTY_OBJECT, score = 0, gameType }) => {
  const { user: currentUser } = useSession();
  const router = useRouter();
  const isCurrentUser = user?._id === currentUser?._id;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (!_isEmpty(user)) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      fadeAnim.setValue(0);
    }
  }, [user, fadeAnim]);

  const userName = isCurrentUser ? 'You' : userReader.username(user);

  let userRating = userReader.rating(user);
  if (gameType === GAME_TYPES.FLASH_ANZAN) {
    userRating = userReader.flashAnzanRating(user);
  } else if (gameType === GAME_TYPES.DMAS_ABILITY) {
    userRating = userReader.abilityDuelsRating(user);
  }

  const onPressUserCard = useCallback(() => {
    const username = userReader.username(user);
    router.navigate(`/profile/${username}`);
  }, [user, router]);

  return (
    <Animated.View style={{ opacity: fadeAnim }}>
      <Pressable onPress={onPressUserCard}>
        <View style={styles.background}>
          <View
            style={[
              styles.card,
              !isCurrentUser && { flexDirection: 'row-reverse' },
            ]}
          >
            <UserImage
              size={50}
              user={user}
              rounded={false}
              style={[
                styles.userImage,
                !isCurrentUser && {
                  borderColor: Dark.colors.headerOpponentBorderColor,
                },
              ]}
            />
            <View style={styles.userInfo}>
              <View style={styles.userNameContainer}>
                <Text style={styles.userName} numberOfLines={1}>
                  {userName}
                </Text>
                <Text
                  style={[
                    styles.userRating,
                    !isCurrentUser && { alignSelf: 'flex-end' },
                  ]}
                  numberOfLines={1}
                >
                  {userRating}
                </Text>
              </View>
            </View>
          </View>
          <View
            style={[
              styles.scoreContainer,
              !isCurrentUser && { alignSelf: 'flex-end' },
            ]}
          >
            <Text style={[styles.userScore]}>{score}</Text>
          </View>
        </View>
      </Pressable>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  background: {
    minWidth: 120,
    maxWidth: 240,
    height: 80,
    borderRadius: 8,
    overflow: 'hidden',
  },
  card: {
    flex: 1,
    height: 50,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    gap: 8,
  },
  userInfo: {
    alignItems: 'right',
  },
  userNameContainer: {
    flexDirection: 'column',
  },
  userName: {
    fontSize: 12,
    maxWidth: 70,
    color: Dark.colors.textLight,
    fontFamily: 'Montserrat-700',
  },
  userRating: {
    maxWidth: 40,
    fontSize: 10,
    color: Dark.colors.textLight,
    opacity: 0.6,
    lineHeight: 12,
    fontFamily: 'Montserrat-500',
  },
  scoreContainer: {
    width: 48,
    height: 23,
    borderRadius: 8,
    backgroundColor: Dark.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 0.5,
    borderColor: Dark.colors.tertiary,
  },
  userScore: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: 'white',
    marginHorizontal: 0,
  },
  userImage: {
    width: 40,
    height: 40,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: Dark.colors.headerUserBorderColor,
    overflow: 'hidden',
  },
});

export default React.memo(GameHeaderPlayerCard);
