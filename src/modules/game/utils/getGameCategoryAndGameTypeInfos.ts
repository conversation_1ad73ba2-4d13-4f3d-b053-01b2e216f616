import {
  GAME_CATEGORY,
  GAME_MODES,
  GAME_TYPES,
} from 'modules/game/constants/game';

const getGameCategorySpecificConfigKey = (gameCategory: string) => {
  switch (gameCategory) {
    case GAME_CATEGORY.BLITZ:
      return 'blitz';
    case GAME_CATEGORY.CLASSICAL:
      return 'classical';
    case GAME_CATEGORY.MEMORY:
      return 'memory';
    case GAME_CATEGORY.PUZZLE:
      return 'puzzle';
    default:
      return 'blitz';
  }
};

const getModeSpecificConfigKey = (gameMode: string) => {
  switch (gameMode) {
    case GAME_MODES.ONLINE_CHALLENGE:
      return 'onlineChallenge';
    case GAME_MODES.ONLINE_SEARCH:
      return 'onlineSearch';
    case GAME_MODES.GROUP_PLAY:
      return 'groupPlay';
    case GAME_MODES.PRACTICE:
      return 'practice';
    case GAME_MODES.RUSH_WITH_TIME:
      return 'rushWithTime';
    case GAME_MODES.RUSH_WITHOUT_TIME:
      return 'rushWithoutTime';
    case GAME_MODES.PLAY_VIA_LINK:
      return 'playViaLink';
    case GAME_MODES.SUMDAY_SHOWDOWN:
      return 'sumdayShowdown';
    case GAME_MODES.SURVIVAL_SATURDAY:
      return 'survivalSaturday';
    default:
      return 'onlineSearch';
  }
};

const getGameCategoryFromGameType = (gameType: string) => {
  switch (gameType) {
    case GAME_TYPES.DMAS:
      return GAME_CATEGORY.BLITZ;
    case GAME_TYPES.DMAS_TIME_BANK:
      return GAME_CATEGORY.BLITZ;
    case GAME_TYPES.FASTEST_FINGER:
      return GAME_CATEGORY.BLITZ;
    case GAME_TYPES.FLASH_ANZAN:
      return GAME_CATEGORY.MEMORY;
    case GAME_TYPES.DMAS_ABILITY:
      return GAME_CATEGORY.CLASSICAL;
    case GAME_TYPES.CROSS_MATH_PUZZLE:
      return GAME_CATEGORY.PUZZLE;
    case GAME_TYPES.KEN_KEN_PUZZLE:
      return GAME_CATEGORY.PUZZLE;
    default:
      return GAME_CATEGORY.BLITZ;
  }
};

export const getGameConfigInputForGame = ({
  gameType,
  gameMode,
  timeLimit,
  configs,
}: {
  gameType: string;
  gameMode: string;
  timeLimit: number;
  configs: object;
}) => {
  let newGameType = gameType;

  switch (newGameType) {
    case GAME_TYPES.PLAY_ONLINE:
      newGameType = GAME_TYPES.DMAS;
      break;
    case GAME_TYPES.GROUP_PLAY:
      newGameType = GAME_TYPES.DMAS;
      break;
    case GAME_TYPES.PLAY_WITH_FRIEND:
      newGameType = GAME_TYPES.DMAS;
      break;
    case GAME_TYPES.ABILITY_DUELS:
      newGameType = GAME_TYPES.DMAS_ABILITY;
      break;
    default:
      newGameType = gameType;
      break;
  }

  const gameCategory = getGameCategoryFromGameType(newGameType);

  return {
    categorySpecificConfig: {
      category: gameCategory,
      [getGameCategorySpecificConfigKey(gameCategory)]: {
        timeLimit,
      },
    },
    gameTypeSpecificConfig: {
      type: newGameType,
    },
    modeSpecificConfig: {
      mode: gameMode,
      [getModeSpecificConfigKey(gameMode)]: {
        ...configs,
      },
    },
  };
};
