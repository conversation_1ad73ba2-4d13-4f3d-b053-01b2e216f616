/**
 * Migration utilities for transitioning from old GroupPlay implementation to new Zustand-based architecture
 */

import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _reduce from 'lodash/reduce';
import _map from 'lodash/map';
import { GroupPlayGameState } from '@/src/store/useGroupPlayStore/types';

// Legacy interfaces for migration
interface LegacyGameData {
  _id: string;
  startTime: string;
  config: {
    timeLimit: number;
    maxTimePerQuestion: number;
    numPlayers: number;
  };
  players: any[];
  questions: any[];
  userSubmissionsWithQuestion: any[];
  gameStatus: string;
}

interface LegacyQuestionState {
  [questionId: string]: {
    question: any;
    hasSolved: boolean;
    incorrectAttempts: number;
  };
}

/**
 * Migrates legacy game data to new GroupPlay store format
 */
export const migrateLegacyGameData = (legacyGame: LegacyGameData): any => {
  if (!legacyGame || _isEmpty(legacyGame)) {
    throw new Error('Invalid legacy game data provided');
  }

  const {
    _id: gameId,
    startTime,
    config,
    players = [],
    questions = [],
    userSubmissionsWithQuestion = [],
    gameStatus,
  } = legacyGame;

  // Migrate config
  const migratedConfig = {
    timeLimit: _get(config, 'timeLimit', 300),
    maxTimePerQuestion: _get(config, 'maxTimePerQuestion', 10),
    waitingTime: 5, // Default waiting time
    numPlayers: _get(config, 'numPlayers', 4),
    gameType: _get(legacyGame, 'gameType', ''),
    gameMode: _get(legacyGame, 'gameMode', ''),
  };

  // Migrate questions
  const migratedQuestions = _map(questions, (questionObj) => ({
    question: questionObj.question,
    submissions: questionObj.submissions || [],
  }));

  // Migrate players
  const migratedPlayers = _map(players, (player) => ({
    userId: player.userId,
    userName: player.userName || `Player ${player.userId?.slice(-4)}`,
    score: player.score || 0,
  }));

  return {
    _id: gameId,
    startTime,
    endTime: legacyGame.endTime,
    config: migratedConfig,
    players: migratedPlayers,
    questions: migratedQuestions,
    userSubmissionsWithQuestion,
    gameStatus,
    // Add any additional fields that might be needed
    leaderBoard: legacyGame.leaderBoard || [],
  };
};

/**
 * Migrates legacy question state to new format
 */
export const migrateLegacyQuestionState = (
  legacyQuestions: LegacyQuestionState,
  allQuestions: any[]
): Record<string, any> => {
  return _reduce(
    legacyQuestions,
    (acc, questionState, questionId) => {
      const questionData = allQuestions.find(
        (q) => _get(q, ['question', 'id']) === questionId
      );

      acc[questionId] = {
        id: questionId,
        question: questionState.question,
        answers: questionState.question?.answers || [],
        maxTimeLimit: _get(questionState.question, 'maxTimeLimit', 10) * 1000,
        hasSolved: questionState.hasSolved,
        incorrectAttempts: questionState.incorrectAttempts,
        submittedAnswer: undefined,
        submissionTime: undefined,
        isCorrect: undefined,
      };

      return acc;
    },
    {}
  );
};

/**
 * Converts legacy WebSocket events to new event format
 */
export const migrateLegacyWebSocketEvent = (legacyEvent: any): any => {
  const { type, data, game, event: eventType } = legacyEvent;

  // Map legacy event types to new event types
  const eventTypeMapping: Record<string, string> = {
    CORRECT_MOVE_MADE: 'ANSWER_CORRECT',
    INCORRECT_MOVE_MADE: 'ANSWER_INCORRECT',
    USER_JOINED: 'PLAYER_JOINED',
    PLAYER_REMOVED: 'PLAYER_LEFT',
    GAME_STARTED: 'GAME_STARTED',
    GAME_ENDED: 'GAME_ENDED',
  };

  const newEventType = eventTypeMapping[eventType || type] || eventType || type;

  return {
    id: `migrated-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type: newEventType,
    gameId: _get(game, '_id') || _get(data, 'gameId'),
    userId: _get(data, 'userId'),
    timestamp: Date.now(),
    data: {
      ...data,
      originalEvent: legacyEvent,
      migrated: true,
    },
  };
};

/**
 * Migration validation utilities
 */
export const validateMigratedData = (migratedData: any): boolean => {
  const requiredFields = [
    '_id',
    'startTime',
    'config',
    'players',
    'questions',
    'gameStatus',
  ];

  return requiredFields.every((field) => !_isEmpty(_get(migratedData, field)));
};

/**
 * Performance comparison utilities
 */
export class PerformanceComparison {
  private metrics: Record<string, number[]> = {};

  startMeasurement(key: string): void {
    if (!this.metrics[key]) {
      this.metrics[key] = [];
    }
    this.metrics[key].push(performance.now());
  }

  endMeasurement(key: string): number {
    if (!this.metrics[key] || this.metrics[key].length === 0) {
      return 0;
    }

    const startTime = this.metrics[key].pop()!;
    const duration = performance.now() - startTime;
    
    // Store the duration for analysis
    if (!this.metrics[`${key}_durations`]) {
      this.metrics[`${key}_durations`] = [];
    }
    this.metrics[`${key}_durations`].push(duration);

    return duration;
  }

  getAverageTime(key: string): number {
    const durations = this.metrics[`${key}_durations`] || [];
    if (durations.length === 0) return 0;
    
    return durations.reduce((sum, duration) => sum + duration, 0) / durations.length;
  }

  getMetrics(): Record<string, any> {
    const result: Record<string, any> = {};
    
    Object.keys(this.metrics).forEach((key) => {
      if (key.endsWith('_durations')) {
        const baseKey = key.replace('_durations', '');
        const durations = this.metrics[key];
        
        result[baseKey] = {
          count: durations.length,
          average: this.getAverageTime(baseKey),
          min: Math.min(...durations),
          max: Math.max(...durations),
          total: durations.reduce((sum, d) => sum + d, 0),
        };
      }
    });

    return result;
  }

  reset(): void {
    this.metrics = {};
  }
}

/**
 * Migration helper for component updates
 */
export const createMigrationHelper = () => {
  const performanceComparison = new PerformanceComparison();
  
  return {
    // Measure legacy vs new implementation performance
    measureLegacyOperation: (operation: () => any, key: string) => {
      performanceComparison.startMeasurement(`legacy_${key}`);
      const result = operation();
      performanceComparison.endMeasurement(`legacy_${key}`);
      return result;
    },

    measureNewOperation: (operation: () => any, key: string) => {
      performanceComparison.startMeasurement(`new_${key}`);
      const result = operation();
      performanceComparison.endMeasurement(`new_${key}`);
      return result;
    },

    getPerformanceReport: () => {
      const metrics = performanceComparison.getMetrics();
      const report: Record<string, any> = {};

      Object.keys(metrics).forEach((key) => {
        if (key.startsWith('legacy_')) {
          const operation = key.replace('legacy_', '');
          const newKey = `new_${operation}`;
          
          if (metrics[newKey]) {
            report[operation] = {
              legacy: metrics[key],
              new: metrics[newKey],
              improvement: {
                averageSpeedup: metrics[key].average / metrics[newKey].average,
                totalTimeSaved: metrics[key].total - metrics[newKey].total,
              },
            };
          }
        }
      });

      return report;
    },

    resetMetrics: () => {
      performanceComparison.reset();
    },
  };
};

/**
 * Debugging utilities for migration
 */
export const createMigrationDebugger = () => {
  const logs: Array<{ timestamp: number; level: string; message: string; data?: any }> = [];

  const log = (level: 'info' | 'warn' | 'error', message: string, data?: any) => {
    const logEntry = {
      timestamp: Date.now(),
      level,
      message,
      data,
    };
    
    logs.push(logEntry);
    console[level](`[Migration] ${message}`, data || '');
  };

  return {
    info: (message: string, data?: any) => log('info', message, data),
    warn: (message: string, data?: any) => log('warn', message, data),
    error: (message: string, data?: any) => log('error', message, data),
    
    getLogs: () => logs,
    
    exportLogs: () => {
      return JSON.stringify(logs, null, 2);
    },
    
    clearLogs: () => {
      logs.length = 0;
    },
  };
};

// Export singleton instances for global use
export const migrationHelper = createMigrationHelper();
export const migrationDebugger = createMigrationDebugger();

// Migration status tracking
export const MigrationStatus = {
  NOT_STARTED: 'not_started',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  FAILED: 'failed',
} as const;

export type MigrationStatusType = typeof MigrationStatus[keyof typeof MigrationStatus];

export interface MigrationState {
  status: MigrationStatusType;
  startTime?: number;
  endTime?: number;
  errors: string[];
  warnings: string[];
  migratedComponents: string[];
}

export const createMigrationTracker = (): {
  getStatus: () => MigrationState;
  startMigration: () => void;
  completeMigration: () => void;
  failMigration: (error: string) => void;
  addWarning: (warning: string) => void;
  markComponentMigrated: (component: string) => void;
} => {
  let state: MigrationState = {
    status: MigrationStatus.NOT_STARTED,
    errors: [],
    warnings: [],
    migratedComponents: [],
  };

  return {
    getStatus: () => ({ ...state }),
    
    startMigration: () => {
      state = {
        ...state,
        status: MigrationStatus.IN_PROGRESS,
        startTime: Date.now(),
      };
    },
    
    completeMigration: () => {
      state = {
        ...state,
        status: MigrationStatus.COMPLETED,
        endTime: Date.now(),
      };
    },
    
    failMigration: (error: string) => {
      state = {
        ...state,
        status: MigrationStatus.FAILED,
        endTime: Date.now(),
        errors: [...state.errors, error],
      };
    },
    
    addWarning: (warning: string) => {
      state = {
        ...state,
        warnings: [...state.warnings, warning],
      };
    },
    
    markComponentMigrated: (component: string) => {
      state = {
        ...state,
        migratedComponents: [...state.migratedComponents, component],
      };
    },
  };
};
