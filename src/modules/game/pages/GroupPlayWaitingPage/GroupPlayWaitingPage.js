import React, { useCallback, useMemo } from 'react';
import _map from 'lodash/map';
import _size from 'lodash/size';
import _isEqual from 'lodash/isEqual';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { Dimensions, ScrollView, Text, View } from 'react-native';
import PrimaryButton from 'atoms/PrimaryButton';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import PropTypes from 'prop-types';
import dark from 'core/constants/themes/dark';
import useGoBack from 'navigator/hooks/useGoBack';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Header from 'shared/Header/Header';
import WebBackButton from 'shared/WebBackButton';
import styles from './GroupPlayWaitingPage.style';
import JoinedPlayersCard from './components/JoinedPlayersCard/JoinedPlayersCard';
import useGameContext from '../../hooks/useGameContext';
import useStartGameQuery from '../../../home/<USER>/useStartGameQuery';
import useLeaveGame from '../../hooks/mutations/useLeaveGame';
import GroupPlayShareLink from './components/GroupPlayShareLink';
import WaitingForAdminToStart from './components/WaitingForAdminToStart';

const GroupPlayWaitingPage = (props) => {
  const { userId } = useSession();
  const { game } = props;

  const { players } = useGameContext();
  const { startGame } = useStartGameQuery();
  const { leavingGame, leaveGame } = useLeaveGame();
  const { goBack } = useGoBack();
  const { gameStatus, gameType, config, createdBy, _id: gameId } = game;
  const { numPlayers } = config;
  const isGameOwner = useMemo(
    () => _isEqual(userId, createdBy),
    [userId, createdBy],
  );

  const handleStartGame = useCallback(() => {
    startGame(gameId);
    showToast({
      type: TOAST_TYPE.LOADING,
      description: 'Starting Game',
    });
  }, [startGame, gameId]);

  const handleLeaveGame = useCallback(() => {
    leaveGame({ gameId }).then((isLeftGame) => {
      if (isLeftGame) {
        goBack();
      }
    });
  }, [gameId, goBack]);

  const { isMobile: isCompactMode } = useMediaQuery();

  return (
    <View style={{ flex: 1 }}>
      <Header title="Game" />
      <View
        style={[
          styles.innerContainer,
          !isCompactMode && { paddingHorizontal: 50 },
        ]}
      >
        {!isCompactMode && (
          <View
            style={{
              ...styles.header,
              backgroundColor: 'transparent',
            }}
          >
            <View style={styles.headerLeft}>
              <WebBackButton />
              <Text style={styles.headerTitle}>Game</Text>
            </View>
            <View
              style={{ flexDirection: 'row', alignItems: 'center', gap: 15 }}
            >
              <Text
                style={{
                  fontFamily: 'Montserrat-500',
                  fontSize: 15,
                  lineHeight: 24,
                  color: 'white',
                }}
              >
                <Text style={{ fontFamily: 'Montserrat-700' }}>
                  {_size(players)}
                </Text>{' '}
                Joined So Far
              </Text>
              {_size(players) >= 2 && isGameOwner && (
                <PrimaryButton
                  onPress={handleStartGame}
                  label="Start Game"
                  radius={20}
                  buttonStyle={{
                    height: 35,
                    width: 180,
                  }}
                />
              )}
            </View>
          </View>
        )}

        {isGameOwner && <GroupPlayShareLink gameId={gameId} />}

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          {_map(players, (player, index) => (
            <JoinedPlayersCard
              key={`${index}-${player?._id}`}
              gameId={gameId}
              userDetailsData={player}
              creatorId={createdBy}
              isAdmin={_isEqual(createdBy, player?._id)}
            />
          ))}
        </ScrollView>

        {!isGameOwner && <WaitingForAdminToStart />}

        {isGameOwner && _size(players) >= 2 && isCompactMode && (
          <PrimaryButton
            onPress={handleStartGame}
            label="Start Game"
            radius={20}
            buttonStyle={{
              height: 40,
              width: Dimensions.get('window').width - 32,
            }}
          />
        )}

        {!isGameOwner && _size(players) >= 2 && (
          <View style={{ alignItems: 'center' }}>
            <PrimaryButton
              onPress={handleLeaveGame}
              label={leavingGame ? 'Leaving Game...' : 'Leave Game'}
              radius={20}
              buttonStyle={{
                height: 40,
                width: 180,
                backgroundColor: 'transparent',
              }}
              labelStyle={{
                color: dark.colors.textDark,
                fontFamily: 'Montserrat-500',
              }}
            />
          </View>
        )}
      </View>
    </View>
  );
};

GroupPlayWaitingPage.propTypes = {
  game: PropTypes.object,
};

export default React.memo(GroupPlayWaitingPage);
