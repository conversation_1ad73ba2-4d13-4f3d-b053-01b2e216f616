import { Text, TouchableOpacity, View } from 'react-native';
import * as Clipboard from 'expo-clipboard';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import { getSiteUrl } from 'core/constants/appConstants';
import Ionicons from '@expo/vector-icons/Ionicons';
import dark from 'core/constants/themes/dark';
import useMediaQuery from 'core/hooks/useMediaQuery';
import useNativeUrlSharing from 'core/hooks/useNativeUrlSharing';
import React, { useCallback, useMemo } from 'react';
import styles from './GroupPlayShareLink.style';

const GroupPlayShareLink = ({ gameId }) => {
  const siteUrl = useMemo(() => getSiteUrl(), []);
  const shareLink = useMemo(
    () => `${siteUrl}/game/${gameId}/play`,
    [gameId, siteUrl],
  );

  const { handleShare: handleShareLink } = useNativeUrlSharing({
    url: shareLink,
  });

  const handleCopyLink = useCallback(async () => {
    await Clipboard.setStringAsync(shareLink);
    showToast({
      type: TOAST_TYPE.SUCCESS,
      description: 'Copied game url to clipboard',
    });
  }, [shareLink]);

  const { isMobile: isCompactMode } = useMediaQuery();

  if (!isCompactMode) {
    return (
      <View style={[styles.container, { flexDirection: 'row' }]}>
        <View style={styles.linkIconContainer}>
          <Ionicons name="link" size={20} color={dark.colors.textDark} />
        </View>
        <View
          style={[
            styles.infoSection,
            {
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              width: '95%',
            },
          ]}
        >
          <Text style={styles.shareInfoText}>
            Share the link with your friends to join the room.
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity onPress={handleCopyLink}>
              <Text style={styles.buttonText}>Copy link</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={handleShareLink}>
              <Text style={styles.buttonText}>Share link</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.linkIconContainer}>
        <Ionicons name="link" size={20} color={dark.colors.textDark} />
      </View>
      <View style={styles.infoSection}>
        <View style={{ width: '80%' }}>
          <Text style={styles.shareInfoText}>
            Share the link with your friends to join the room.
          </Text>
        </View>
        <View style={styles.buttonContainer}>
          <TouchableOpacity onPress={handleCopyLink}>
            <Text style={styles.buttonText}>Copy link</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={handleShareLink}>
            <Text style={styles.buttonText}>Share link</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default React.memo(GroupPlayShareLink);
