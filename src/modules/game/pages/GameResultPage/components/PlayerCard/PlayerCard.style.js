import { StyleSheet } from 'react-native';

import dark from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  background: {
    flex: 1,
    borderRadius: 16,
    minWidth: 90,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
  },
  winnerContainer: {
    borderColor: dark.colors.victoryColor,
    borderRadius: 14,
    borderWidth: 1,
  },
  loserContainer: {
    borderWidth: 1,
    borderRadius: 14,
    borderColor: dark.colors.defeatColor,
  },
  defaultContainer: {
    borderColor: dark.colors.tertiary,
    borderRadius: 14,
    borderWidth: 1,
  },
  image2: {
    position: 'absolute',
    height: 60,
    width: 50,
  },
  image1: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: 80,
    width: 60,
  },
  card: {
    flex: 1,
    alignItems: 'center',
    borderRadius: 8,
    padding: 8,
    paddingTop: 16,
    gap: 8,
  },
  userInfo: {
    flex: 1,
    alignItems: 'center',
    gap: 4,
  },
  userName: {
    fontFamily: 'Montserrat-500',
    marginHorizontal: 4,
    fontSize: 10,
    color: dark.colors.textDark,
    overflow: 'hidden',
    textoverflow: 'ellipsis',
  },
  userScore: {
    fontSize: 10,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textLight,
    opacity: 0.6,
  },
});

export default styles;