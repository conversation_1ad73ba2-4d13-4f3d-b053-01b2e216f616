import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const createStyles = () => StyleSheet.create({
    
    resultsContainer: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
    },
    playerContainer: {
      alignItems: 'center',
    },
    scoresContainer: {
      flex: 1,
      marginTop: 40,
      minWidth: 140,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'column',
    
    },
    digitScoreContainer: {
      width: 75,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    scoreText: {
      textAlign: 'center',
      fontSize: 12,
      fontFamily: 'Montserrat-700',
      color: 'white',
      marginBottom: 14,
    },
    totalScoreContainer: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      minWidth: 100,
      height: 40,

    },
    totalScore: {
      width: 32,
      height: 24,
      backgroundColor: 'transparent',
      borderWidth: 2,
      borderRadius: 4,
      borderColor: dark.colors.tertiary,
      alignItems: 'center',
      justifyContent: 'center',
    },
    innerScoreText: {
      fontFamily: 'Montserrat-700',
      fontSize: 12,
      color: dark.colors.textLight,
    },
    hyphen: {
      fontFamily: 'Montserrat-700',
      color: dark.colors.textLight,
    },
});


const useGameResultPlayersStyles = () => {
  const styles = createStyles();
  return styles;
};

export default useGameResultPlayersStyles;