import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import _map from 'lodash/map';
import _findIndex from 'lodash/findIndex';
import _isEmpty from 'lodash/isEmpty';
import PropTypes from 'prop-types';
import useMediaQuery from 'core/hooks/useMediaQuery';
import dark from 'core/constants/themes/dark';
import Icon, { ICON_TYPES } from 'atoms/Icon';
import Loading from 'atoms/Loading';
import { useRouter } from 'expo-router';
import { showPopover } from 'molecules/Popover/Popover';
import Header from 'shared/Header';
import { GAME_TYPES } from 'core/constants/gameTypes';
import WebBackButton from 'shared/WebBackButton';
import _get from 'lodash/get';
import { useSession } from 'modules/auth/containers/AuthProvider';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import _isNil from 'lodash/isNil';
import _isArray from 'lodash/isArray';
import _isObject from 'lodash/isObject';
import gameReader from '@/src/core/readers/gameReader';
import gameLeaderboardReader from '@/src/core/readers/gameLeaderboardReader';
import GameResultFooter from './components/GameResultFooter';
import useHandleGameLeaderboard from '../../hooks/useHandleGameLeaderboard';
import useGameResultStyles from './GameResult.style';
import useGameContext from '../../hooks/useGameContext';
import GameStatsRow from '../GameAnalysis/components/GameStatsRow';
import useGetGameStats from '../../hooks/useGetGameStats';
import GameResultOverlay from '../../components/GameResultOverlay';
import NewGameAnalysisHeader from '../NewGameAnalysis/NewGameAnalysisHeader';
import NewGameAnalysisGraph from '../NewGameAnalysis/NewGameAnalysisGraph/NewGameAnalysisGraph';
import {
  ABILITY_GAME_ANALYSIS_FIELDS,
  GAME_ANALYSIS_FIELDS,
} from '../../constants/gameAnalysisFields';
import { GAME_STATUS } from '../../constants/game';
import useHandleGameEventTrigger from '../../hooks/useHandleGameEventTrigger';

const GameResult = (props) => {
  const {
    minifiedQuestions,
    players,
    gameId,
    adaptedPlayers,
    player1,
    player2,
    rematchWithSamePlayer,
    navigateToNewGame,
    gameType,
    reFetchGame,
  } = props;
  const { game } = useGameContext();
  const { userId: currentUserId } = useSession();

  const shouldShowNotificationRequestModal = useMemo(() => {
    const leaderBoard = gameReader.leaderBoard(game);
    const leaderBoardEntry = gameLeaderboardReader.findEntryForPlayer(
      leaderBoard,
      currentUserId,
    );
    const isGameEnded = gameReader.gameStatus(game) === GAME_STATUS.ENDED;
    return gameLeaderboardReader.isWinner(leaderBoardEntry) && isGameEnded;
  }, [currentUserId, game]);

  useHandleGameEventTrigger(shouldShowNotificationRequestModal, gameId);

  const styles = useGameResultStyles();
  const { isMobile: isCompactMode } = useMediaQuery();

  const opponentId = useMemo(() => {
    if (!_isArray(players) || _isEmpty(players)) return '';

    const opponentIndex = _findIndex(
      players,
      (player) => _isObject(player) && _get(player, '_id') !== currentUserId,
    );

    return opponentIndex >= 0 ? _get(players, [opponentIndex, '_id'], '') : '';
  }, [players, currentUserId]);

  const gameStats = useGetGameStats({ minifiedQuestions });

  const router = useRouter();

  const navigateToDetailedAnalysis = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.GAME.CLICKED_ON_GAME_DETAILED_ANALYSIS);
    router.push(`/game/${gameId}/analysis`);
  }, [gameId, router]);

  const renderTrailingComponent = useCallback(
    () => (
      <TouchableOpacity
        onPress={() => {
          navigateToDetailedAnalysis();
        }}
      >
        <View style={styles.trailingComponent}>
          <Text style={styles.newGameText}>Detailed Game Analysis</Text>
          <Icon
            name="arrowright"
            size={16}
            color={dark.colors.secondary}
            type={ICON_TYPES.ANT_DESIGN}
          />
        </View>
      </TouchableOpacity>
    ),
    [navigateToDetailedAnalysis, styles.newGameText, styles.trailingComponent],
  );

  const isLoading = useMemo(() => {
    if (
      _isEmpty(gameId) ||
      !_isArray(players) ||
      _isEmpty(players) ||
      _isEmpty(minifiedQuestions) ||
      _isEmpty(adaptedPlayers) ||
      _isNil(player1) ||
      _isNil(player2)
    ) {
      return true;
    }
    return false;
  }, [gameId, players, minifiedQuestions, adaptedPlayers, player1, player2]);

  const runOnceRef = useRef(false);
  const timeOutRef = useRef(null);
  const refetchGameRef = useRef(reFetchGame);
  refetchGameRef.current = reFetchGame;

  useEffect(() => {
    if (!isLoading || runOnceRef.current) return;
    runOnceRef.current = true;
    timeOutRef.current = setTimeout(() => {
      refetchGameRef.current?.();
    }, 6000);

    return () => {
      clearTimeout(timeOutRef.current);
      runOnceRef.current = false;
    };
  }, [isLoading]);

  if (isLoading) {
    return <Loading label="Loading Game Results..." />;
  }

  return (
    <View style={[styles.container]}>
      <Header renderTrailingComponent={renderTrailingComponent} />
      <View style={[styles.innerContainer]}>
        <WebBackButton
          containerStyle={{ paddingHorizontal: 8, paddingVertical: 0 }}
          renderTrailingComponent={renderTrailingComponent}
        />
        <ScrollView
          contentContainerStyle={{
            gap: 15,
            paddingHorizontal: isCompactMode ? 0 : 10,
          }}
          showsVerticalScrollIndicator={false}
        >
          <NewGameAnalysisHeader adaptedPlayers={adaptedPlayers} />
          <NewGameAnalysisGraph
            gameId={gameId}
            opponentSubmissionTimes={gameStats?.submissionTimes[opponentId]}
            userSubmissionTimes={gameStats?.submissionTimes[currentUserId]}
            questions={gameStats?.questions}
          />
          <View style={styles.analysisContainer}>
            {_map(
              gameType === GAME_TYPES.ABILITY_DUELS
                ? ABILITY_GAME_ANALYSIS_FIELDS
                : GAME_ANALYSIS_FIELDS,
              (field, index) => (
                <GameStatsRow
                  fieldName={field.fieldName}
                  key={`${field.key}-${index}`}
                  userScore={_get(gameStats, [field.key, currentUserId])}
                  opponentScore={_get(gameStats, [field.key, opponentId])}
                />
              ),
            )}
          </View>
        </ScrollView>
        <GameResultFooter
          rematchWithSamePlayer={rematchWithSamePlayer}
          navigateToNewGame={navigateToNewGame}
        />
      </View>
    </View>
  );
};

GameResult.propTypes = {
  minifiedQuestions: PropTypes.array,
  players: PropTypes.array,
  gameId: PropTypes.string,
  isCurrPlayerWinner: PropTypes.bool,
  adaptedPlayers: PropTypes.array,
  isFlashAnzan: PropTypes.bool,
  timeLimit: PropTypes.number,
  player1: PropTypes.object,
  player2: PropTypes.object,
  currentPlayer: PropTypes.object,
  currentPlayerOriginalRating: PropTypes.number,
  currentPlayerOriginalStatikCoins: PropTypes.number,
  gameType: PropTypes.string,
};

const GameResultContainer = () => {
  const gameLeaderboardData = useHandleGameLeaderboard();
  const { game, players, reFetchGame } = useGameContext();
  const overlayShown = useRef(false);
  const { minifiedQuestions, gameType } = game ?? EMPTY_OBJECT;
  const styles = useGameResultStyles();
  const gameId = gameReader.id(game);

  const {
    isFlashAnzan,
    currentPlayer,
    player1,
    player2,
    adaptedPlayers,
    currentPlayerOriginalRating,
    currentPlayerOriginalStatikCoins,
    currentPlayerStatus,
    isCurrPlayerWinner,
    isRequesting,
    timeLimit,
    rematchWithSamePlayer,
    navigateToNewGame,
  } = gameLeaderboardData;

  const shouldShowPopover =
    !_isEmpty(game) && !_isEmpty(minifiedQuestions) && !overlayShown.current;

  useEffect(() => {
    if (shouldShowPopover) {
      if (!shouldShowPopover) return;

      showPopover({
        content: <GameResultOverlay gameId={gameId} />,
        overlayLook: true,
        style: styles.overlayStyle,
      });
      overlayShown.current = true;
    }
  }, [
    shouldShowPopover,
    game,
    players,
    player1,
    player2,
    currentPlayer,
    adaptedPlayers,
    currentPlayerOriginalRating,
    currentPlayerOriginalStatikCoins,
    rematchWithSamePlayer,
    navigateToNewGame,
    styles.overlayStyle,
    gameId,
  ]);

  if (
    _isEmpty(gameLeaderboardData?.adaptedPlayers) ||
    _isEmpty(gameLeaderboardData) ||
    _isEmpty(gameLeaderboardData.adaptedPlayers) ||
    _isEmpty(players)
  ) {
    return <Loading label="Loading Game Results..." />;
  }

  return (
    <GameResult
      minifiedQuestions={minifiedQuestions}
      players={players}
      gameId={gameId}
      adaptedPlayers={adaptedPlayers}
      isCurrPlayerWinner={isCurrPlayerWinner}
      isFlashAnzan={isFlashAnzan}
      timeLimit={timeLimit}
      player1={player1}
      reFetchGame={reFetchGame}
      player2={player2}
      currentPlayer={currentPlayer}
      currentPlayerOriginalRating={currentPlayerOriginalRating}
      currentPlayerOriginalStatikCoins={currentPlayerOriginalStatikCoins}
      currentPlayerStatus={currentPlayerStatus}
      isRequesting={isRequesting}
      rematchWithSamePlayer={rematchWithSamePlayer}
      navigateToNewGame={navigateToNewGame}
      gameType={gameType}
    />
  );
};

export default React.memo(GameResultContainer);
