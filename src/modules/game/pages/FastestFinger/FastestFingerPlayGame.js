import _isEmpty from 'lodash/isEmpty';

import React, { useCallback, useEffect, useRef } from 'react';
import { KeyboardAvoidingView, Platform, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Loading from 'atoms/Loading';
import useGameWaitingTimer from 'shared/game/hooks/useGameWaitingTimer';
import fastestFingerSq from '@/assets/audio/fastest_finger_swap_question.wav';
import { GAME_TYPES } from 'core/constants/gameTypes';
import useSound from '@/src/core/hooks/useSound';
import { QUESTION_CATEGORIES } from 'core/constants/questionCategories';
import QuestionsRenderer from 'shared/QuestionsRenderer';
import Header from '../PlayGame/Header';
import Footer from '../PlayGame/Footer';
import styles from '../PlayGame/PlayGame.style';
import useFastestFingerGameQuestionState from '../../hooks/useFastestFingerGameQuestionState';

const FastestFingerPlayGame = ({ game }) => {
  const { config, gameType } = game;

  const { isMobile } = useMediaQuery();
  const initialQuestionRef = useRef(false);
  const { playSound } = useSound({ soundFile: fastestFingerSq });

  const { isReady, renderQuestionOverlay } = useGameWaitingTimer({ game });

  const { currentQuestion, playersScores, submitAnswer } =
    useFastestFingerGameQuestionState();

  useEffect(() => {
    if (currentQuestion?.id && isReady) {
      if (!initialQuestionRef.current) {
        initialQuestionRef.current = true;
        return;
      }
      playSound?.();
    }
  }, [currentQuestion?.id, isReady, playSound]);

  const renderFooter = useCallback(
    () => (
      <View style={[styles.footerContainer]}>
        <Footer
          gameType={GAME_TYPES.FASTEST_FINGER}
          question={currentQuestion}
          submitAnswer={submitAnswer}
          isGameActive={isReady}
        />
      </View>
    ),
    [currentQuestion, submitAnswer, isReady],
  );

  const gameTypeRef = useRef(gameType);
  gameTypeRef.current = gameType;
  const configRef = useRef(config);
  configRef.current = config;

  if (_isEmpty(currentQuestion)) {
    return <Loading />;
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 48 : 24}
    >
      <View
        style={[styles.mainContainer, !isMobile && styles.mainContainerWeb]}
      >
        <View style={[styles.container, !isMobile && styles.webContainer]}>
          <View style={isMobile && styles.mobileHeader}>
            <Header playersScores={playersScores} />
          </View>

          <View style={[styles.question]}>
            <QuestionsRenderer
              question={currentQuestion}
              renderQuestionOverlay={renderQuestionOverlay}
              category={QUESTION_CATEGORIES.DMAS}
            />
          </View>

          {!isMobile && renderFooter()}
        </View>
        {isMobile && renderFooter()}
      </View>
    </KeyboardAvoidingView>
  );
};

export default React.memo(FastestFingerPlayGame);
