import React, { useCallback, useMemo } from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import Header from 'shared/Header';
import _map from 'lodash/map';
import _findIndex from 'lodash/findIndex';
import PropTypes from 'prop-types';
import useMediaQuery from 'core/hooks/useMediaQuery';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import dark from 'core/constants/themes/dark';
import Loading from '@/src/components/atoms/Loading';
import _isNil from 'lodash/isNil';
import _get from 'lodash/get';
import { useSession } from '../../../auth/containers/AuthProvider';
import useNewGameAnalysisStyles from './NewGameAnalysis.style';
import useGetGameStats from '../../hooks/useGetGameStats';
import GameStatsRow from '../GameAnalysis/components/GameStatsRow';
import {ABILITY_GAME_ANALYSIS_FIELDS, GAME_ANALYSIS_FIELDS} from '../../constants/gameAnalysisFields';
import useGameContext from '../../hooks/useGameContext';
import WebBackButton from '../../../../components/shared/WebBackButton';
import NewGameAnalysisHeader from './NewGameAnalysisHeader';
import NewGameAnalysisGraph from './NewGameAnalysisGraph/NewGameAnalysisGraph';
import useHandleGameLeaderboard from '../../hooks/useHandleGameLeaderboard';
import { GAME_TYPES } from '@/src/core/constants/gameTypes';

const NewGameAnalysis = (props) => {
  const {
    minifiedQuestions,
    players,
    gameId,
    adaptedPlayers,
    navigateToNewGame,
    gameType
  } = props;
  const { userId: currentUserId } = useSession();
  const styles = useNewGameAnalysisStyles();
  const { isMobile: isCompactMode } = useMediaQuery();

  const opponentId = useMemo(() => {
    const index = _findIndex(players, (val) => val?._id != currentUserId);
    return index > 0 ? players[index]?._id : '';
  }, [players, currentUserId]);

  const gameStats = useGetGameStats({ minifiedQuestions });

  const renderTrailingComponent = useCallback(
    () => (
      <TouchableOpacity
        onPress={() => {
          navigateToNewGame();
        }}
      >
        <View style={styles.trailingComponent}>
          <MaterialIcons name="add" size={16} color={dark.colors.secondary} />
          <Text style={styles.newGameText}>New Game</Text>
        </View>
      </TouchableOpacity>
    ),
    [navigateToNewGame, styles.newGameText, styles.trailingComponent],
  );

  return (
    <View style={[styles.container]}>
      <Header renderTrailingComponent={renderTrailingComponent} />
      <View
        style={[
          styles.innerContainer,
          { width: isCompactMode ? '100%' : '40%' },
        ]}
      >
        <WebBackButton containerStyle={{ paddingVertical: 0 }} />
        <ScrollView
          contentContainerStyle={{
            gap: 15,
            paddingHorizontal: isCompactMode ? 0 : 10,
          }}
          showsVerticalScrollIndicator={false}
        >
          <NewGameAnalysisHeader adaptedPlayers={adaptedPlayers} />
          <NewGameAnalysisGraph
            gameId={gameId}
            opponentSubmissionTimes={gameStats?.submissionTimes[opponentId]}
            userSubmissionTimes={gameStats?.submissionTimes[currentUserId]}
            questions={gameStats?.questions}
          />

          {_map(gameType === GAME_TYPES.ABILITY_DUELS
            ? ABILITY_GAME_ANALYSIS_FIELDS
            : GAME_ANALYSIS_FIELDS, (field, index) => (
            <GameStatsRow
              fieldName={field.fieldName}
              key={`${field.key}-${index}`}
              userScore={_get(gameStats, [field.key, currentUserId])}
              opponentScore={_get(gameStats, [field.key, opponentId])}
            />
          ))}
        </ScrollView>
      </View>
    </View>
  );
};

NewGameAnalysis.propTypes = {
  minifiedQuestions: PropTypes.array,
  players: PropTypes.array,
  gameId: PropTypes.string,
  adaptedPlayers: PropTypes.array,
  navigateToNewGame: PropTypes.func,
};

const NewGameAnalysisContainer = () => {
  const { game, players } = useGameContext();

  if (_isNil(game)) {
    return <Loading label="Loading Game Analysis" />;
  }

  const { minifiedQuestions, gameType } = game ?? EMPTY_OBJECT;
  const gameLeaderboardData = useHandleGameLeaderboard();
  const { adaptedPlayers, navigateToNewGame } = gameLeaderboardData;

  return (
    <NewGameAnalysis
      minifiedQuestions={minifiedQuestions}
      players={players}
      gameId={game._id}
      adaptedPlayers={adaptedPlayers}
      navigateToNewGame={navigateToNewGame}
      gameType={gameType}
    />
  );
};

NewGameAnalysisContainer.propTypes = {};
export default React.memo(NewGameAnalysisContainer);
