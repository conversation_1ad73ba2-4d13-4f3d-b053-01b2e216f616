import PropTypes from 'prop-types';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Animated, View } from 'react-native';
import { Button, Text } from '@rneui/themed';
import DarkTheme from '@/src/core/constants/themes/dark';
import { getSiteUrl } from 'core/constants/appConstants';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import useNativeUrlSharing from 'core/hooks/useNativeUrlSharing';
import DotAnimation from 'shared/DotAnimation';
import gameReader from 'core/readers/gameReader';
import { GAME_MODES } from 'modules/game/constants/game';
import GroupPlayWaitingPage from 'modules/game/pages/GroupPlayWaitingPage';
import WaitingForUserToJoin from 'modules/game/pages/ChallengeUser/Components/WaitingForUserToJoin';
import { GAME_TYPES } from 'core/constants/gameTypes';
import useHandleLeaveGame from '../../../../hooks/useHandleLeaveGame';
import styles from './WaitingForFriend.styles';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from '@/src/components/atoms/Icon';
import useGameContext from 'modules/game/hooks/useGameContext';
import dark from '@/src/core/constants/themes/dark';

const TRACKED_EVENT_FOR_GAMES = {};

const WaitingForFriend = (props) => {
  const { game } = props ?? EMPTY_OBJECT;
  const { _id: gameId } = game;
  const { reFetchGame } = useGameContext();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const siteUrl = useMemo(() => getSiteUrl(), []);

  const rotateValue = useRef(new Animated.Value(0)).current;

  const GAME_URL = `${siteUrl}/game/${gameId}/play`;

  const { handleShare } = useNativeUrlSharing({ url: GAME_URL });

  const onPressShareLink = useCallback(async () => {
    Analytics.track(ANALYTICS_EVENTS.PLAY_WITH_FRIEND?.CLICKED_ON_COPY_LINK, {
      [PAGE_NAME_KEY]: PAGE_NAMES.PLAY_WITH_FRIEND_WAITING_PAGE,
    });
    await handleShare();
  }, [handleShare]);

  const refreshGame = useCallback(async () => {
    if(isRefreshing) return;
    setIsRefreshing(true);
    await reFetchGame();
    setIsRefreshing(false);
  }, [reFetchGame, isRefreshing]);

  useEffect(() => {
    const rotateAnimation = Animated.loop(
      Animated.timing(rotateValue, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      }),
      {
        iterations: 1,
      },
    );
    rotateAnimation.start();

    return () => rotateAnimation.stop();
  }, []);

  useEffect(() => {
    if (gameId && !TRACKED_EVENT_FOR_GAMES[gameId]) {
      Analytics.track(ANALYTICS_EVENTS.PLAY_WITH_FRIEND?.VIEW_PWF_WAITING_PAGE);
      TRACKED_EVENT_FOR_GAMES[gameId] = true;
    }
  }, [gameId]);

  const rotate = rotateValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const { onPressLeaveGame, isCancellingGame } = useHandleLeaveGame({ gameId });
  const gameMode = gameReader.gameMode(game);

  const gameType = gameReader.gameType(game);

  if (
    gameMode === GAME_MODES.GROUP_PLAY ||
    gameType === GAME_TYPES.GROUP_PLAY
  ) {
    return <GroupPlayWaitingPage game={game} />;
  }

  if (
    gameMode === GAME_MODES.ONLINE_CHALLENGE ||
    gameType === GAME_TYPES.ONLINE_CHALLENGE
  ) {
    return <WaitingForUserToJoin game={game} />;
  }

  return (
    <View style={styles.container}>
      <View style={styles.container}>
        <Animated.Text style={[styles.timer, { transform: [{ rotate }] }]}>
          ⏳
        </Animated.Text>
        <Text style={styles.waitingText}>
          Waiting for your Friend to join
          <DotAnimation />
        </Text>
        <Text style={styles.shareLinkLabel}>
          Share the link with your friend.
        </Text>
        <Button
          title={GAME_URL}
          onPress={onPressShareLink}
          titleProps={{ numberOfLines: 1 }}
          icon={{
            name: 'copy',
            type: 'font-awesome-5',
            size: 16,
            color: DarkTheme.colors.secondary,
          }}
          iconRight
          iconContainerStyle={{ marginLeft: 10 }}
          titleStyle={styles.linkText}
          buttonStyle={styles.linkButton}
          containerStyle={styles.linkButtonContainer}
        />
      </View>
      <InteractiveSecondaryButton
        label={isRefreshing ? "Refreshing..." :"Refresh"}
        iconConfig={{
          name: 'refresh-cw',
          type: ICON_TYPES.FEATHER,
          size: 16,
          color: dark.colors.textLight,
        }}
        labelStyle={styles.labelStyle}
        buttonStyle={styles.refreshButton}
        buttonBackgroundStyle={styles.refreshButtonBackground}
        buttonContainerStyle={styles.refreshContainer}
        onPress={refreshGame}
      />
      <View style={{ marginBottom: 16 }}>
        <Button type="clear" onPress={onPressLeaveGame}>
          <Text style={styles.cancel}>Cancel</Text>
        </Button>
      </View>
    </View>
  );
};

WaitingForFriend.propTypes = {
  game: PropTypes.object.isRequired,
};

export default React.memo(WaitingForFriend);
