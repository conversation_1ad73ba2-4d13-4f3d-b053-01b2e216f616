import PropTypes from "prop-types"
import React, {useCallback} from "react"
import {Text, TouchableOpacity, View} from "react-native"
import UserImage from "atoms/UserImage"
import userReader from "core/readers/userReader"
import {useRouter} from "expo-router"
import numeral from "numeral"
import useMediaQuery from "core/hooks/useMediaQuery"
import useGroupPlayLeaderboardRowStyles from "./GroupPlayLeaderboardRow.style"

const NUMERAL_FORMAT = '0[.]00a'

const GroupPlayLeaderboardRow = (props) => {
  const router = useRouter()
  const {score, user, rank, containerStyle, currentQuesScore} = props

  const styles = useGroupPlayLeaderboardRowStyles()

  const {isMobile: isCompactMode} = useMediaQuery(0)

  const navigateToUserProfile = useCallback(() => {
    router.push(`/profile/${userReader?.username(user)}`)
  }, [router, user])

  return (
    <TouchableOpacity style={[styles.container, containerStyle]} onPress={navigateToUserProfile}>
      <Text style={[styles.rankText, !isCompactMode && {flex: 0.2}]}>
        {numeral(rank).format(NUMERAL_FORMAT)}
      </Text>
      <View style={styles.userInfo}>
        <UserImage user={user} style={styles.userImage} rounded={false}/>
        <View style={styles.userDetailsTextContainer}>
          <Text style={styles.userNameText} numberOfLines={1}>
            {userReader.username(user)}
          </Text>
          <Text style={styles.ratingText}>
            {user?.rating}
          </Text>
        </View>
      </View>

      <View style={styles.statikCoinsDetail}>
        <Text style={styles.statikCoinsText}>
          {numeral(score).format(NUMERAL_FORMAT)}
        </Text>
        {currentQuesScore ? (
          <Text style={styles.currentQuesScoreText}>
            {`+${currentQuesScore}`}
          </Text>
        ) : <View style={styles.emptyCurrentQuesScoreText}/>}
      </View>
    </TouchableOpacity>
  )
}

GroupPlayLeaderboardRow.propTypes = {
  score: PropTypes.number,
  user: PropTypes.object,
  rank: PropTypes.number,
  currentQuesScore: PropTypes.oneOf([PropTypes.number, undefined]),
  containerStyle: PropTypes.object
}

export default React.memo(GroupPlayLeaderboardRow)