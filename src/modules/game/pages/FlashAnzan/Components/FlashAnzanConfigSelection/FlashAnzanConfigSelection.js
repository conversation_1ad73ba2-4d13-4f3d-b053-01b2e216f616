import React, { useMemo } from 'react';
import { Text, View } from 'react-native';
import NewNumberInput from 'molecules/NewNumberInput';
import ToggleSwitch from 'molecules/ToggleSwitch';
import Icon from 'atoms/Icon';
import dark from 'core/constants/themes/dark';
import TextWithShadow from 'shared/TextWithShadow';
import useGameContext from 'modules/game/hooks/useGameContext';
import { useSession } from 'modules/auth/containers/AuthProvider';
import _find from 'lodash/find';
import userReader from 'core/readers/userReader';
import _keyBy from 'lodash/keyBy';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _reduce from 'lodash/reduce';
import UserImage from 'atoms/UserImage';
import styles from './FlashAnzanConfigSelection.style';
import getMaxScoreOfFlashAnzanQueFromConfig from '../../../../utils/getMaxScoreOfFlashAnzanQueByConfig';

const getPlayerScores = ({ game }) => {
  const { leaderBoard, players } = game;
  const leaderBoardByUserId = _keyBy(leaderBoard, (entry) =>
    _get(entry, 'userId'),
  );
  const scores = _map(players, (player) => ({
    userId: player?.userId,
    score: _get(leaderBoardByUserId[player?.userId], 'totalPoints', 0),
  }));
  return _reduce(
    scores,
    (acc, { userId, score }) => {
      acc[userId] = score;
      return acc;
    },
    {},
  );
};

const FlashAnzanConfigSelection = ({
  selectedConfig,
  updateConfig,
  currentQuestion,
  phaseCountdown,
}) => {
  const { userId } = useSession();

  const { players, game } = useGameContext();

  const playersScores = useMemo(() => getPlayerScores({ game }), [game]);

  const {
    digits,
    flashSpeed,
    includeSubstraction,
    numberCount,
    noOfQuestions,
  } = selectedConfig ?? EMPTY_OBJECT;

  const maxSore = useMemo(
    () => getMaxScoreOfFlashAnzanQueFromConfig({ config: selectedConfig }),
    [selectedConfig],
  );

  const renderHeader = () => {
    const me = _find(players, (p) => userReader.id(p) === userId);
    const opponent = _find(players, (p) => userReader.id(p) !== userId);
    const myScore = playersScores?.[userReader.id(me)] || 0;
    const opponentScore = playersScores?.[userReader.id(opponent)] || 0;

    let myScoreShadowColor = dark.colors.tertiary;
    if (myScore > opponentScore) {
      myScoreShadowColor = dark.colors.victoryColor;
    } else if (myScore < opponentScore) {
      myScoreShadowColor = dark.colors.defeatColor;
    }

    return (
      <View style={styles.header}>
        <View style={styles.playerInfo}>
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'row',
            }}
          >
            <TextWithShadow
              text={myScore}
              textStyle={styles.score}
              shadowColor={myScoreShadowColor}
            />
          </View>
          <View style={styles.playerNameAndRatingContainer}>
            <View style={styles.playerNameContainer}>
              <UserImage user={me} size={12} />
              <Text style={styles.playerName} numberOfLines={1}>
                {userReader.username(me)}
              </Text>
            </View>
            <Text style={styles.userRatingText}>
              ({userReader.flashAnzanRating(me)})
            </Text>
          </View>
        </View>
        <Text style={styles.questionCount}>
          {currentQuestion}/{noOfQuestions}
        </Text>
        <View style={styles.playerInfo}>
          <TextWithShadow text={opponentScore} textStyle={styles.score} />
          <View style={styles.playerNameAndRatingContainer}>
            <View style={styles.playerNameContainer}>
              <UserImage user={opponent} size={12} />
              <Text style={styles.playerName} numberOfLines={1}>
                {userReader.username(opponent)}
              </Text>
            </View>
            <Text style={styles.userRatingText}>
              ({userReader.flashAnzanRating(opponent)})
            </Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.configContainer}>
      {renderHeader()}
      <Text style={styles.countdownText}>
        PREP PHASE ENDS IN {phaseCountdown}
      </Text>
      <View style={styles.conversionTopContainer}>
        <Text style={styles.conversionNameText}>CONVERSION</Text>
        <View style={styles.conversionContainer}>
          <View style={styles.conversionItem}>
            <Text style={styles.conversionText}>{numberCount} NUMBERS</Text>
          </View>
          <Icon name="arrow-right" size={15} color={dark.colors.secondary} />
          <View style={styles.conversionItem}>
            <Text style={styles.conversionText}>{maxSore} POINTS</Text>
          </View>
        </View>
      </View>
      <View style={{ paddingHorizontal: 16, gap: 32 }}>
        <NewNumberInput
          label="Number of digits"
          value={digits}
          onValueChange={(value) => updateConfig?.('digits', value)}
          minValue={1}
          maxValue={5}
          valueSuffix=" DIGIT"
        />
        <NewNumberInput
          label="Time Interval"
          value={flashSpeed}
          minValue={200}
          onValueChange={(value) => updateConfig?.('flashSpeed', value)}
          maxValue={5000}
          incrementBy={100}
          valueSuffix="MS"
        />
        <ToggleSwitch
          label="Include Subtraction"
          value={includeSubstraction}
          onValueChange={(value) =>
            updateConfig?.('includeSubstraction', value)
          }
        />
      </View>
    </View>
  );
};

export default React.memo(FlashAnzanConfigSelection);
