import React, { useCallback, useEffect, useRef } from 'react';
import { View } from 'react-native';
import { Text } from '@rneui/themed';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import _toNumber from 'lodash/toNumber';
import Header from 'shared/Header';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import gameReader from 'core/readers/gameReader';
import useGameContext from '../../hooks/useGameContext';
import styles from './PracticeGameResult.style';
import Analytics from '../../../../core/analytics';
import { ANALYTICS_EVENTS } from '../../../../core/analytics/const';
import useGoBack from '../../../../navigator/hooks/useGoBack';

const PracticeGameResult = ({ playerScore }) => {
  const { game } = useGameContext();

  const { goBackToHome } = useGoBack();

  const gameConfigRef = useRef();

  const timeLimit = gameReader.timeLimit(game);

  const navigateToHome = useCallback(() => {
    goBackToHome();
  }, [goBackToHome]);

  useEffect(() => {
    const { gameType, config } = game ?? EMPTY_OBJECT;
    Analytics.track(ANALYTICS_EVENTS.RESULT_PAGE_SHOWN, {
      gameType,
      ...config,
    });
  }, []);

  const { goBack } = useGoBack();

  // TODO: @mohan add personal best record in timed environment.

  return (
    <View style={{ flex: 1 }}>
      <Header title="Practice" goBack={goBack} />
      <View style={styles.container}>
        <View ref={gameConfigRef} style={styles.contentContainer}>
          <View style={styles.headerContainer}>
            <View style={styles.headerTitleContainer}>
              <Text
                style={{
                  fontSize: 18,
                  lineHeight: 18,
                  flexShrink: 1,
                  color: 'white',
                  fontFamily: 'Montserrat-600',
                  textAlign: 'center',
                }}
              >
                TIME'S UP
              </Text>
            </View>
          </View>

          <View style={styles.timeContainer}>
            <MaterialIcons name="timer" color="white" size={24} />
            <Text style={styles.gameTime}> {_toNumber(timeLimit) / 60}:00</Text>
          </View>

          <View style={styles.resultContainer}>
            <View style={styles.scoreBox}>
              <Text style={styles.scoreText}>YOUR SCORE</Text>
              <Text style={styles.score}>{playerScore}</Text>
            </View>
          </View>

          <View style={styles.actionsContainer}>
            <InteractiveSecondaryButton
              label="Go Home"
              buttonContainerStyle={{ width: '100%' }}
              onPress={navigateToHome}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

export default React.memo(PracticeGameResult);
