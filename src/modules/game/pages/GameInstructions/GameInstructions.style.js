import { StyleSheet } from "react-native";
import useMediaQuery from "core/hooks/useMediaQuery";
import { useMemo } from "react";

const createStyles = (isCompactMode) => StyleSheet.create({
    container: {

    }
})

const useGameInstructionStyles = () => {
    const { isMobile: isCompactMode } = useMediaQuery()
    const styles = useMemo(() => createStyles(isCompactMode), [isCompactMode])
    return styles
}

export default useGameInstructionStyles