import React from 'react';
import { Redirect } from 'expo-router';
import PropTypes from 'prop-types';
import _isNil from 'lodash/isNil';
import FlashAnzanInstruction from './Components/FlashAnzanInstruction';
import { GAME_TYPES } from '../../../home/<USER>/gameTypes';
import CreateGame from '../CreateGame';

const COMPONENT_FACTORY = {
  [GAME_TYPES.FLASH_ANZAN]: FlashAnzanInstruction,
  [GAME_TYPES.GROUP_PLAY]: CreateGame,
};

const GameInstructions = (props) => {
  const { gameType } = props;

  const Component = COMPONENT_FACTORY[gameType];

  if (_isNil(Component)) {
    return <Redirect href="/home" />;
  }

  return <Component />;
};

GameInstructions.propTypes = {
  gameType: PropTypes.string,
};

export default React.memo(GameInstructions);
