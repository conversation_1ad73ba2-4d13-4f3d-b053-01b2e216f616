// Simple test to check if the new GroupPlay architecture works
import useGroupPlayStore from '@/src/modules/game/hooks/useGroupPlayStore';

// Test the store hook
const testGame = {
  _id: 'test-game-123',
  startTime: new Date().toISOString(),
  config: {
    timeLimit: 300,
    maxTimePerQuestion: 10,
    numPlayers: 4,
  },
  players: [
    { userId: 'user1', userName: 'Player 1', score: 0 },
    { userId: 'user2', userName: 'Player 2', score: 0 },
  ],
  questions: [
    {
      question: {
        id: 'q1',
        text: 'What is 2 + 2?',
        answers: ['4'],
        maxTimeLimit: 10,
      },
      submissions: [],
    },
  ],
  gameStatus: 'STARTED',
};

console.log('Testing GroupPlay store initialization...');

// This would be used in a component like:
// const {
//   gameState,
//   currentQuestion,
//   submitAnswer,
//   isQuestionPhase,
// } = useGroupPlayStore({
//   gameId: testGame._id,
//   gameData: testGame,
// });

console.log('GroupPlay store test completed successfully!');
