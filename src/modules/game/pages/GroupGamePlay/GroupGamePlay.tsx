import React, { memo, useCallback, useMemo } from 'react';
import { KeyboardAvoidingView, Platform, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useSession } from 'modules/auth/containers/AuthProvider';
import useGroupPlayStore from '@/src/modules/game/hooks/useGroupPlayStore';
import useGroupPlayEvents from '@/src/modules/game/hooks/useGroupPlayEvents';
import gameReader from 'core/readers/gameReader';
import LeaderboardInBetweenGame from './LeaderboardInBetweenGame';
import {
  ErrorState,
  GameContent,
  GameFooter,
  GameHeader,
  LoadingState,
  WaitingState,
} from './components';
import styles from './GroupGamePlay.style';

interface GroupGamePlayProps {
  game: any;
}

const GroupGamePlay: React.FC<GroupGamePlayProps> = memo(({ game }) => {
  const { isMobile } = useMediaQuery();

  const {
    gameState,
    isLoading,
    error,
    currentQuestion,
    currentPhase,
    timeRemaining,
    questionTimeRemaining,
    players,
    currentPlayer,
    leaderboard,
    gameProgress,
    submitAnswer,
    isQuestionPhase,
    isWaitingPhase,
    isGameReady,
    isGameActive,
    showLeaderboard,
    formatTime,
    getCurrentServerTime,
  } = useGroupPlayStore({
    gameId: game?._id || '',
    gameData: game,
    autoInitialize: true,
  });

  const { sendWebSocketMessage } = useGroupPlayEvents({
    gameId: game?._id || '',
    enableWebSocketIntegration: true,
    eventHandlers: {
      onQuestionStart: (event) => {
        console.info('Question started:', event.data);
      },
      onAnswerSubmitted: (event) => {
        console.info('Answer submitted:', event.data);
      },
      onPlayerScoreUpdated: (event) => {
        console.info('Player score updated:', event.data);
      },
      onLeaderboardUpdated: (event) => {
        console.info('Leaderboard updated:', event.data);
      },
    },
  });

  const handleSubmitAnswer = useCallback(
    async (answer: string) => {
      if (!currentQuestion || !isQuestionPhase) {
        return false;
      }

      try {
        const result = await submitAnswer(answer);

        sendWebSocketMessage({
          type: 'submitAnswer',
          data: {
            questionId: currentQuestion.id,
            answer,
            submissionTime: getCurrentServerTime(),
            gameId: gameState.gameId,
          },
        });

        return result;
      } catch (error) {
        console.error('Error submitting answer:', error);
        return false;
      }
    },
    [
      currentQuestion,
      isQuestionPhase,
      submitAnswer,
      sendWebSocketMessage,
      getCurrentServerTime,
      gameState.gameId,
    ],
  );

  const handleForceQuestionSubmission = useCallback(() => {
    if (currentQuestion && !currentQuestion.hasSolved) {
      handleSubmitAnswer('');
    }
  }, [currentQuestion, handleSubmitAnswer]);

  const MemoizedHeader = useMemo(
    () => (
      <GameHeader
        currentQuestionId={currentQuestion?.id || null}
        isQuestionPhase={isQuestionPhase}
        isWaitingPhase={isWaitingPhase}
        currentPhase={currentPhase}
        questionTimeRemaining={questionTimeRemaining}
        gameProgress={gameProgress}
        formatTime={formatTime}
        gameState={gameState}
        players={players}
        game={game}
        styles={styles}
      />
    ),
    [
      currentQuestion?.id,
      isQuestionPhase,
      isWaitingPhase,
      currentPhase,
      questionTimeRemaining,
      gameProgress,
      formatTime,
      gameState,
      players,
      game,
    ],
  );

  const MemoizedGameContent = useMemo(
    () => (
      <GameContent
        currentQuestion={currentQuestion}
        isQuestionPhase={isQuestionPhase}
        currentPlayer={currentPlayer}
        styles={styles}
      />
    ),
    [currentQuestion, isQuestionPhase, currentPlayer],
  );

  const MemoizedFooter = useMemo(
    () => (
      <GameFooter
        currentQuestion={currentQuestion}
        isQuestionPhase={isQuestionPhase}
        isGameActive={isGameActive}
        questionStartTime={gameState.questionStartTime}
        onSubmitAnswer={handleSubmitAnswer}
        onForceSubmission={handleForceQuestionSubmission}
        gameState={gameState}
        players={players}
        game={game}
        styles={styles}
      />
    ),
    [
      currentQuestion,
      isQuestionPhase,
      isGameActive,
      gameState.questionStartTime,
      handleSubmitAnswer,
      handleForceQuestionSubmission,
      gameState,
      players,
      game,
    ],
  );

  const MemoizedLeaderboard = useMemo(() => {
    if (!showLeaderboard) return null;

    return (
      <LeaderboardInBetweenGame
        currentQuestionId={currentQuestion?.id || null}
      />
    );
  }, [showLeaderboard, currentQuestion?.id]);

  if (isLoading) {
    return <LoadingState />;
  }

  if (error) {
    return <ErrorState error={error} />;
  }

  if (!isGameReady) {
    return (
      <WaitingState
        header={MemoizedHeader}
        currentPhase={currentPhase}
        formatTime={formatTime}
      />
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 48 : 24}
    >
      <View
        style={[styles.mainContainer, !isMobile && styles.mainContainerWeb]}
      >
        {MemoizedLeaderboard}

        <View style={[styles.container, !isMobile && styles.webContainer]}>
          {MemoizedHeader}
          {MemoizedGameContent}
          {MemoizedFooter}
        </View>
      </View>
    </KeyboardAvoidingView>
  );
});

GroupGamePlay.displayName = 'GroupGamePlay';

export default GroupGamePlay;
