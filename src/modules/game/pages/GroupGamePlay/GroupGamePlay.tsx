import React, { useCallback, useEffect, useMemo, memo } from 'react';
import { KeyboardAvoidingView, Platform, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useSession } from '@/src/core/contexts/SessionContext';
import useGroupPlayStore from '@/src/modules/game/hooks/useGroupPlayStore';
import useGroupPlayEvents from '@/src/modules/game/hooks/useGroupPlayEvents';
import Header from './Header';
import Footer from './Footer';
import Question from './Question';
import LeaderboardInBetweenGame from './LeaderboardInBetweenGame';
import PointGainedInCurrRoundInfo from './PointGainedInCurrRoundInfo';
import WaitingPhaseTimer from './WaitingPhaseTimer';
import GamePhaseIndicator from './components/GamePhaseIndicator';
import LoadingState from './components/LoadingState';
import ErrorState from './components/ErrorState';
import WaitingState from './components/WaitingState';
import styles from './GroupGamePlay.style';

const EMPTY_OBJECT = {};

interface GroupGamePlayProps {
  game: any;
}

const GroupGamePlay: React.FC<GroupGamePlayProps> = memo(({ game }) => {
  const { userId } = useSession();
  const { isMobile } = useMediaQuery();

  // Initialize the store with game data
  const {
    gameState,
    isLoading,
    error,
    currentQuestion,
    currentPhase,
    timeRemaining,
    questionTimeRemaining,
    players,
    currentPlayer,
    leaderboard,
    gameProgress,
    submitAnswer,
    isQuestionPhase,
    isWaitingPhase,
    isGameReady,
    isGameActive,
    showLeaderboard,
    formatTime,
    getCurrentServerTime,
  } = useGroupPlayStore({
    gameId: game?._id || '',
    gameData: game,
    autoInitialize: true,
  });

  // Set up event handling
  const {
    emitEvent,
    sendWebSocketMessage,
    isWebSocketConnected,
    getEventStats,
  } = useGroupPlayEvents({
    gameId: game?._id || '',
    enableWebSocketIntegration: true,
    eventHandlers: {
      onQuestionStart: (event) => {
        console.log('Question started:', event.data);
      },
      onAnswerSubmitted: (event) => {
        console.log('Answer submitted:', event.data);
      },
      onPlayerScoreUpdated: (event) => {
        console.log('Player score updated:', event.data);
      },
      onLeaderboardUpdated: (event) => {
        console.log('Leaderboard updated:', event.data);
      },
    },
  });

  // Handle answer submission with WebSocket integration
  const handleSubmitAnswer = useCallback(async (answer: string) => {
    if (!currentQuestion || !isQuestionPhase) {
      return false;
    }

    try {
      // Submit locally first (optimistic update)
      const result = await submitAnswer(answer);
      
      // Send to server via WebSocket
      sendWebSocketMessage({
        type: 'submitAnswer',
        data: {
          questionId: currentQuestion.id,
          answer,
          submissionTime: getCurrentServerTime(),
          gameId: gameState.gameId,
        },
      });

      return result;
    } catch (error) {
      console.error('Error submitting answer:', error);
      return false;
    }
  }, [
    currentQuestion,
    isQuestionPhase,
    submitAnswer,
    sendWebSocketMessage,
    getCurrentServerTime,
    gameState.gameId,
  ]);

  // Handle force question submission (timeout)
  const handleForceQuestionSubmission = useCallback(() => {
    if (currentQuestion && !currentQuestion.hasSolved) {
      // Submit empty answer on timeout
      handleSubmitAnswer('');
    }
  }, [currentQuestion, handleSubmitAnswer]);

  // Memoized components for performance
  const MemoizedHeader = useMemo(() => (
    <Header 
      currentQuestionId={currentQuestion?.id || null}
      gameProgress={gameProgress}
      timeRemaining={timeRemaining}
      formatTime={formatTime}
      players={players}
      isWebSocketConnected={isWebSocketConnected}
    />
  ), [
    currentQuestion?.id,
    gameProgress,
    timeRemaining,
    formatTime,
    players,
    isWebSocketConnected,
  ]);

  const MemoizedQuestion = useMemo(() => {
    if (!currentQuestion || !isQuestionPhase) return null;
    
    return (
      <Question
        question={currentQuestion}
        timeRemaining={questionTimeRemaining}
        formatTime={formatTime}
        isActive={isQuestionPhase && isGameActive}
        onSubmitAnswer={handleSubmitAnswer}
      />
    );
  }, [
    currentQuestion,
    isQuestionPhase,
    questionTimeRemaining,
    formatTime,
    isGameActive,
    handleSubmitAnswer,
  ]);

  const MemoizedFooter = useMemo(() => {
    if (!currentQuestion || !isQuestionPhase) return null;
    
    return (
      <Footer
        question={currentQuestion}
        submitAnswer={handleSubmitAnswer}
        isGameActive={isGameActive && isQuestionPhase}
        startTime={gameState.questionStartTime}
        handleForceQuestionSubmission={handleForceQuestionSubmission}
        timeRemaining={questionTimeRemaining}
        formatTime={formatTime}
      />
    );
  }, [
    currentQuestion,
    isQuestionPhase,
    handleSubmitAnswer,
    isGameActive,
    gameState.questionStartTime,
    handleForceQuestionSubmission,
    questionTimeRemaining,
    formatTime,
  ]);

  const MemoizedWaitingTimer = useMemo(() => {
    if (!isWaitingPhase) return null;
    
    return (
      <WaitingPhaseTimer
        phase={currentPhase}
        formatTime={formatTime}
        onPhaseEnd={() => {
          // Phase transition is handled by the store
        }}
      />
    );
  }, [isWaitingPhase, currentPhase, formatTime]);

  const MemoizedLeaderboard = useMemo(() => {
    if (!showLeaderboard) return null;
    
    return (
      <LeaderboardInBetweenGame
        currentQuestionId={currentQuestion?.id || null}
        leaderboard={leaderboard}
        currentUserId={userId}
        gameProgress={gameProgress}
      />
    );
  }, [showLeaderboard, currentQuestion?.id, leaderboard, userId, gameProgress]);

  const MemoizedPointsInfo = useMemo(() => {
    if (!currentPlayer || !currentQuestion?.hasSolved) return null;
    
    return (
      <PointGainedInCurrRoundInfo
        player={currentPlayer}
        question={currentQuestion}
      />
    );
  }, [currentPlayer, currentQuestion]);

  // Loading state
  if (isLoading) {
    return (
      <View style={[styles.mainContainer, styles.loadingContainer]}>
        {/* Add loading component here */}
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View style={[styles.mainContainer, styles.errorContainer]}>
        {/* Add error component here */}
      </View>
    );
  }

  // Game not ready state
  if (!isGameReady) {
    return (
      <View style={[styles.mainContainer, styles.waitingContainer]}>
        {MemoizedHeader}
        {MemoizedWaitingTimer}
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 48 : 24}
    >
      <View style={[styles.mainContainer, !isMobile && styles.mainContainerWeb]}>
        {MemoizedLeaderboard}
        
        <View style={[styles.container, !isMobile && styles.webContainer]}>
          <View style={styles.mobileHeader}>
            {MemoizedHeader}
            
            {/* Game Phase Indicator */}
            <View style={styles.phaseIndicator}>
              {isQuestionPhase && (
                <View style={styles.questionPhaseIndicator}>
                  {/* Question phase UI */}
                </View>
              )}
              
              {isWaitingPhase && (
                <View style={styles.waitingPhaseIndicator}>
                  {MemoizedWaitingTimer}
                </View>
              )}
            </View>
          </View>

          {/* Main Game Content */}
          <View style={styles.gameContent}>
            {MemoizedQuestion}
            {MemoizedPointsInfo}
          </View>

          {/* Footer */}
          <View style={styles.footerContainer}>
            {MemoizedFooter}
          </View>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
});

GroupGamePlay.displayName = 'GroupGamePlay';

export default GroupGamePlay;
