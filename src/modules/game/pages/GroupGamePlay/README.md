# Enhanced GroupPlay Architecture

This document describes the new enhanced GroupPlay architecture that addresses performance issues, timing inconsistencies, and provides a clean separation between business and application layers.

## Architecture Overview

### 🏗️ **Layered Architecture**

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │  GroupGamePlay  │  │     Header      │  │   Footer    │ │
│  │   Component     │  │   Component     │  │ Component   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     Hook Layer                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │useGroupPlayStore│  │useGroupPlayEvents│ │   Custom    │ │
│  │                 │  │                 │  │   Hooks     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   State Management Layer                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ Zustand Store   │  │ Event Manager   │  │ Optimistic  │ │
│  │                 │  │                 │  │  Updates    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Business Logic Layer                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │  Game Service   │  │Question Service │  │Timing Service│ │
│  │                 │  │                 │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
│  ┌─────────────────┐                                       │
│  │ Event Service   │                                       │
│  │                 │                                       │
│  └─────────────────┘                                       │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Infrastructure Layer                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   WebSocket     │  │      API        │  │   Storage   │ │
│  │   Manager       │  │    Clients      │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 **Key Improvements**

### 1. **Performance Optimizations**
- **Memoized Components**: All major components are memoized to prevent unnecessary re-renders
- **Optimistic Updates**: Immediate UI feedback with rollback capabilities
- **Preloading**: Questions are preloaded to eliminate loading delays
- **Event Batching**: Multiple events are batched to reduce processing overhead

### 2. **Timing Synchronization**
- **Server Time Sync**: Automatic synchronization with server time
- **Predictive Timing**: Client-side prediction of phase transitions
- **Drift Compensation**: Automatic correction for time drift
- **Performance Monitoring**: Latency tracking and optimization

### 3. **Event-Driven Architecture**
- **Event Emitter Pattern**: Decoupled event handling
- **Event History**: Complete audit trail of game events
- **Event Debugging**: Built-in debugging and monitoring tools
- **WebSocket Integration**: Seamless real-time communication

### 4. **State Management**
- **Zustand Store**: Centralized, immutable state management
- **Business Logic Separation**: Clear separation of concerns
- **Type Safety**: Full TypeScript support
- **State Persistence**: Automatic state recovery

## 📁 **File Structure**

```
src/
├── store/useGroupPlayStore/
│   ├── index.ts                    # Main store interface
│   ├── types.ts                    # Type definitions
│   ├── events.ts                   # Event management
│   ├── handlers.ts                 # Store handlers
│   └── useZustandGroupPlayStore.ts # Zustand implementation
│
├── modules/game/services/
│   ├── GroupPlayGameService.ts     # Core game logic
│   ├── GroupPlayQuestionService.ts # Question management
│   ├── GroupPlayTimingService.ts   # Timing synchronization
│   └── GroupPlayEventService.ts    # Event handling
│
├── modules/game/hooks/
│   ├── useGroupPlayStore.ts        # Store hook
│   └── useGroupPlayEvents.ts       # Event management hook
│
└── modules/game/pages/GroupGamePlay/
    ├── GroupGamePlay.tsx           # Main component
    ├── Header/                     # Header components
    ├── Footer/                     # Footer components
    ├── Question/                   # Question components
    └── README.md                   # This file
```

## 🔧 **Usage Examples**

### Basic Usage

```typescript
import useGroupPlayStore from '@/src/modules/game/hooks/useGroupPlayStore';
import useGroupPlayEvents from '@/src/modules/game/hooks/useGroupPlayEvents';

const GroupGamePlay = ({ game }) => {
  // Initialize store
  const {
    currentQuestion,
    submitAnswer,
    isQuestionPhase,
    timeRemaining,
  } = useGroupPlayStore({
    gameId: game._id,
    gameData: game,
  });

  // Set up events
  const { emitEvent } = useGroupPlayEvents({
    gameId: game._id,
    eventHandlers: {
      onAnswerSubmitted: (event) => {
        console.log('Answer submitted:', event.data);
      },
    },
  });

  const handleSubmit = async (answer) => {
    const result = await submitAnswer(answer);
    return result;
  };

  return (
    <div>
      {currentQuestion && (
        <Question 
          question={currentQuestion}
          onSubmit={handleSubmit}
          timeRemaining={timeRemaining}
        />
      )}
    </div>
  );
};
```

### Event Handling

```typescript
// Listen to specific events
const { addEventListener } = useGroupPlayEvents({ gameId });

useEffect(() => {
  const unsubscribe = addEventListener('QUESTION_STARTED', (event) => {
    console.log('New question:', event.data.questionId);
  });

  return unsubscribe;
}, [addEventListener]);

// Emit custom events
const { emitEvent } = useGroupPlayEvents({ gameId });

const handleCustomAction = () => {
  emitEvent('CUSTOM_EVENT', { 
    action: 'player_action',
    timestamp: Date.now() 
  });
};
```

### Service Usage

```typescript
import GroupPlayGameService from '@/src/modules/game/services/GroupPlayGameService';
import GroupPlayTimingService from '@/src/modules/game/services/GroupPlayTimingService';

// Game service
const gameService = new GroupPlayGameService();
const gameState = gameService.initializeGame(gameData);
const currentPhase = gameService.calculateCurrentPhase(gameState);

// Timing service
const timingService = new GroupPlayTimingService();
const serverTime = timingService.getCurrentServerTime();
const timeRemaining = timingService.calculateTimeRemaining(gameState);
```

## 🎯 **Event Types**

### Game Events
- `GAME_INITIALIZED` - Game setup complete
- `GAME_STARTED` - Game begins
- `GAME_ENDED` - Game finished
- `PHASE_CHANGED` - Game phase transition

### Question Events
- `QUESTION_STARTED` - New question begins
- `QUESTION_ENDED` - Question time expires
- `QUESTION_PRELOADED` - Question preloaded

### Answer Events
- `ANSWER_SUBMITTED` - Player submits answer
- `ANSWER_CORRECT` - Correct answer submitted
- `ANSWER_INCORRECT` - Incorrect answer submitted
- `ANSWER_TIMEOUT` - Question times out

### Player Events
- `PLAYER_JOINED` - Player joins game
- `PLAYER_LEFT` - Player leaves game
- `PLAYER_SCORE_UPDATED` - Score changes
- `LEADERBOARD_UPDATED` - Leaderboard refresh

## 🔍 **Debugging & Monitoring**

### Enable Debug Mode
```typescript
import { groupPlayEventManager } from '@/src/store/useGroupPlayStore/events';

// Enable event debugging
groupPlayEventManager.enableDebugMode();

// Get event statistics
const { getEventStats } = useGroupPlayEvents({ gameId });
const stats = getEventStats();
console.log('Event stats:', stats);
```

### Performance Monitoring
```typescript
import GroupPlayTimingService from '@/src/modules/game/services/GroupPlayTimingService';

const timingService = new GroupPlayTimingService();
const stats = timingService.getTimingStats();
console.log('Timing stats:', stats);
```

## 🚨 **Migration Guide**

### From Old Implementation

1. **Replace useGroupPlayQuestionState**:
   ```typescript
   // Old
   const { currentQuestion, submitAnswer } = useGroupPlayQuestionState();
   
   // New
   const { currentQuestion, submitAnswer } = useGroupPlayStore({ gameId, gameData });
   ```

2. **Replace WebSocket handling**:
   ```typescript
   // Old
   const { lastMessage } = useWebsocketStore();
   
   // New
   const { sendWebSocketMessage } = useGroupPlayEvents({ gameId });
   ```

3. **Replace timing calculations**:
   ```typescript
   // Old
   const timeLeft = calculateTimeRemainingInQuestion();
   
   // New
   const { questionTimeRemaining } = useGroupPlayStore({ gameId, gameData });
   ```

## 🎨 **Best Practices**

1. **Component Memoization**: Always memoize expensive components
2. **Event Cleanup**: Properly clean up event listeners
3. **Error Handling**: Use try-catch blocks for async operations
4. **Type Safety**: Leverage TypeScript for better development experience
5. **Performance**: Use optimistic updates for better UX

## 🔮 **Future Enhancements**

- **Offline Support**: Cache game state for offline play
- **Analytics Integration**: Built-in analytics tracking
- **A/B Testing**: Support for feature flags and experiments
- **Machine Learning**: Predictive preloading based on user behavior
- **WebAssembly**: Move critical calculations to WASM for better performance
