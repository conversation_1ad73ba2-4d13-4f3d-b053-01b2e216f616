import dark from "core/constants/themes/dark";
import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
    container: {
        flex: 1,
        overflow: "hidden",
        gap: 100
    },
    headerText: {
        fontFamily: "Montserrat-600",
        fontSize: 17,
        color: dark.colors.primary,
        lineHeight: 24
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
    },
    topThreeContainer: {
        flexDirection: 'row',
        width: "100%"
    },
    topCard: {
        alignItems: 'center',
        justifyContent: "flex-end",
        paddingBottom: 55,
        // flex: 1
    },
    header: {
        position: "absolute",
        width: "100%",
        top: 20,
        flexDirection: "row",
        alignItems: "center",
        gap: 100,
        paddingLeft: 12
    },
    avatar: {
        width: 48,
        height: 48,
        borderRadius: 4,
        overflow: "hidden"
    },
    name: {
        marginTop: 10,
        fontSize: 14,
        fontFamily: "Montserrat-600",
        lineHeight: 20,
        maxWidth:100,
        color: dark.colors.primary
    },
    rating: {
        fontSize: 11,
        fontFamily: "Montserrat-700",
        lineHeight: 13,
        color: dark.colors.primary
    },
    score: {
        marginTop: 10,
        fontSize: 14,
        fontFamily: "Montserrat-600",
        lineHeight: 20,
        color: dark.colors.primary
    },
    rank: {
        marginTop: 25,
        fontSize: 24,
        lineHeight: 28,
        fontFamily: 'Montserrat-700',
        color: dark.colors.errorDark,
    },
    othersList: {
        marginTop: 20,
        paddingHorizontal: 20,
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 15,
        padding: 10,
        backgroundColor: '#fff',
        borderRadius: 8,
        elevation: 2,
    },
    rankNumber: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#444',
    },
    rowDetails: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    smallAvatar: {
        width: 40,
        height: 40,
        borderRadius: 20,
        marginRight: 10,
    },
    otherName: {
        fontSize: 14,
        color: '#444',
    },
    otherScore: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#000',
    },
    button: {
        margin: 20,
        padding: 15,
        backgroundColor: '#AAFFAA',
        borderRadius: 10,
        alignItems: 'center',
    },
    buttonText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#444',
    },
    userInfo: {
        flexDirection: "row",
        alignItems: "flex-start",
        flex: 1.8,
    },
    scoreDetail: {
        justifyContent: "flex-end",
        alignItems: "center",
        flex: 0.3
    },
    rankHeader: {
        flex: 0.3,
    },
    rowHeadingText: {
        fontFamily: "Montserrat-500",
        fontSize: 12,
        lineHeight: 20,
        color: dark.colors.textDark
    }
});

export default styles