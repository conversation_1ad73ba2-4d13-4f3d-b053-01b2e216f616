import React, { memo } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import Dark from '@/src/core/constants/themes/dark';

const LoadingState: React.FC = memo(() => {
  return (
    <View style={styles.container}>
      <ActivityIndicator 
        size="large" 
        color={Dark.colors.secondary} 
      />
      <Text style={styles.loadingText}>
        Loading Game...
      </Text>
    </View>
  );
});

const styles = {
  container: {
    flex: 1,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    backgroundColor: Dark.colors.background,
    gap: 16,
  },
  loadingText: {
    color: Dark.colors.textDark,
    fontSize: 16,
    fontFamily: 'Montserrat-500',
  },
};

LoadingState.displayName = 'LoadingState';

export default LoadingState;
