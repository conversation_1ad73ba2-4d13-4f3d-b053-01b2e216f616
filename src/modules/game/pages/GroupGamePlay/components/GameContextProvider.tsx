import React, { createContext, useContext, memo } from 'react';

// Create a context that mimics the old useGameContext
const GroupPlayGameContext = createContext<any>(null);

interface GameContextProviderProps {
  children: React.ReactNode;
  gameState: any;
  players: any[];
  game: any;
}

const GameContextProvider: React.FC<GameContextProviderProps> = memo(({
  children,
  gameState,
  players,
  game,
}) => {
  // Transform the new store data to match the old context interface
  const contextValue = {
    players: players || [],
    game: game || {},
    gameState,
    // Add any other properties that the Header component expects
  };

  return (
    <GroupPlayGameContext.Provider value={contextValue}>
      {children}
    </GroupPlayGameContext.Provider>
  );
});

// Hook to use the game context (replacement for useGameContext)
export const useGroupPlayGameContext = () => {
  const context = useContext(GroupPlayGameContext);
  if (!context) {
    throw new Error('useGroupPlayGameContext must be used within GameContextProvider');
  }
  return context;
};

GameContextProvider.displayName = 'GameContextProvider';

export default GameContextProvider;
