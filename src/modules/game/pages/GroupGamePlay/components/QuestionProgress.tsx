import React, { memo } from 'react';
import { View, Text } from 'react-native';
import Dark from '@/src/core/constants/themes/dark';

interface QuestionProgressProps {
  current: number;
  total: number;
}

const QuestionProgress: React.FC<QuestionProgressProps> = memo(({
  current,
  total,
}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.progressText}>
        {current}/{total}
      </Text>
    </View>
  );
});

const styles = {
  container: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    backgroundColor: Dark.colors.cardBackground,
    borderRadius: 12,
  },
  progressText: {
    fontFamily: 'Montserrat-600',
    fontSize: 14,
    color: Dark.colors.textDark,
  },
};

QuestionProgress.displayName = 'QuestionProgress';

export default QuestionProgress;
