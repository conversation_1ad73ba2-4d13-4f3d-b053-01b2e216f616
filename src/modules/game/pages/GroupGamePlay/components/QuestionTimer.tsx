import React, { memo } from 'react';
import { View, Text } from 'react-native';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Dark from '@/src/core/constants/themes/dark';

interface QuestionTimerProps {
  timeRemaining: number;
  isQuestionPhase: boolean;
  formatTime: (ms: number) => string;
}

const QuestionTimer: React.FC<QuestionTimerProps> = memo(({
  timeRemaining,
  isQuestionPhase,
  formatTime,
}) => {
  if (!isQuestionPhase) {
    return null;
  }

  return (
    <View style={styles.container}>
      <MaterialIcons name="timer" color={Dark.colors.textDark} size={20} />
      <Text style={styles.timerText}>
        {formatTime(timeRemaining)}
      </Text>
    </View>
  );
});

const styles = {
  container: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 4,
  },
  timerText: {
    fontFamily: 'Montserrat-600',
    fontSize: 16,
    color: Dark.colors.textDark,
  },
};

QuestionTimer.displayName = 'QuestionTimer';

export default QuestionTimer;
