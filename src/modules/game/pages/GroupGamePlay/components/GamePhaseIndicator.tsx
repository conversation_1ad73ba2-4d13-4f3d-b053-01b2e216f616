import React, { memo } from 'react';
import { View, Text } from 'react-native';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import WaitingPhaseTimer from '../WaitingPhaseTimer';
import Dark from '@/src/core/constants/themes/dark';

interface GamePhaseIndicatorProps {
  isQuestionPhase: boolean;
  isWaitingPhase: boolean;
  currentPhase: any;
  formatTime: (ms: number) => string;
}

const GamePhaseIndicator: React.FC<GamePhaseIndicatorProps> = memo(({
  isQuestionPhase,
  isWaitingPhase,
  currentPhase,
  formatTime,
}) => {
  // Always render container to prevent layout shifts
  return (
    <View style={styles.phaseContainer}>
      {isWaitingPhase && (
        <WaitingPhaseTimer
          phase={currentPhase}
          formatTime={formatTime}
          onPhaseEnd={() => {
            // Phase transition is handled by the store
          }}
        />
      )}

      {isQuestionPhase && (
        <View style={styles.questionPhaseIndicator}>
          <MaterialIcons
            name="quiz"
            size={16}
            color={Dark.colors.secondary}
          />
          <Text style={styles.phaseText}>Question Phase</Text>
        </View>
      )}
    </View>
  );
});

const styles = {
  phaseContainer: {
    width: '100%',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    paddingVertical: 8,
    minHeight: 40, // Prevent layout shifts
  },
  questionPhaseIndicator: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    gap: 8,
  },
  phaseText: {
    color: Dark.colors.textDark,
    fontSize: 14,
    fontFamily: 'Montserrat-500',
  },
};

GamePhaseIndicator.displayName = 'GamePhaseIndicator';

export default GamePhaseIndicator;
