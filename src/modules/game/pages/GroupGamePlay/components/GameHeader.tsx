import React, { memo } from 'react';
import { View } from 'react-native';
import Header from '../Header';
import GamePhaseIndicator from './GamePhaseIndicator';
import QuestionTimer from './QuestionTimer';
import QuestionProgress from './QuestionProgress';

interface GameHeaderProps {
  currentQuestionId: string | null;
  isQuestionPhase: boolean;
  isWaitingPhase: boolean;
  currentPhase: any;
  questionTimeRemaining: number;
  gameProgress: { current: number; total: number };
  formatTime: (ms: number) => string;
  styles: any;
}

const GameHeader: React.FC<GameHeaderProps> = memo(({
  currentQuestionId,
  isQuestionPhase,
  isWaitingPhase,
  currentPhase,
  questionTimeRemaining,
  gameProgress,
  formatTime,
  styles,
}) => {
  return (
    <View style={styles.mobileHeader}>
      <Header currentQuestionId={currentQuestionId} />

      {/* Timer and Progress Row */}
      <View style={styles.timerProgressRow}>
        <QuestionTimer
          timeRemaining={questionTimeRemaining}
          isQuestionPhase={isQuestionPhase}
          formatTime={formatTime}
        />

        <QuestionProgress
          current={gameProgress.current}
          total={gameProgress.total}
        />
      </View>

      <GamePhaseIndicator
        isQuestionPhase={isQuestionPhase}
        isWaitingPhase={isWaitingPhase}
        currentPhase={currentPhase}
        formatTime={formatTime}
      />
    </View>
  );
});

GameHeader.displayName = 'GameHeader';

export default GameHeader;
