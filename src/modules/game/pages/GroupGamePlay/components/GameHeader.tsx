import React, { memo } from 'react';
import { View } from 'react-native';
import Header from '../Header';
import GamePhaseIndicator from './GamePhaseIndicator';

interface GameHeaderProps {
  currentQuestionId: string | null;
  isQuestionPhase: boolean;
  isWaitingPhase: boolean;
  currentPhase: any;
  formatTime: (ms: number) => string;
  styles: any;
}

const GameHeader: React.FC<GameHeaderProps> = memo(({
  currentQuestionId,
  isQuestionPhase,
  isWaitingPhase,
  currentPhase,
  formatTime,
  styles,
}) => {
  return (
    <View style={styles.mobileHeader}>
      <Header currentQuestionId={currentQuestionId} />
      
      <GamePhaseIndicator
        isQuestionPhase={isQuestionPhase}
        isWaitingPhase={isWaitingPhase}
        currentPhase={currentPhase}
        formatTime={formatTime}
      />
    </View>
  );
});

GameHeader.displayName = 'GameHeader';

export default GameHeader;
