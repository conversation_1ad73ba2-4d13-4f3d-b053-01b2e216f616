import React, { memo } from 'react';
import { View } from 'react-native';
import Header from '../Header';
import GamePhaseIndicator from './GamePhaseIndicator';
import QuestionTimer from './QuestionTimer';
import QuestionProgress from './QuestionProgress';
import GameContextProvider from './GameContextProvider';

interface GameHeaderProps {
  currentQuestionId: string | null;
  isQuestionPhase: boolean;
  isWaitingPhase: boolean;
  currentPhase: any;
  questionTimeRemaining: number;
  gameProgress: { current: number; total: number };
  formatTime: (ms: number) => string;
  gameState: any;
  players: any[];
  game: any;
  styles: any;
}

const GameHeader: React.FC<GameHeaderProps> = memo(({
  currentQuestionId,
  isQuestionPhase,
  isWaitingPhase,
  currentPhase,
  questionTimeRemaining,
  gameProgress,
  formatTime,
  gameState,
  players,
  game,
  styles,
}) => {
  return (
    <View style={styles.mobileHeader}>
      <GameContextProvider
        gameState={gameState}
        players={players}
        game={game}
      >
        <Header currentQuestionId={currentQuestionId} />
      </GameContextProvider>

      {/* Timer and Progress Row */}
      <View style={styles.timerProgressRow}>
        <QuestionTimer
          timeRemaining={questionTimeRemaining}
          isQuestionPhase={isQuestionPhase}
          formatTime={formatTime}
        />

        <QuestionProgress
          current={gameProgress.current}
          total={gameProgress.total}
        />
      </View>

      <GamePhaseIndicator
        isQuestionPhase={isQuestionPhase}
        isWaitingPhase={isWaitingPhase}
        currentPhase={currentPhase}
        formatTime={formatTime}
      />
    </View>
  );
});

GameHeader.displayName = 'GameHeader';

export default GameHeader;
