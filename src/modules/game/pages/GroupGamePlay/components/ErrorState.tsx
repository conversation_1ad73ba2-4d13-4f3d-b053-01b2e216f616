import React, { memo } from 'react';
import { View, Text } from 'react-native';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Dark from '@/src/core/constants/themes/dark';

interface ErrorStateProps {
  error: string;
}

const ErrorState: React.FC<ErrorStateProps> = memo(({ error }) => {
  return (
    <View style={styles.container}>
      <MaterialIcons 
        name="error-outline" 
        size={48} 
        color={Dark.colors.defeatColor} 
      />
      <Text style={styles.errorTitle}>
        Something went wrong
      </Text>
      <Text style={styles.errorMessage}>
        {error}
      </Text>
    </View>
  );
});

const styles = {
  container: {
    flex: 1,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    backgroundColor: Dark.colors.background,
    gap: 16,
    paddingHorizontal: 32,
  },
  errorTitle: {
    color: Dark.colors.textDark,
    fontSize: 18,
    fontFamily: 'Montserrat-600',
    textAlign: 'center' as const,
  },
  errorMessage: {
    color: Dark.colors.textLight,
    fontSize: 14,
    fontFamily: 'Montserrat-400',
    textAlign: 'center' as const,
    lineHeight: 20,
  },
};

ErrorState.displayName = 'ErrorState';

export default ErrorState;
