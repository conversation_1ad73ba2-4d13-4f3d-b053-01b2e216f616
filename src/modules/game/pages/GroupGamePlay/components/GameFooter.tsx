import React, { memo } from 'react';
import { View } from 'react-native';
import Footer from '../Footer';

interface GameFooterProps {
  currentQuestion: any;
  isQuestionPhase: boolean;
  isGameActive: boolean;
  questionStartTime: number;
  onSubmitAnswer: (answer: string) => Promise<boolean>;
  onForceSubmission: () => void;
  styles: any;
}

const GameFooter: React.FC<GameFooterProps> = memo(({
  currentQuestion,
  isQuestionPhase,
  isGameActive,
  questionStartTime,
  onSubmitAnswer,
  onForceSubmission,
  styles,
}) => {
  if (!currentQuestion || !isQuestionPhase) {
    return null;
  }

  return (
    <View style={styles.footerContainer}>
      <Footer
        question={currentQuestion}
        submitAnswer={onSubmitAnswer}
        isGameActive={isGameActive && isQuestionPhase}
        startTime={questionStartTime}
        handleForceQuestionSubmission={onForceSubmission}
      />
    </View>
  );
});

GameFooter.displayName = 'GameFooter';

export default GameFooter;
