import React, { memo } from 'react';
import { View } from 'react-native';
import Footer from '../Footer';
import GameContextProvider from './GameContextProvider';

interface GameFooterProps {
  currentQuestion: any;
  isQuestionPhase: boolean;
  isGameActive: boolean;
  questionStartTime: number;
  onSubmitAnswer: (answer: string) => Promise<boolean>;
  onForceSubmission: () => void;
  gameState: any;
  players: any[];
  game: any;
  styles: any;
}

const GameFooter: React.FC<GameFooterProps> = memo(({
  currentQuestion,
  isQuestionPhase,
  isGameActive,
  questionStartTime,
  onSubmitAnswer,
  onForceSubmission,
  gameState,
  players,
  game,
  styles,
}) => {
  // Always show footer (keyboard) as requested
  return (
    <View style={styles.footerContainer}>
      {currentQuestion && (
        <GameContextProvider
          gameState={gameState}
          players={players}
          game={game}
        >
          <Footer
            question={currentQuestion}
            submitAnswer={onSubmitAnswer}
            isGameActive={isGameActive}
            startTime={questionStartTime}
            handleForceQuestionSubmission={onForceSubmission}
          />
        </GameContextProvider>
      )}
    </View>
  );
});

GameFooter.displayName = 'GameFooter';

export default GameFooter;
