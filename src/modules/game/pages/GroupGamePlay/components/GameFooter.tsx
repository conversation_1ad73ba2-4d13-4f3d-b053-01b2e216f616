import React, { memo } from 'react';
import { View } from 'react-native';
import Footer from '../Footer';

interface GameFooterProps {
  currentQuestion: any;
  isQuestionPhase: boolean;
  isGameActive: boolean;
  questionStartTime: number;
  onSubmitAnswer: (answer: string) => Promise<boolean>;
  onForceSubmission: () => void;
  styles: any;
}

const GameFooter: React.FC<GameFooterProps> = memo(({
  currentQuestion,
  isQuestionPhase,
  isGameActive,
  questionStartTime,
  onSubmitAnswer,
  onForceSubmission,
  styles,
}) => {
  // Always show footer (keyboard) as requested
  return (
    <View style={styles.footerContainer}>
      {currentQuestion && (
        <Footer
          question={currentQuestion}
          submitAnswer={onSubmitAnswer}
          isGameActive={isGameActive}
          startTime={questionStartTime}
          handleForceQuestionSubmission={onForceSubmission}
        />
      )}
    </View>
  );
});

GameFooter.displayName = 'GameFooter';

export default GameFooter;
