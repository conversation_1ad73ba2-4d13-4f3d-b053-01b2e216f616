import React, { memo } from 'react';
import { View } from 'react-native';
import WaitingPhaseTimer from '../WaitingPhaseTimer';
import Dark from '@/src/core/constants/themes/dark';

interface WaitingStateProps {
  header: React.ReactNode;
  currentPhase: any;
  formatTime: (ms: number) => string;
}

const WaitingState: React.FC<WaitingStateProps> = memo(({
  header,
  currentPhase,
  formatTime,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        {header}
      </View>
      
      <View style={styles.waitingContainer}>
        <WaitingPhaseTimer
          phase={currentPhase}
          formatTime={formatTime}
          onPhaseEnd={() => {
            // Phase transition is handled by the store
          }}
        />
      </View>
    </View>
  );
});

const styles = {
  container: {
    flex: 1,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    backgroundColor: Dark.colors.background,
  },
  headerContainer: {
    width: '100%',
    alignItems: 'center' as const,
  },
  waitingContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    padding: 32,
  },
};

WaitingState.displayName = 'WaitingState';

export default WaitingState;
