import React, { memo } from 'react';
import { View } from 'react-native';
import Question from '../Question';
import PointGainedInCurrRoundInfo from '../PointGainedInCurrRoundInfo';

interface GameContentProps {
  currentQuestion: any;
  isQuestionPhase: boolean;
  currentPlayer: any;
  styles: any;
}

const GameContent: React.FC<GameContentProps> = memo(({
  currentQuestion,
  isQuestionPhase,
  currentPlayer,
  styles,
}) => {
  // Always render the container, let individual components handle their visibility
  return (
    <View style={styles.question}>
      {currentQuestion && (
        <Question
          question={currentQuestion}
          renderQuestionOverlay={null}
        />
      )}

      {currentPlayer && currentQuestion?.hasSolved && (
        <PointGainedInCurrRoundInfo />
      )}
    </View>
  );
});

GameContent.displayName = 'GameContent';

export default GameContent;
