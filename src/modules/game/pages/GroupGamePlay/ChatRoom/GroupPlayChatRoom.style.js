import dark from 'core/constants/themes/dark'
import { Dimensions, StyleSheet } from 'react-native'

const styles = StyleSheet.create({
  container: {
    flex: 1,
    maxWidth: 350,
    justifyContent: 'center',
    backgroundColor: dark.colors.gradientBackground,
    paddingHorizontal: 16,
    paddingVertical: 15,
    borderRadius: 12,
    marginVertical: 20,
    marginRight: 16,
  },
  header: {
    textAlign: 'left',
    color: dark.colors.textDark,
    fontSize: 10,
    paddingBottom: 14,
    fontFamily: 'Montserrat-500',
  },
  chatList: {},
  messageContainer: {
    marginBottom: 10,
  },
  sender: {
    fontSize: 11,
    fontFamily: 'Montserrat-500',
    color: '#FFF563',
  },
  message: {
    fontSize: 11,
    color: '#FFFFFF',
    marginLeft: 30,
    marginTop: -10,
    fontFamily: 'Montserrat-500',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    // padding: 10,
  },
  input: {
    flex: 1,
    height: 80,
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 8,
    color: '#FFFFFF',
    backgroundColor: '#333',
    outlineStyle: 'none',
    fontFamily: 'Montserrat-500',
  },
  sendButton: {
    marginLeft: 10,
    backgroundColor: dark.colors.secondary,
    borderRadius: 15,
    height: 30,
    width: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendText: {
    color: '#000000',
    fontWeight: 'bold',
  },
})

export default styles
