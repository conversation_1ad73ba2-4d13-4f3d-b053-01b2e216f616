import {StyleSheet} from 'react-native'
import Dark from '@/src/core/constants/themes/dark'

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Dark.colors.background,
  },
  mainContainerWeb: {
    // justifyContent: 'center',
    gap: 24,
    flexDirection: "row"
  },
  container: {
    flex: 1,
    flexDirection: 'column',
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'space-evenly',
    backgroundColor: Dark.colors.background,
  },
  webContainer: {
    flexDirection: 'column',
    width: '100%',
    height: '100%',
    alignItems: 'center',
    backgroundColor: Dark.colors.background,
    justifyContent: 'center',
    gap: 24,
    flex: 3
  },
  mobileHeader: {
    width: '100%',
    alignItems: 'center',
  },
  timerProgressRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  timerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  timerContentContainer: {
    width: '100%',
    minHeight: 300,
  },
  question: {
    flex: 1,
    width: '90%',
    maxHeight: 400,
    maxWidth: 420,
  },
  footerContainer: {
    width: '100%',
    maxWidth: 420,
  },
  overlay: {
    // flex: 1,
    width: '100%',
    maxWidth: 400,
    Height: 400,
    backgroundColor: Dark.colors.background,
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  timerOverlay: {
    padding: 0,
    margin: 0,
    flex: 1,
    backgroundColor: Dark.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  time: {
    fontSize: 24,
    fontFamily: 'Montserrat-700',
    color: Dark.colors.stroke,
  },
  timerText: {
    color: Dark.colors.textDark,
    fontSize: 18,
    fontFamily: "Montserrat-500"
  },
})

export default styles
