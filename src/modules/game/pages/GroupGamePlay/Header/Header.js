import React, {useCallback} from 'react'
import {ScrollView} from 'react-native'
import useMediaQuery from '@/src/core/hooks/useMediaQuery'
import _map from "lodash/map"
import _findIndex from "lodash/findIndex"
import _isEqual from "lodash/isEqual"
import useGameContext from '../../../hooks/useGameContext'
import { useGroupPlayGameContext } from '../components/GameContextProvider'
import UserScoreCard from './UserScoreCard'
import {processGroupPlayPlayersData} from "@/src/modules/game/utils/processGroupPlayPlayersData"
import dark from '@/src/core/constants/themes/dark'
import useGroupPlayUserScore from "modules/game/hooks/useGroupPlayUserScore";

const Header = ({currentQuestionId}) => {
  // Try to use the new context first, fallback to old context
  let players, game;
  try {
    const newContext = useGroupPlayGameContext();
    players = newContext.players;
    game = newContext.game;
  } catch {
    // Fallback to old context if new context is not available
    const oldContext = useGameContext();
    players = oldContext.players;
    game = oldContext.game;
  }

  const {isMobile} = useMediaQuery()

  const {getUserCurrentQuestionScore} = useGroupPlayUserScore({game});

  const {rankedPlayers} = processGroupPlayPlayersData({players, game}) ?? EMPTY_OBJECT

  const {userSubmissionsWithQuestion} = game ?? EMPTY_OBJECT

  const checkIfUserHasSubmittedCurrQue = useCallback(({playerId}) => {
    const currentQuestionIndex = _findIndex(userSubmissionsWithQuestion, (que) => _isEqual(que?.questionId, currentQuestionId))
    if (currentQuestionIndex != -1) {
      const currentQueObj = userSubmissionsWithQuestion[currentQuestionIndex]
      const submissionIndex = _findIndex(currentQueObj?.submissions, (submission) => _isEqual(submission?.userId, playerId))

      return submissionIndex != -1
    }

    return false
  }, [userSubmissionsWithQuestion, currentQuestionId])

  const renderCurrentUserScoreCard = (player, index) => {
    const {hasSolved, score} = getUserCurrentQuestionScore({
      playerId: player?._id,
      questionId: currentQuestionId,
    });
    return (
      <UserScoreCard
        user={player}
        score={player?.score}
        currentQuesScore={score}
        key={`${index}`}
        containerStyle={hasSolved && {borderColor: dark.colors.secondary}}
        rank={index + 1}/>
    );
  };

  if (!isMobile) {
    return null
  }

  return (
    <ScrollView style={{maxWidth: 420, width: '98%', flexDirection: 'row', height: 54, gap: 10,}} horizontal
                showsHorizontalScrollIndicator={false}>
      {
        _map((rankedPlayers), renderCurrentUserScoreCard)
      }
    </ScrollView>
  )
}

export default Header
