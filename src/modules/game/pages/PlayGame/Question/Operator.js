import React from 'react'
import { Text } from 'react-native'
import Svg, { Path } from 'react-native-svg'

const OperatorSVG = ({ operator, size = 16 }) => {
  // Define the SVG paths for each operator
  const operatorPaths = {
    '+': 'M12 2 L12 22 M2 12 L22 12',
    '-': 'M2 12 L22 12',
    '×': 'M4 4 L20 20 M20 4 L4 20',
    '*': 'M12 2 L12 22 M2 12 L22 12 M6 6 L18 18 M18 6 L6 18',
    '÷': 'M2 12 L22 12 M12 5 A1 1 0 1 1 11.99 5 M12 19 A1 1 0 1 1 11.99 19',
  }

  // Get the SVG path for the provided operator
  const path = operatorPaths[operator]

  // If operator is not valid, show an error
  if (!path) {
    return <Text>{operator}</Text>
  }

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 24 24" // Set the viewBox to match the 24px size
    >
      <Path
        d={path}
        stroke="white"
        strokeWidth="2" // Adjust the line thickness
        fill="none"
        strokeLinecap="round" // Optional: Round the line ends for a polished look
      />
    </Svg>
  )
}

export default OperatorSVG
