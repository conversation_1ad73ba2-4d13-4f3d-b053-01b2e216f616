import { StyleSheet } from "react-native";
import dark from "../../../../../../core/constants/themes/dark";

const styles = StyleSheet.create({
    container: {
        // flex: 1,
        flexDirection: "row",
        justifyContent: 'space-between',
        marginVertical:10,
        alignItems:"center"
    },
    userContainer: {
        flex: 1,
        flexDirection: 'row',
        paddingVertical: 4,
        borderWidth: 1.5,
        borderRadius: 8,
    },
    opponentContainer:{
        flex: 1,
        flexDirection: 'row',
        paddingVertical: 4,
        borderWidth: 1.5,
        borderRadius: 8,
    },
    currUserScore: {
        textAlign: "center",
        flex: 1,
        fontFamily: "Montserrat-700",
        fontSize: 12,
        lineHeight: 19,
        color: 'white'
    },
    opponentUserScore: {
        textAlign: "center",
        flex: 1,
        fontFamily: "Montserrat-700",
        fontSize: 12,
        lineHeight: 19,
        color: 'white'
    },
    statsFieldContainer:{
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginHorizontal: 20,
    },
    statsField: {
        fontFamily: "Montserrat-600",
        fontSize: 10,
        letterSpacing: 1,
        lineHeight: 13,
        textAlign: "center",
        flex: 1,
        color:dark.colors.textLight
    },
    emptyIcon:{
        height: 10, 
        width: 6,
    }
})

export default styles