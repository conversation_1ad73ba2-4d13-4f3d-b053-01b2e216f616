import { Dimensions, StyleSheet } from "react-native";
import dark from "core/constants/themes/dark";

const styles = StyleSheet.create({
        container: {
        maxWidth: Dimensions.get("window").width * 0.9,
        width: '100%',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexDirection: 'row',
        height: 54,
        gap: 16,
    },
    containerMob: {
        maxWidth: 'auto',
        borderWidth: 0.5,
        width: '100%',
        borderColor: '#666',
        borderRadius: 12,
        overflow: 'hidden',
    },
    timerBox: {
        flexDirection: 'row',
        alignItems: 'flex-end',
        width: 90,
        justifyContent: 'center',
    },
    timerText: {
        marginBottom: -1,
        fontSize: 18,
        fontFamily: 'Montserrat-700',
        color: dark.colors.textDark
    },
    timerText2: {
        marginBottom: -1,
        fontSize: 14,
        fontFamily: 'Montserrat-700',
        color: '#FFFFFF',
    },
    backImg: {
        position: 'absolute',
        width: '100%',
        height: '100%',
        resizeMode: 'stretch',
    },
})

export default styles