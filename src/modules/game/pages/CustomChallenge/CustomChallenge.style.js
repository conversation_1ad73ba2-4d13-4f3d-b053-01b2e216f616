import { StyleSheet } from "react-native";
import dark from "../../../../core/constants/themes/dark";

const styles = StyleSheet.create({
    container: {
        flex: 1,
        overflow: "hidden"
    },
    headerContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%"
    },
    headerText: {
        fontFamily: "Montserrat-500",
        fontSize: 15,
        lineHeight: 24,
        color: "white"
    },
    vsText: {
        fontFamily: "Montserrat-700",
        fontSize: 14,
        lineHeight: 16,
        color: "white",
        marginBottom: 20
    },
    usersInfoRow: {
        alignItems: "center",
        gap: 20,
        paddingHorizontal: 70,
        flexDirection: "row"
    },
    userImage: {
        height: 112,
        width: 112,
        borderRadius: 8,
        overflow: "hidden",
        borderWidth: 1,
        borderColor: "#363636"
    },
    userInfo: {
        alignItems: "center",
        gap: 3
    },
    userName: {
        fontFamily: "Montserrat-500",
        color: "white",
        lineHeight: 12,
        fontSize: 10,
        maxWidth: 140
    },
    rating: {
        fontFamily: "Montserrat-600",
        color: dark.colors.textDark,
        lineHeight: 11,
        fontSize: 9,
        maxWidth: 55,
        textAlign: "center"
    },
    gameConfigContainer: {
        // justifyContent: "center",
        // alignItems: "center",
        paddingTop: 15,
        paddingHorizontal: 16
    },
    configOptionsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        alignItems: 'center',
        gap: 20,
        // width: '100%',
    },
    buttonContainer: {
        width: '45%',
        maxWidth: 130,
    },
    compactButtonContainer: {
        maxWidth: 120,
    },
    timeContainer: {
        backgroundColor: "transparent",
        borderColor: dark.colors.tertiary,
        borderWidth: 1,
        height: 40
    },
    buttonLabelStyle: {
        fontSize: 14,
        fontFamily: 'Montserrat-600'
    },
    innerContainer: {
        justifyContent: "center",
        flex: 1,
        alignItems: "center",
        gap: 20
    },
    challengeButton: {
        position: "absolute",
        bottom: 15,
        width: "100%",
        justifyContent: 'center',
        alignItems: "center"
    },
    sprintDuelText: {
        fontSize: 9,
        textAlign: "center",
        fontFamily: 'Montserrat-600',
        color: dark.colors.textDark,
        marginTop: 60,
        marginBottom: 20
    }
})

export default styles