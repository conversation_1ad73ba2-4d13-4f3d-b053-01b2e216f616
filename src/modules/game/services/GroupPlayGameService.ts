import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _reduce from 'lodash/reduce';
import _filter from 'lodash/filter';
import _findIndex from 'lodash/findIndex';
import _isEqual from 'lodash/isEqual';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import gameReader from '@/src/core/readers/gameReader';
import {
  GroupPlayGameState,
  GroupPlayGamePhase,
  GroupPlayQuestion,
  GroupPlayPlayer,
  GroupPlayGameServiceInterface,
  GroupPlayGameConfig,
} from '@/src/store/useGroupPlayStore/types';
import { groupPlayEventManager, GROUP_PLAY_EVENTS } from '@/src/store/useGroupPlayStore/events';

const EMPTY_OBJECT = {};
const EMPTY_ARRAY = [];
const WAITING_TIME = 5000; // 5 seconds

class GroupPlayGameService implements GroupPlayGameServiceInterface {
  private static instance: GroupPlayGameService;

  constructor() {
    if (GroupPlayGameService.instance) {
      return GroupPlayGameService.instance;
    }
    GroupPlayGameService.instance = this;
  }

  initializeGame(gameData: any): GroupPlayGameState {
    const {
      _id: gameId,
      startTime,
      endTime,
      config,
      players = EMPTY_ARRAY,
      questions: allQuestions = EMPTY_ARRAY,
      gameStatus,
      userSubmissionsWithQuestion = EMPTY_ARRAY,
    } = gameData ?? EMPTY_OBJECT;

    const gameConfig: GroupPlayGameConfig = {
      timeLimit: _get(config, 'timeLimit', 300),
      maxTimePerQuestion: _get(config, 'maxTimePerQuestion', 10),
      waitingTime: WAITING_TIME / 1000,
      numPlayers: _get(config, 'numPlayers', 4),
      gameType: gameReader.gameType(gameData) || '',
      gameMode: gameReader.gameMode(gameData) || '',
    };

    // Initialize questions state
    const questions = this.initializeQuestions(allQuestions, userSubmissionsWithQuestion);

    // Initialize players state
    const playersState = this.initializePlayers(players);

    // Calculate current phase and question
    const gameStartTime = new Date(startTime).getTime();
    const currentTime = getCurrentTimeWithOffset();
    const currentPhase = this.calculateCurrentPhase({
      gameId,
      gameStatus,
      startTime: gameStartTime,
      config: gameConfig,
    } as any);

    const currentQuestionIndex = this.calculateCurrentQuestionIndex({
      gameId,
      gameStatus,
      startTime: gameStartTime,
      config: gameConfig,
      allQuestions,
    } as any);

    const currentQuestionId = _get(allQuestions[currentQuestionIndex], ['question', 'id'], null);

    const initialState: GroupPlayGameState = {
      // Game Core Data
      gameId,
      gameStatus,
      startTime: gameStartTime,
      endTime: endTime ? new Date(endTime).getTime() : undefined,
      config: gameConfig,

      // Questions Management
      questions,
      allQuestions,
      currentQuestionId,
      currentQuestionIndex,
      totalQuestions: allQuestions.length,
      questionStartTime: this.calculateQuestionStartTime(gameStartTime, currentQuestionIndex, gameConfig),

      // Players Management
      players: playersState,
      currentUserId: '', // Will be set by the component
      leaderboard: Object.values(playersState),

      // Game Phase Management
      currentPhase,
      isGameReady: currentTime >= gameStartTime,
      isGameActive: this.isGameActive({ gameStatus } as any),

      // Timing & Synchronization
      serverTimeOffset: 0,
      lastSyncTime: currentTime,
      cycleStartTime: gameStartTime,

      // UI State
      loading: false,
      error: null,
      isSubmitting: false,
      showLeaderboard: false,

      // Performance Optimizations
      preloadedQuestions: {},
      optimisticUpdates: {},

      // Event Management
      eventQueue: [],
      lastEventId: null,
    };

    // Emit initialization event
    groupPlayEventManager.emit(GROUP_PLAY_EVENTS.GAME_INITIALIZED, {
      gameId,
      gameState: initialState,
    }, gameId);

    return initialState;
  }

  calculateCurrentPhase(gameState: GroupPlayGameState): GroupPlayGamePhase {
    const { startTime, config, isGameReady } = gameState;
    const currentTime = getCurrentTimeWithOffset();

    if (!isGameReady) {
      return {
        type: 'INITIAL_WAITING',
        startTime: currentTime,
        endTime: startTime,
        duration: startTime - currentTime,
      };
    }

    const cycleTime = (config.maxTimePerQuestion * 1000) + WAITING_TIME;
    const timeSinceStart = currentTime - startTime;
    const currentCycle = Math.floor(timeSinceStart / cycleTime);
    const currentQuestionStartTime = startTime + (currentCycle * cycleTime);
    const timeIntoCurrentPhase = currentTime - currentQuestionStartTime;

    if (timeIntoCurrentPhase < config.maxTimePerQuestion * 1000) {
      return {
        type: 'QUESTION',
        startTime: currentQuestionStartTime,
        endTime: currentQuestionStartTime + (config.maxTimePerQuestion * 1000),
        duration: config.maxTimePerQuestion * 1000,
      };
    } else {
      return {
        type: 'WAITING',
        startTime: currentQuestionStartTime + (config.maxTimePerQuestion * 1000),
        endTime: currentQuestionStartTime + cycleTime,
        duration: WAITING_TIME,
      };
    }
  }

  calculateCurrentQuestionIndex(gameState: GroupPlayGameState): number {
    const { startTime, config } = gameState;
    const currentTime = getCurrentTimeWithOffset();
    const timeElapsed = currentTime - startTime;
    const totalTimePerQuestion = (config.maxTimePerQuestion * 1000) + WAITING_TIME;
    return Math.floor(timeElapsed / totalTimePerQuestion);
  }

  isGameActive(gameState: GroupPlayGameState): boolean {
    const { gameStatus } = gameState;
    const inactiveStatuses = ['ENDED', 'CANCELLED', 'ABORTED'];
    return !inactiveStatuses.includes(gameStatus);
  }

  private initializeQuestions(
    allQuestions: any[],
    userSubmissions: any[]
  ): Record<string, GroupPlayQuestion> {
    return _reduce(
      allQuestions,
      (acc, questionObject) => {
        const { submissions = [], question } = questionObject;
        const { id } = question;

        // Find user's submission for this question
        const userSubmission = _filter(submissions, (submission) => 
          submission?.userId && submission.userId !== ''
        )[0];

        acc[id] = {
          id,
          question,
          answers: question.answers || [],
          maxTimeLimit: _get(question, 'maxTimeLimit', 10) * 1000,
          hasSolved: !_isEmpty(userSubmission),
          incorrectAttempts: 0,
          submittedAnswer: userSubmission?.answer,
          submissionTime: userSubmission?.submissionTime,
          isCorrect: userSubmission?.isCorrect,
        };

        return acc;
      },
      {}
    );
  }

  private initializePlayers(players: any[]): Record<string, GroupPlayPlayer> {
    return _reduce(
      players,
      (acc, player) => {
        const { userId, userName, score = 0 } = player;
        acc[userId] = {
          userId,
          userName: userName || `Player ${userId.slice(-4)}`,
          score,
          correctAnswers: 0,
          incorrectAnswers: 0,
          isOnline: true,
          lastActivity: Date.now(),
        };
        return acc;
      },
      {}
    );
  }

  private calculateQuestionStartTime(
    gameStartTime: number,
    questionIndex: number,
    config: GroupPlayGameConfig
  ): number {
    const cycleTime = (config.maxTimePerQuestion * 1000) + WAITING_TIME;
    return gameStartTime + (questionIndex * cycleTime);
  }

  // Game State Update Methods
  updateGamePhase(gameState: GroupPlayGameState): GroupPlayGameState {
    const newPhase = this.calculateCurrentPhase(gameState);
    
    if (!_isEqual(newPhase, gameState.currentPhase)) {
      groupPlayEventManager.emit(GROUP_PLAY_EVENTS.PHASE_CHANGED, {
        previousPhase: gameState.currentPhase,
        newPhase,
      }, gameState.gameId);

      return {
        ...gameState,
        currentPhase: newPhase,
      };
    }

    return gameState;
  }

  updateCurrentQuestion(gameState: GroupPlayGameState): GroupPlayGameState {
    const newQuestionIndex = this.calculateCurrentQuestionIndex(gameState);
    const newQuestionId = _get(gameState.allQuestions[newQuestionIndex], ['question', 'id'], null);

    if (newQuestionId !== gameState.currentQuestionId) {
      const newQuestionStartTime = this.calculateQuestionStartTime(
        gameState.startTime,
        newQuestionIndex,
        gameState.config
      );

      groupPlayEventManager.emit(GROUP_PLAY_EVENTS.QUESTION_STARTED, {
        questionId: newQuestionId,
        questionIndex: newQuestionIndex,
        startTime: newQuestionStartTime,
      }, gameState.gameId);

      return {
        ...gameState,
        currentQuestionId: newQuestionId,
        currentQuestionIndex: newQuestionIndex,
        questionStartTime: newQuestionStartTime,
      };
    }

    return gameState;
  }

  // Utility Methods
  getTimeRemainingInQuestion(gameState: GroupPlayGameState): number {
    const { currentPhase } = gameState;
    const currentTime = getCurrentTimeWithOffset();
    
    if (currentPhase.type === 'QUESTION') {
      return Math.max(0, currentPhase.endTime - currentTime);
    }
    
    return 0;
  }

  getTimeRemainingInPhase(gameState: GroupPlayGameState): number {
    const { currentPhase } = gameState;
    const currentTime = getCurrentTimeWithOffset();
    return Math.max(0, currentPhase.endTime - currentTime);
  }

  isQuestionPhase(gameState: GroupPlayGameState): boolean {
    return gameState.currentPhase.type === 'QUESTION';
  }

  isWaitingPhase(gameState: GroupPlayGameState): boolean {
    return gameState.currentPhase.type === 'WAITING';
  }

  getCurrentQuestion(gameState: GroupPlayGameState): GroupPlayQuestion | null {
    if (!gameState.currentQuestionId) return null;
    return gameState.questions[gameState.currentQuestionId] || null;
  }
}

export default GroupPlayGameService;
