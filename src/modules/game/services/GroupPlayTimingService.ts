import _toNumber from 'lodash/toNumber';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import {
  GroupPlayGameState,
  GroupPlayTimingServiceInterface,
} from '@/src/store/useGroupPlayStore/types';
import { groupPlayEventManager, GROUP_PLAY_EVENTS } from '@/src/store/useGroupPlayStore/events';

class GroupPlayTimingService implements GroupPlayTimingServiceInterface {
  private static instance: GroupPlayTimingService;
  private serverTimeOffset: number = 0;
  private lastSyncTime: number = 0;
  private syncInterval: NodeJS.Timeout | null = null;
  private timers: Map<string, NodeJS.Timeout> = new Map();
  private readonly SYNC_INTERVAL = 30000; // 30 seconds
  private readonly MAX_TIME_DRIFT = 1000; // 1 second

  constructor() {
    if (GroupPlayTimingService.instance) {
      return GroupPlayTimingService.instance;
    }
    GroupPlayTimingService.instance = this;
    this.startPeriodicSync();
  }

  getCurrentServerTime(): number {
    return Date.now() + this.serverTimeOffset;
  }

  syncWithServer(serverTime: number): void {
    const clientTime = Date.now();
    const newOffset = serverTime - clientTime;
    
    // Only update if the drift is significant
    if (Math.abs(newOffset - this.serverTimeOffset) > this.MAX_TIME_DRIFT) {
      this.serverTimeOffset = newOffset;
      this.lastSyncTime = clientTime;
      
      // Update global time function
      if (typeof global !== 'undefined') {
        global.getCurrentTime = () => this.getCurrentServerTime();
      }
      if (typeof window !== 'undefined') {
        (window as any).getCurrentTime = () => this.getCurrentServerTime();
      }

      groupPlayEventManager.emit(GROUP_PLAY_EVENTS.TIME_SYNC, {
        serverTime,
        clientTime,
        offset: this.serverTimeOffset,
        drift: Math.abs(newOffset - this.serverTimeOffset),
      });
    }
  }

  calculateTimeRemaining(gameState: GroupPlayGameState): number {
    const currentTime = this.getCurrentServerTime();
    const gameEndTime = gameState.startTime + (gameState.config.timeLimit * 1000);
    return Math.max(0, gameEndTime - currentTime);
  }

  calculateQuestionTimeRemaining(gameState: GroupPlayGameState): number {
    if (gameState.currentPhase.type !== 'QUESTION') {
      return 0;
    }

    const currentTime = this.getCurrentServerTime();
    return Math.max(0, gameState.currentPhase.endTime - currentTime);
  }

  calculatePhaseTimeRemaining(gameState: GroupPlayGameState): number {
    const currentTime = this.getCurrentServerTime();
    return Math.max(0, gameState.currentPhase.endTime - currentTime);
  }

  calculateCycleProgress(gameState: GroupPlayGameState): {
    currentCycle: number;
    cycleProgress: number;
    totalCycles: number;
    timeInCurrentCycle: number;
    cycleStartTime: number;
  } {
    const currentTime = this.getCurrentServerTime();
    const timeSinceStart = currentTime - gameState.startTime;
    const cycleTime = (gameState.config.maxTimePerQuestion * 1000) + (gameState.config.waitingTime * 1000);
    
    const currentCycle = Math.floor(timeSinceStart / cycleTime);
    const timeInCurrentCycle = timeSinceStart % cycleTime;
    const cycleProgress = (timeInCurrentCycle / cycleTime) * 100;
    const totalCycles = Math.ceil((gameState.config.timeLimit * 1000) / cycleTime);
    const cycleStartTime = gameState.startTime + (currentCycle * cycleTime);

    return {
      currentCycle,
      cycleProgress,
      totalCycles,
      timeInCurrentCycle,
      cycleStartTime,
    };
  }

  // Timer Management
  createTimer(
    id: string,
    duration: number,
    callback: () => void,
    gameId?: string
  ): void {
    this.clearTimer(id);
    
    const timer = setTimeout(() => {
      callback();
      this.timers.delete(id);
      
      if (gameId) {
        groupPlayEventManager.emit(GROUP_PLAY_EVENTS.TIMER_TICK, {
          timerId: id,
          completed: true,
        }, gameId);
      }
    }, duration);

    this.timers.set(id, timer);
  }

  clearTimer(id: string): void {
    const timer = this.timers.get(id);
    if (timer) {
      clearTimeout(timer);
      this.timers.delete(id);
    }
  }

  clearAllTimers(): void {
    this.timers.forEach((timer) => clearTimeout(timer));
    this.timers.clear();
  }

  // Phase Timing
  schedulePhaseTransition(
    gameState: GroupPlayGameState,
    onTransition: (newPhase: string) => void
  ): void {
    const timeRemaining = this.calculatePhaseTimeRemaining(gameState);
    
    if (timeRemaining > 0) {
      this.createTimer(
        `phase-transition-${gameState.gameId}`,
        timeRemaining,
        () => {
          const nextPhase = this.getNextPhaseType(gameState.currentPhase.type);
          onTransition(nextPhase);
        },
        gameState.gameId
      );
    }
  }

  scheduleQuestionTimeout(
    gameState: GroupPlayGameState,
    onTimeout: () => void
  ): void {
    const timeRemaining = this.calculateQuestionTimeRemaining(gameState);
    
    if (timeRemaining > 0) {
      this.createTimer(
        `question-timeout-${gameState.gameId}`,
        timeRemaining,
        () => {
          groupPlayEventManager.emit(GROUP_PLAY_EVENTS.ANSWER_TIMEOUT, {
            questionId: gameState.currentQuestionId,
            timeRemaining: 0,
          }, gameState.gameId);
          onTimeout();
        },
        gameState.gameId
      );
    }
  }

  // Game Timing Utilities
  isGameStarted(gameState: GroupPlayGameState): boolean {
    return this.getCurrentServerTime() >= gameState.startTime;
  }

  isGameEnded(gameState: GroupPlayGameState): boolean {
    if (gameState.endTime) {
      return this.getCurrentServerTime() >= gameState.endTime;
    }
    
    const gameEndTime = gameState.startTime + (gameState.config.timeLimit * 1000);
    return this.getCurrentServerTime() >= gameEndTime;
  }

  getGameProgress(gameState: GroupPlayGameState): {
    percentage: number;
    timeElapsed: number;
    timeRemaining: number;
    totalTime: number;
  } {
    const currentTime = this.getCurrentServerTime();
    const totalTime = gameState.config.timeLimit * 1000;
    const timeElapsed = Math.max(0, currentTime - gameState.startTime);
    const timeRemaining = Math.max(0, totalTime - timeElapsed);
    const percentage = totalTime > 0 ? (timeElapsed / totalTime) * 100 : 0;

    return {
      percentage: Math.min(100, percentage),
      timeElapsed,
      timeRemaining,
      totalTime,
    };
  }

  // Synchronization utilities
  private startPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      // Emit sync request event
      groupPlayEventManager.emit(GROUP_PLAY_EVENTS.TIME_SYNC, {
        requestSync: true,
        lastSyncTime: this.lastSyncTime,
        currentOffset: this.serverTimeOffset,
      });
    }, this.SYNC_INTERVAL);
  }

  stopPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  // Time formatting utilities
  formatTime(milliseconds: number): string {
    const seconds = Math.ceil(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes > 0) {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${remainingSeconds}`;
  }

  formatTimeDetailed(milliseconds: number): {
    minutes: number;
    seconds: number;
    totalSeconds: number;
    formatted: string;
  } {
    const totalSeconds = Math.ceil(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    const formatted = this.formatTime(milliseconds);

    return {
      minutes,
      seconds,
      totalSeconds,
      formatted,
    };
  }

  // Performance monitoring
  measureLatency(startTime: number): number {
    return this.getCurrentServerTime() - startTime;
  }

  getTimingStats(): {
    serverTimeOffset: number;
    lastSyncTime: number;
    activeTimers: number;
    syncAge: number;
  } {
    return {
      serverTimeOffset: this.serverTimeOffset,
      lastSyncTime: this.lastSyncTime,
      activeTimers: this.timers.size,
      syncAge: Date.now() - this.lastSyncTime,
    };
  }

  // Private helper methods
  private getNextPhaseType(currentPhase: string): string {
    switch (currentPhase) {
      case 'INITIAL_WAITING':
        return 'QUESTION';
      case 'QUESTION':
        return 'WAITING';
      case 'WAITING':
        return 'QUESTION';
      case 'RESULTS':
        return 'ENDED';
      default:
        return 'QUESTION';
    }
  }

  // Cleanup
  cleanup(): void {
    this.stopPeriodicSync();
    this.clearAllTimers();
  }

  // Static methods for global access
  static getInstance(): GroupPlayTimingService {
    if (!GroupPlayTimingService.instance) {
      new GroupPlayTimingService();
    }
    return GroupPlayTimingService.instance;
  }
}

export default GroupPlayTimingService;
