import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import { decryptJsonData } from 'core/utils/encryptions/decrypt';
import {
  GroupPlayEventServiceInterface,
  GroupPlayEvent,
  GroupPlayGameState,
} from '@/src/store/useGroupPlayStore/types';
import { 
  groupPlayEventManager, 
  GROUP_PLAY_EVENTS,
  GroupPlayEventType,
  emitQuestionEvent,
  emitPlayerEvent,
  emitPhaseEvent,
  emitAnswerEvent,
} from '@/src/store/useGroupPlayStore/events';

interface WebSocketEvent {
  type: string;
  channel: string;
  data: any;
  game?: any;
  event?: string;
  timestamp?: number;
}

class GroupPlayEventService implements GroupPlayEventServiceInterface {
  private static instance: GroupPlayEventService;
  private eventProcessingQueue: WebSocketEvent[] = [];
  private isProcessingQueue = false;
  private gameStateRef: GroupPlayGameState | null = null;

  constructor() {
    if (GroupPlayEventService.instance) {
      return GroupPlayEventService.instance;
    }
    GroupPlayEventService.instance = this;
  }

  // Event Emitter Interface
  emit(eventType: string, data: any): void {
    groupPlayEventManager.emit(eventType as GroupPlayEventType, data);
  }

  on(eventType: string, handler: (data: any) => void): void {
    groupPlayEventManager.on(eventType as GroupPlayEventType, handler);
  }

  off(eventType: string, handler: (data: any) => void): void {
    groupPlayEventManager.off(eventType as GroupPlayEventType, handler);
  }

  // WebSocket Event Processing
  processWebSocketEvent(event: WebSocketEvent): void {
    try {
      // Add to processing queue
      this.eventProcessingQueue.push(event);
      
      // Process queue if not already processing
      if (!this.isProcessingQueue) {
        this.processEventQueue();
      }
    } catch (error) {
      console.error('Error processing WebSocket event:', error);
      this.emit(GROUP_PLAY_EVENTS.CONNECTION_ERROR, {
        error: error.message,
        event,
      });
    }
  }

  private async processEventQueue(): Promise<void> {
    if (this.isProcessingQueue || this.eventProcessingQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    try {
      while (this.eventProcessingQueue.length > 0) {
        const event = this.eventProcessingQueue.shift();
        if (event) {
          await this.processIndividualEvent(event);
        }
      }
    } catch (error) {
      console.error('Error processing event queue:', error);
    } finally {
      this.isProcessingQueue = false;
    }
  }

  private async processIndividualEvent(wsEvent: WebSocketEvent): Promise<void> {
    const { type, data, game, event: eventType } = wsEvent;

    // Decrypt data if needed
    let processedData = data;
    if (typeof data === 'string') {
      try {
        processedData = decryptJsonData(data);
      } catch (error) {
        // Data might not be encrypted
        processedData = data;
      }
    }

    // Process based on event type
    switch (eventType || type) {
      case 'GAME_STARTED':
        this.handleGameStarted(processedData, game);
        break;
        
      case 'GAME_ENDED':
        this.handleGameEnded(processedData, game);
        break;
        
      case 'CORRECT_MOVE_MADE':
        this.handleCorrectAnswer(processedData, game);
        break;
        
      case 'INCORRECT_MOVE_MADE':
        this.handleIncorrectAnswer(processedData, game);
        break;
        
      case 'USER_JOINED':
        this.handleUserJoined(processedData, game);
        break;
        
      case 'PLAYER_REMOVED':
        this.handlePlayerRemoved(processedData, game);
        break;
        
      case 'LEADERBOARD_UPDATED':
        this.handleLeaderboardUpdated(processedData, game);
        break;
        
      case 'QUESTION_CHANGED':
        this.handleQuestionChanged(processedData, game);
        break;
        
      case 'PHASE_CHANGED':
        this.handlePhaseChanged(processedData, game);
        break;
        
      case 'TIME_SYNC':
        this.handleTimeSync(processedData, game);
        break;
        
      default:
        // Handle generic game update
        this.handleGenericGameUpdate(processedData, game, eventType || type);
        break;
    }

    // Emit raw WebSocket event for debugging
    this.emit(GROUP_PLAY_EVENTS.WS_MESSAGE_RECEIVED, {
      originalEvent: wsEvent,
      processedData,
      timestamp: Date.now(),
    });
  }

  // Specific Event Handlers
  private handleGameStarted(data: any, game: any): void {
    const gameId = _get(game, '_id') || _get(data, 'gameId');
    
    this.emit(GROUP_PLAY_EVENTS.GAME_STARTED, {
      gameId,
      startTime: _get(game, 'startTime') || _get(data, 'startTime'),
      game,
      data,
    });
  }

  private handleGameEnded(data: any, game: any): void {
    const gameId = _get(game, '_id') || _get(data, 'gameId');
    
    this.emit(GROUP_PLAY_EVENTS.GAME_ENDED, {
      gameId,
      endTime: _get(game, 'endTime') || _get(data, 'endTime'),
      finalLeaderboard: _get(game, 'leaderBoard') || _get(data, 'leaderboard'),
      game,
      data,
    });
  }

  private handleCorrectAnswer(data: any, game: any): void {
    const gameId = _get(game, '_id') || _get(data, 'gameId');
    const userId = _get(data, 'userId');
    const questionId = _get(data, 'questionId');
    const score = _get(data, 'score', 0);

    emitAnswerEvent('CORRECT', questionId, gameId, userId, {
      score,
      submissionTime: _get(data, 'submissionTime'),
      answer: _get(data, 'answer'),
      timeTaken: _get(data, 'timeTaken'),
    });

    // Also emit player score update
    emitPlayerEvent('SCORE_UPDATED', userId, gameId, {
      newScore: score,
      questionId,
    });
  }

  private handleIncorrectAnswer(data: any, game: any): void {
    const gameId = _get(game, '_id') || _get(data, 'gameId');
    const userId = _get(data, 'userId');
    const questionId = _get(data, 'questionId');

    emitAnswerEvent('INCORRECT', questionId, gameId, userId, {
      submissionTime: _get(data, 'submissionTime'),
      answer: _get(data, 'answer'),
      timeTaken: _get(data, 'timeTaken'),
      incorrectAttempts: _get(data, 'incorrectAttempts', 1),
    });
  }

  private handleUserJoined(data: any, game: any): void {
    const gameId = _get(game, '_id') || _get(data, 'gameId');
    const userId = _get(data, 'userId');
    const userName = _get(data, 'userName') || _get(data, 'user.userName');

    emitPlayerEvent('JOINED', userId, gameId, {
      userName,
      joinTime: Date.now(),
      playerData: data,
    });
  }

  private handlePlayerRemoved(data: any, game: any): void {
    const gameId = _get(game, '_id') || _get(data, 'gameId');
    const userId = _get(data, 'userId');

    emitPlayerEvent('LEFT', userId, gameId, {
      reason: _get(data, 'reason', 'removed'),
      leaveTime: Date.now(),
    });
  }

  private handleLeaderboardUpdated(data: any, game: any): void {
    const gameId = _get(game, '_id') || _get(data, 'gameId');
    const leaderboard = _get(game, 'leaderBoard') || _get(data, 'leaderboard');

    this.emit(GROUP_PLAY_EVENTS.LEADERBOARD_UPDATED, {
      gameId,
      leaderboard,
      timestamp: Date.now(),
    });
  }

  private handleQuestionChanged(data: any, game: any): void {
    const gameId = _get(game, '_id') || _get(data, 'gameId');
    const questionId = _get(data, 'questionId');
    const questionIndex = _get(data, 'questionIndex');

    emitQuestionEvent('STARTED', questionId, gameId, {
      questionIndex,
      startTime: Date.now(),
      questionData: data,
    });
  }

  private handlePhaseChanged(data: any, game: any): void {
    const gameId = _get(game, '_id') || _get(data, 'gameId');
    const newPhase = _get(data, 'phase');
    const phaseStartTime = _get(data, 'startTime', Date.now());

    emitPhaseEvent('CHANGED', gameId, {
      newPhase,
      phaseStartTime,
      previousPhase: _get(data, 'previousPhase'),
      phaseData: data,
    });
  }

  private handleTimeSync(data: any, game: any): void {
    const serverTime = _get(data, 'serverTime');
    const gameId = _get(game, '_id') || _get(data, 'gameId');

    if (serverTime) {
      this.emit(GROUP_PLAY_EVENTS.TIME_SYNC, {
        serverTime,
        clientTime: Date.now(),
        gameId,
        syncData: data,
      });
    }
  }

  private handleGenericGameUpdate(data: any, game: any, eventType: string): void {
    const gameId = _get(game, '_id') || _get(data, 'gameId');

    // Emit a generic update event
    this.emit('GAME_UPDATE', {
      gameId,
      eventType,
      updateData: data,
      gameData: game,
      timestamp: Date.now(),
    });
  }

  // Utility Methods
  setGameStateReference(gameState: GroupPlayGameState): void {
    this.gameStateRef = gameState;
  }

  clearEventQueue(): void {
    this.eventProcessingQueue = [];
    this.isProcessingQueue = false;
  }

  getQueueSize(): number {
    return this.eventProcessingQueue.length;
  }

  // High-level event emission helpers
  emitGameLifecycleEvent(
    type: 'INITIALIZED' | 'STARTED' | 'ENDED' | 'ABORTED',
    gameId: string,
    data: any
  ): void {
    const eventType = `GAME_${type}` as GroupPlayEventType;
    this.emit(eventType, { gameId, ...data });
  }

  emitQuestionLifecycleEvent(
    type: 'STARTED' | 'ENDED' | 'PRELOADED',
    questionId: string,
    gameId: string,
    data: any
  ): void {
    emitQuestionEvent(type, questionId, gameId, data);
  }

  emitPlayerLifecycleEvent(
    type: 'JOINED' | 'LEFT' | 'SCORE_UPDATED' | 'STATUS_CHANGED',
    userId: string,
    gameId: string,
    data: any
  ): void {
    emitPlayerEvent(type, userId, gameId, data);
  }

  emitAnswerLifecycleEvent(
    type: 'SUBMITTED' | 'CORRECT' | 'INCORRECT' | 'TIMEOUT',
    questionId: string,
    gameId: string,
    userId: string,
    data: any
  ): void {
    emitAnswerEvent(type, questionId, gameId, userId, data);
  }

  // Cleanup
  cleanup(): void {
    this.clearEventQueue();
    this.gameStateRef = null;
    groupPlayEventManager.removeAllListeners();
  }

  // Static methods
  static getInstance(): GroupPlayEventService {
    if (!GroupPlayEventService.instance) {
      new GroupPlayEventService();
    }
    return GroupPlayEventService.instance;
  }
}

export default GroupPlayEventService;
