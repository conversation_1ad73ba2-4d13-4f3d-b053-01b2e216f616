import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import _cloneDeep from 'lodash/cloneDeep';
import {
  GroupPlayGameState,
  GroupPlayQuestion,
  GroupPlayQuestionServiceInterface,
} from '@/src/store/useGroupPlayStore/types';
import { groupPlayEventManager, GROUP_PLAY_EVENTS } from '@/src/store/useGroupPlayStore/events';

class GroupPlayQuestionService implements GroupPlayQuestionServiceInterface {
  private static instance: GroupPlayQuestionService;
  private preloadCache: Map<string, any> = new Map();

  constructor() {
    if (GroupPlayQuestionService.instance) {
      return GroupPlayQuestionService.instance;
    }
    GroupPlayQuestionService.instance = this;
  }

  loadQuestion(questionId: string, gameState: GroupPlayGameState): GroupPlayQuestion | null {
    if (!questionId || !gameState.questions[questionId]) {
      return null;
    }

    const question = gameState.questions[questionId];
    
    // Check if question is already loaded
    if (question.question) {
      return question;
    }

    // Try to load from preload cache
    const cachedQuestion = this.preloadCache.get(questionId);
    if (cachedQuestion) {
      const loadedQuestion: GroupPlayQuestion = {
        ...question,
        question: cachedQuestion,
      };

      groupPlayEventManager.emit(GROUP_PLAY_EVENTS.QUESTION_STARTED, {
        questionId,
        question: loadedQuestion,
        fromCache: true,
      }, gameState.gameId);

      return loadedQuestion;
    }

    // Load from allQuestions array
    const questionData = gameState.allQuestions.find(
      (q) => _get(q, ['question', 'id']) === questionId
    );

    if (questionData) {
      const loadedQuestion: GroupPlayQuestion = {
        ...question,
        question: questionData.question,
        answers: questionData.question.answers || [],
        maxTimeLimit: _get(questionData.question, 'maxTimeLimit', 10) * 1000,
      };

      groupPlayEventManager.emit(GROUP_PLAY_EVENTS.QUESTION_STARTED, {
        questionId,
        question: loadedQuestion,
        fromCache: false,
      }, gameState.gameId);

      return loadedQuestion;
    }

    return null;
  }

  preloadQuestion(questionIndex: number, gameState: GroupPlayGameState): void {
    if (questionIndex < 0 || questionIndex >= gameState.allQuestions.length) {
      return;
    }

    const questionData = gameState.allQuestions[questionIndex];
    const questionId = _get(questionData, ['question', 'id']);

    if (!questionId || this.preloadCache.has(questionId)) {
      return;
    }

    // Preload the question data
    this.preloadCache.set(questionId, _cloneDeep(questionData.question));

    groupPlayEventManager.emit(GROUP_PLAY_EVENTS.QUESTION_PRELOADED, {
      questionId,
      questionIndex,
      cacheSize: this.preloadCache.size,
    }, gameState.gameId);
  }

  preloadNextQuestion(gameState: GroupPlayGameState): void {
    const nextQuestionIndex = gameState.currentQuestionIndex + 1;
    this.preloadQuestion(nextQuestionIndex, gameState);
  }

  preloadUpcomingQuestions(gameState: GroupPlayGameState, count: number = 2): void {
    for (let i = 1; i <= count; i++) {
      const questionIndex = gameState.currentQuestionIndex + i;
      this.preloadQuestion(questionIndex, gameState);
    }
  }

  validateAnswer(answer: string, question: GroupPlayQuestion): boolean {
    if (!answer || !question.answers || question.answers.length === 0) {
      return false;
    }

    const normalizedAnswer = this.normalizeAnswer(answer);
    const correctAnswers = question.answers.map(ans => this.normalizeAnswer(ans));

    return correctAnswers.includes(normalizedAnswer);
  }

  calculateScore(question: GroupPlayQuestion, timeTaken: number): number {
    if (!question || timeTaken < 0) {
      return 0;
    }

    const maxTime = question.maxTimeLimit;
    const baseScore = 100;
    
    // Time bonus: faster answers get higher scores
    const timeBonus = Math.max(0, (maxTime - timeTaken) / maxTime * 50);
    
    // Penalty for incorrect attempts
    const attemptPenalty = question.incorrectAttempts * 10;
    
    return Math.max(10, Math.round(baseScore + timeBonus - attemptPenalty));
  }

  processAnswerSubmission(
    questionId: string,
    answer: string,
    gameState: GroupPlayGameState,
    userId: string
  ): {
    isCorrect: boolean;
    score: number;
    timeTaken: number;
    updatedQuestion: GroupPlayQuestion;
  } {
    const question = gameState.questions[questionId];
    if (!question) {
      throw new Error(`Question ${questionId} not found`);
    }

    const submissionTime = Date.now();
    const timeTaken = submissionTime - gameState.questionStartTime;
    const isCorrect = this.validateAnswer(answer, question);
    const score = isCorrect ? this.calculateScore(question, timeTaken) : 0;

    const updatedQuestion: GroupPlayQuestion = {
      ...question,
      submittedAnswer: answer,
      submissionTime,
      isCorrect,
      hasSolved: isCorrect,
      incorrectAttempts: isCorrect ? question.incorrectAttempts : question.incorrectAttempts + 1,
    };

    // Emit answer event
    groupPlayEventManager.emit(
      isCorrect ? GROUP_PLAY_EVENTS.ANSWER_CORRECT : GROUP_PLAY_EVENTS.ANSWER_INCORRECT,
      {
        questionId,
        answer,
        isCorrect,
        score,
        timeTaken,
        userId,
      },
      gameState.gameId,
      userId
    );

    return {
      isCorrect,
      score,
      timeTaken,
      updatedQuestion,
    };
  }

  getQuestionProgress(gameState: GroupPlayGameState): {
    current: number;
    total: number;
    percentage: number;
    solved: number;
    remaining: number;
  } {
    const total = gameState.totalQuestions;
    const current = gameState.currentQuestionIndex + 1;
    const solved = Object.values(gameState.questions).filter(q => q.hasSolved).length;
    const remaining = total - current;
    const percentage = total > 0 ? (current / total) * 100 : 0;

    return {
      current,
      total,
      percentage,
      solved,
      remaining,
    };
  }

  getQuestionStatistics(gameState: GroupPlayGameState): {
    totalQuestions: number;
    answeredQuestions: number;
    correctAnswers: number;
    incorrectAnswers: number;
    averageTime: number;
    accuracy: number;
  } {
    const questions = Object.values(gameState.questions);
    const answeredQuestions = questions.filter(q => q.submittedAnswer);
    const correctAnswers = questions.filter(q => q.isCorrect).length;
    const incorrectAnswers = answeredQuestions.length - correctAnswers;
    
    const totalTime = answeredQuestions.reduce((sum, q) => {
      if (q.submissionTime && gameState.questionStartTime) {
        return sum + (q.submissionTime - gameState.questionStartTime);
      }
      return sum;
    }, 0);
    
    const averageTime = answeredQuestions.length > 0 ? totalTime / answeredQuestions.length : 0;
    const accuracy = answeredQuestions.length > 0 ? (correctAnswers / answeredQuestions.length) * 100 : 0;

    return {
      totalQuestions: questions.length,
      answeredQuestions: answeredQuestions.length,
      correctAnswers,
      incorrectAnswers,
      averageTime,
      accuracy,
    };
  }

  // Question state management
  updateQuestionState(
    questionId: string,
    updates: Partial<GroupPlayQuestion>,
    gameState: GroupPlayGameState
  ): GroupPlayQuestion {
    const currentQuestion = gameState.questions[questionId];
    if (!currentQuestion) {
      throw new Error(`Question ${questionId} not found`);
    }

    const updatedQuestion = {
      ...currentQuestion,
      ...updates,
    };

    return updatedQuestion;
  }

  // Cache management
  clearPreloadCache(gameId?: string): void {
    if (gameId) {
      // Clear cache for specific game (if we track game-specific cache)
      this.preloadCache.clear();
    } else {
      this.preloadCache.clear();
    }
  }

  getCacheSize(): number {
    return this.preloadCache.size;
  }

  getCachedQuestionIds(): string[] {
    return Array.from(this.preloadCache.keys());
  }

  // Utility methods
  private normalizeAnswer(answer: string): string {
    return answer.toString().trim().toLowerCase();
  }

  // Question timing utilities
  isQuestionExpired(question: GroupPlayQuestion, gameState: GroupPlayGameState): boolean {
    if (!gameState.questionStartTime) return false;
    
    const currentTime = Date.now();
    const timeElapsed = currentTime - gameState.questionStartTime;
    return timeElapsed > question.maxTimeLimit;
  }

  getTimeRemainingForQuestion(question: GroupPlayQuestion, gameState: GroupPlayGameState): number {
    if (!gameState.questionStartTime) return question.maxTimeLimit;
    
    const currentTime = Date.now();
    const timeElapsed = currentTime - gameState.questionStartTime;
    return Math.max(0, question.maxTimeLimit - timeElapsed);
  }

  // Question difficulty and categorization
  getQuestionDifficulty(question: GroupPlayQuestion): 'easy' | 'medium' | 'hard' {
    // This could be based on question metadata, user performance, etc.
    const avgTime = question.maxTimeLimit;
    
    if (avgTime <= 5000) return 'easy';
    if (avgTime <= 10000) return 'medium';
    return 'hard';
  }

  // Batch operations
  preloadQuestionBatch(questionIndices: number[], gameState: GroupPlayGameState): void {
    questionIndices.forEach(index => {
      this.preloadQuestion(index, gameState);
    });
  }

  validateAnswerBatch(
    answers: Array<{ questionId: string; answer: string }>,
    gameState: GroupPlayGameState
  ): Array<{ questionId: string; isCorrect: boolean; score: number }> {
    return answers.map(({ questionId, answer }) => {
      const question = gameState.questions[questionId];
      if (!question) {
        return { questionId, isCorrect: false, score: 0 };
      }

      const isCorrect = this.validateAnswer(answer, question);
      const score = isCorrect ? this.calculateScore(question, 0) : 0;

      return { questionId, isCorrect, score };
    });
  }
}

export default GroupPlayQuestionService;
