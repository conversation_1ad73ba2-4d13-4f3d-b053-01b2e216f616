import React, { useCallback, useMemo } from 'react';
import { View, Text, TouchableOpacity, Image, ScrollView, Platform } from 'react-native';

import Ionicons from '@expo/vector-icons/Ionicons';
import dark from 'core/constants/themes/dark';
import Header from 'shared/Header';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import streakIcon from '@/assets/images/3dIcons/streak.png';
import staticCoinsIcon from '@/assets/images/3dIcons/staticCoins.png';
import { getFormattedTimeObject, getFormattedTimeWithMS } from '../../../../core/utils/general';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _toNumber from 'lodash/toNumber';
import { useSession } from 'modules/auth/containers/AuthProvider';
import useDailyChallengeResultPageStyles from './DailyChallengeResultPage.style';
import { ICON_TYPES } from '@/src/components/atoms/Icon';
import DailyChallengeSharableCard from '../../components/DailyChallengeSharableCard/DailyChallengeSharableCard';
import { openShareableCardFlow } from '@/src/components/shared/ShareResultModal';
import WebBackButton from '@/src/components/shared/WebBackButton';
import { showPopover } from '@/src/components/molecules/Popover/Popover';
import { getDailyChallengeResultLabel } from '../../utils/getRandomResultLabel';
import useDailyChallengeEventTrigger from '../../hooks/useDailyChallengeEventTrigger';

const DEFAULT_SCORE = 60 * 60 * 1000;

const DailyChallengeResultPage = (props) => {
  const { user } = useSession();
  const styles = useDailyChallengeResultPageStyles();
  const isWeb = Platform.OS === 'web';

  const { dailyChallenge, userResult = EMPTY_OBJECT } = props;
  const { stats: dailyChallengeStats } = dailyChallenge;
  const { result: dailyChallengeResult, stats: userDivisionStats } = userResult;

  const completedAt = _get(dailyChallengeResult, 'completedAt');
  useDailyChallengeEventTrigger(completedAt);
  const statikCoinsEarned = _get(dailyChallengeResult, 'statikCoinsEarned', 0);

  const userStatikCoins = _get(user, 'statikCoins', 0);

  const userTotalTime = _toNumber(
    _get(dailyChallengeResult, ['score'], DEFAULT_SCORE),
  );
  const formattedScore = getFormattedTimeWithMS(userTotalTime, true);

  const timeObject = getFormattedTimeObject(userTotalTime);

  const todaysAverage = _toNumber(_get(dailyChallengeStats, 'averageTime'));
  const showTodaysAverage = userTotalTime <= todaysAverage;

  const currentStreak = _get(userDivisionStats, ['streaks', 'current']);
  const highestStreak = _get(userDivisionStats, ['streaks', 'highest']);

  const division = _get(dailyChallenge, 'division', 'OPEN');
  const { hours, minutes, seconds } = timeObject;
  const timeSpentString = `${hours > 0 ? `${hours} hours ` : ''}${minutes > 0 ? `${minutes} minutes ` : ''}${seconds} seconds`;
  const label = getDailyChallengeResultLabel(division, timeSpentString)

  const challengeStatsToShow = useMemo(() => {
    const stats = [];
    if (dailyChallengeStats?.totalSubmission >= 0) {
      stats.push({
        label: 'Total Played',
        value: dailyChallengeStats?.totalSubmission,
      });
    }
    if (userDivisionStats.bestTime >= 0) {
      const formattedBestTime = getFormattedTimeWithMS(
        userDivisionStats.bestTime,
      );
      stats.push({ label: 'Your Best', value: formattedBestTime });
    }
    if (userDivisionStats.averageTime >= 0) {
      const formattedAvgTime = getFormattedTimeWithMS(
        userDivisionStats.averageTime,
      );
      stats.push({ label: 'Your Avg', value: formattedAvgTime });
    }
    return stats;
  }, [userDivisionStats, dailyChallengeStats]);

  const renderDailyChallengeStat = useCallback(
    ({ label, value }) => (
      <View style={styles.statBox}>
        <Text style={styles.statNumber}>{value}</Text>
        <Text style={styles.statLabel}>{label}</Text>
      </View>
    ),
    [],
  );

  const renderDailyChallengeResultCard = useCallback(
    () => (
      <View style={{ flexShrink: 1, height: 250, width: 275 }}>
        <DailyChallengeSharableCard
          dailyChallenge={dailyChallenge}
          userResult={userResult}
        />
      </View>
    ),
    [dailyChallenge, userResult],
  );

  const onPressShareButton = useCallback(() => {
    if (isWeb) {
      showPopover({
        content: (
          <DailyChallengeSharableCard
            dailyChallenge={dailyChallenge}
            userResult={userResult}
          />
        ),
        overlayLook: true,
        animationType: 'slide',
      });
      return;
    }
    openShareableCardFlow({
      renderResultCard: renderDailyChallengeResultCard,
      message: label,
      storyBackgroundColors: {
        backgroundBottomColor: dark.colors.red,
        backgroundTopColor: dark.colors.puzzle.share.storyBackgroundColorTop,
      },
    });
  }, [dailyChallenge, userResult, isWeb]);

  const renderTrailingComponent = () => {
    return (
      <InteractiveSecondaryButton
        onPress={onPressShareButton}
        iconConfig={{
          name: 'share',
          type: ICON_TYPES.ENTYPO,
          color: dark.colors.secondaryButtonBorder,
          size: 20,
        }}
        buttonContainerStyle={{ width: 36, height: 36 }}
      />
    );
  };

  return (
    <View style={styles.mainContainer}>
      <Header
        title={'Daily Challenge'}
        renderTrailingComponent={renderTrailingComponent}
      />
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
      >
        <WebBackButton renderTrailingComponent={renderTrailingComponent} />
        <View style={styles.iconContainer}>
          <Ionicons name="rocket" size={48} color={dark.colors.secondary} />
        </View>

        {/* Title */}
        <View style={styles.titleContainer}>
          <Text style={styles.title}>Lightning Fast!</Text>
          <Text style={styles.subtitle}>You Crushed Today's Challenge!</Text>
          <Text style={styles.division}>
            OPEN DAILY CHALLENGE #{dailyChallenge.challengeNumber}
          </Text>
        </View>

        {/* Challenge Time */}
        <View style={styles.timeContainer}>
          <View style={styles.timeText}>
            <Text style={styles.greenText}>{formattedScore}</Text>
            <Text style={styles.timeFormat}>min : sec : ms</Text>
          </View>
          {showTodaysAverage && (
            <Text style={styles.averageText}>
              Today's Avg : {getFormattedTimeWithMS(userTotalTime)}
            </Text>
          )}
        </View>

        {/* Share Button */}
        {/* <TouchableOpacity style={styles.shareButton}>
                    <Text style={styles.shareText}>Share</Text>
                    <FontAwesome5 name="share-alt" size={10} color={dark.colors.textDark} />
                </TouchableOpacity> */}

        {/* Stats Row */}
        <View style={styles.statsContainer}>
          {_map(challengeStatsToShow, renderDailyChallengeStat)}
        </View>

        {/* Footer Row */}
        <View style={styles.footerContainer}>
          <View style={styles.footerBox}>
            <Image source={streakIcon} style={{ height: 36, width: 36 }} />
            <View style={styles.footerText}>
              <Text style={styles.currentStreak}>{currentStreak}D streak</Text>
              <Text style={styles.maxStreak}>{highestStreak}D best streak</Text>
            </View>
          </View>
          <View style={styles.footerBox}>
            <Image source={staticCoinsIcon} style={{ height: 36, width: 36 }} />
            <View style={styles.footerText}>
              <Text style={styles.currentCoins}>+{statikCoinsEarned}</Text>
              <Text style={styles.maxStreak}>
                {userStatikCoins} statik coins
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default React.memo(DailyChallengeResultPage);
