import _isEmpty from 'lodash/isEmpty';
import React, { cloneElement, useEffect } from 'react';

import ErrorView from 'atoms/ErrorView';
import Loading from 'atoms/Loading';
import useGetDailyChallengeById from 'core/graphql/queries/useGetDailyChallengeById';
import DailyChallengeResultPage from './DailyChallengeResultPage';
import useGetUserResultByDailyChallenge from '../../hooks/queries/useGetUserResultByDailyChallengeId';

const DailyChallengeResultPageContainer = (props) => {
  const { dailyChallengeId } = props;

  const { dailyChallenge, loading, error, reFetchDailyChallenge } =
    useGetDailyChallengeById({ dailyChallengeId });
  const {
    loading: userResultLoading,
    error: userResultError,
    result,
  } = useGetUserResultByDailyChallenge({ challengeId: dailyChallengeId });

  useEffect(() => {
    reFetchDailyChallenge?.();
  }, []);

  if (loading || userResultLoading) {
    return <Loading label={'Loading Daily Challenge Result'} />;
  }

  if (
    error ||
    userResultError ||
    _isEmpty(dailyChallenge) ||
    _isEmpty(result)
  ) {
    return (
      <ErrorView
        errorMessage={
          'Something went wrong while fetching daily challenge result'
        }
      />
    );
  }

  const propsToPass = {
    dailyChallenge: dailyChallenge,
    dailyChallengeId: dailyChallengeId,
    userResult: result,
  };

  return <DailyChallengeResultPage {...propsToPass} />;
};

export default React.memo(DailyChallengeResultPageContainer);
