import { StyleSheet } from 'react-native'
import dark from 'core/constants/themes/dark'
import { withOpacity } from '../../../../core/utils/colorUtils'

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    paddingLeft: 8,
    paddingRight: 8,
    paddingVertical: 0,
    // borderWidth: 1,
    // borderColor: dark.colors.tertiary,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    height: 70,
    width: 260,
  },
  hoveredContainer: {
    backgroundColor: withOpacity(dark.colors.secondary, 0.05),
  },
  infoText: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    lineHeight: 12,
    color: dark.colors.textDark,
    maxWidth: 160,
    marginTop: 2,
  },
  titleText: {
    fontSize: 14,
    fontFamily: 'Montserrat-700',
    lineHeight: 13,
    color: 'white',
    letterSpacing: 1,
  },
  imageContainer: {
    backgroundColor: '#323232',
    height: 45,
    width: 45,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
})

export default styles
