import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from '@/src/core/utils/colorUtils';

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    maxHeight: 280,
    maxWidth: 280,
    aspectRatio: 1,
    alignSelf: 'center',
    zIndex: 100,
  },
  container: {
    flex: 1,
    padding: 10,
    paddingTop: 16,
    minHeight: 250,
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'flex-start',
    
  },
  timeTakenContainer: {
    flexDirection: 'row',
    gap: 2,
  },
  finalTimeText: {
    fontSize: 12,
    letterSpacing: 2.5,
    lineHeight: 18,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
  },
  timeTakenText: {
    fontSize: 55,
    lineHeight: 80,
    fontFamily: 'Tanker-500',
    color: 'white',
  },
  timeTakenDot: {
    fontSize: 70,
    lineHeight: 80,
    fontFamily: 'Tanker-500',
    color: dark.colors.textLight,
  },
  crossMathPuzzleNameText: {
    fontSize: 16,
    lineHeight: 18,
    letterSpacing: 0,
    textAlign: 'center',
    color: withOpacity(dark.colors.textLight, 0.8),
    fontFamily: 'Montserrat-800',
  },
  footerText: {
    fontSize: 12,
    lineHeight: 15,
    textAlign: 'center',
    color: withOpacity(dark.colors.textLight, 0.8),
    fontFamily: 'Montserrat-700',
  },
  centerContent: {
    flex: 1,
    padding: 8,
    height: 100,
    gap: 4,
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  matiks: {
    flexDirection: 'row',
    gap: 4,
    marginBottom: 24,
  },
  matiksText: {
    fontFamily: 'Montserrat-700',
    fontSize: 12,
    color: dark.colors.textLight,
    letterSpacing: 3,
  },
  division: {
    fontFamily: 'Montserrat-700',
    fontSize: 10,
    color: withOpacity(dark.colors.textLight, 0.8),
  },
  svgContainer: {
    width: 274,
    height: 274,
    borderRadius: 20,
    overflow: 'hidden',
  },
});

export default styles;
