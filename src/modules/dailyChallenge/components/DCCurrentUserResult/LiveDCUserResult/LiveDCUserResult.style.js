import { StyleSheet } from "react-native";
import dark from "core/constants/themes/dark";

const styles = StyleSheet.create({
    container: {
        maxWidth: '100%',
        flexDirection: "row",
        gap: 8,
        marginVertical: 15
    },
    innerContainer: {
        paddingHorizontal: 8,
        paddingVertical: 6,
        justifyContent: "center",
        flex: 1,
        borderColor: dark.colors.tertiary,
        borderWidth: 1,
        borderRadius: 8,
        // maxWidth:200
    },
    label: {
        fontSize: 12,
        fontFamily: 'Montserrat-500',
        lineHeight: 20,
        color: dark.colors.textDark
    },
    value: {
        fontSize: 16,
        fontFamily: 'Montserrat-700',
        lineHeight: 20,
        color: "white"
    }
})


export default styles