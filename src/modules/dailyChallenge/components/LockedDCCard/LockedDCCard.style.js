import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    borderRadius: 14,
    paddingLeft: 12,
    paddingRight: 16,
    paddingVertical: 10,
    backgroundColor: dark.colors.purpleLight,
    borderWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    width: 260,
    height: 78,
  },
  infoText: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    lineHeight: 12,
    color: dark.colors.textDark,
    maxWidth: 160,
    marginTop: 2,
  },
  imageContainer: {
    backgroundColor: '#323232',
    height: 40,
    width: 40,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    position: 'absolute',
    width: '100%',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  lockedContainer: {
    height: 40,
    // width: 271,
    width: '100%',
    // paddingHorizontal: 14,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 5,
  },
  buttonText: {
    fontSize: 13,
    lineHeight: 19,
    fontFamily: 'Montserrat-500',
    color: dark.colors.secondary,
  },
  titleText: {
    fontFamily: 'Montserrat-700',
    color: dark.colors.purpleLight2,
    fontSize: 16,
  },
  subTitle: {
    fontFamily: 'Montserrat-600',
    fontSize: 10,
    color: dark.colors.textLight,
    opacity: 0.4,
    marginTop: 8,
  },
  date: {
    fontFamily: 'Montserrat-700',
    color: dark.colors.purpleLight2,
    fontSize: 14,
  },
});

export default styles;
