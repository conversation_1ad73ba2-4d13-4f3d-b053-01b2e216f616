import { gql, useQuery } from '@apollo/client';
import _get from 'lodash/get'

const GET_USER_RESULT_QUERY = gql`
    query GetUserResultByDailyChallengeId($challengeId: ID!) {
        getUserResultByDailyChallengeId(challengeId: $challengeId) {
            success
            error
            result {
                result {
                    userId
                    challengeId
                    score
                    completedAt
                    rank
                    statikCoinsEarned
                    resultStatus
                }
                stats {
                    _id
                    userId
                    division
                    totalAttempts
                    totalSubmission
                    averageTime
                    bestTime
                    streaks {
                        current
                        highest
                        lastPlayedDate
                    }
                    averageAccuracy
                }
            }
        }
    }
`;

const useGetUserResultByDailyChallenge = ({ challengeId }) => {
    const { loading, error, data } = useQuery(GET_USER_RESULT_QUERY, {
        variables: {
            challengeId,
            fetchPolicy: 'network-only',
        }
    });

    return {
        loading,
        error,
        ..._get(data, 'getUserResultByDailyChallengeId', EMPTY_OBJECT),
    }
}

export default useGetUserResultByDailyChallenge;