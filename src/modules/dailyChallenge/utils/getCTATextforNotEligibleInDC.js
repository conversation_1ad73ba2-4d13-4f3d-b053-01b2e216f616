import { DAILY_CHALLENGE_DIVISION } from "../../../core/constants/dailyChallenge";

const getCTATextForNotEligibleInDC = ({ isUserCrossedDiv, division, user }) => {
    const { isGuest } = user

    if (isGuest)
        return 'To Participate in these categories Sign-in first'

    if (isUserCrossedDiv)
        return 'You’ve outgrown this level!.';

    if (division === DAILY_CHALLENGE_DIVISION.DIV1)
        return 'Boost your rating to 2000+ and enter DIV 1!'

    if (division === DAILY_CHALLENGE_DIVISION.DIV2)
        return 'Boost your rating to 1200+ and enter DIV 2!'

    return 'Aim High! Keep climbing to compete at this level and join the elite.'
    
}

export default getCTATextForNotEligibleInDC