import _size from 'lodash/size';

const DAILY_CHALLENGE_RESULT_TEMPLATES = [
  '{division} Daily Challenge on Matiks⚡, cleared in {score}. Istg I didn’t cheat',
  'Matiks⚡ {division} Daily Challenge mastered in {score}. Am I unstoppable? Prove me wrong!',
  'I’ve spent more time picking a song than solving this. 😭😭{division} Daily Challenge, done in {score}',
  '{score}. I blinked and {division} Daily Challenge was gone 💀Beat that (I dare you)',
  'Matiks Daily Challenge {division}: {score}. That’s not solving. That’s art. (I’m Picasso lol)',
  'If speed had a name, it’d be my Matiks Daily Challenge {division} score: {score}',
  'It’s not a sprint, it’s a marathon... unless you’re on Matiks. Then it’s {division} Daily Challenge crushed in {score}.'
];

export const getDailyChallengeResultLabel = (
    division: string,
    score: string

) => {
  const randomIndex = Math.floor(
    Math.random() * _size(DAILY_CHALLENGE_RESULT_TEMPLATES),
  );
  const selectedTemplate = DAILY_CHALLENGE_RESULT_TEMPLATES[randomIndex];

  return selectedTemplate
    .replace(/{division}/g, division)
    .replace(/{score}/g, score)
};
