import { useRouter } from 'expo-router';
import React, { useCallback } from 'react';
import UserImage from 'atoms/UserImage';
import { View, Text, TouchableOpacity } from 'react-native';
import Analytics from '../../../../core/analytics';
import { ANALYTICS_EVENTS } from '../../../../core/analytics/const';
import { SearchUserCardProps } from './types';
import styles from './SearchUserCard.style';

const SearchUserCard: React.FC<SearchUserCardProps> = (props) => {
  const router = useRouter();
  const { infoData, onPress, showDivider = true } = props;
  const {
    username,
    rating,
    profileImageUrl,
    _id: searchedUserId,
    name,
  } = infoData ?? EMPTY_OBJECT;

  const navigateToUserProfile = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS
        .CLICKED_ON_USER_PROFILE_FROM_SEARCH_FREIND_LIST,
    );
    router.push(`/profile/${username}/`);
    onPress?.();
  }, [router, onPress, username]);

  return (
    <TouchableOpacity
      style={[styles.container, showDivider && styles.dividerBar]}
      onPress={navigateToUserProfile}
      key={searchedUserId}
    >
      <View style={styles.userInfoWithImage}>
        <UserImage
          style={styles.userImage}
          user={{ profileImageUrl, _id: searchedUserId }}
          rounded={false}
        />
        <View style={styles.userInfo}>
          <Text style={styles.userName} numberOfLines={1}>
            {username}
          </Text>
          <View style={{ flexDirection: 'row' }}>
            {/*<Text style={styles.userRating} numberOfLines={1}>
                            {`${name} `}
                        </Text> */}
            <Text style={styles.userRating} numberOfLines={1}>
              {rating}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default React.memo(SearchUserCard);
