/* eslint-disable react/jsx-props-no-spreading */
import React, { useCallback, useEffect, useState } from 'react';
import { Tab<PERSON><PERSON>, TabView } from 'react-native-tab-view';
import dark from 'core/constants/themes/dark';
import { useLocalSearchParams } from 'expo-router';
import { Dimensions, Text, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import {
  FRIENDS_LIST_TABS,
  FRIENDS_STATUS,
} from '../../constants/friendsListTab';
import useFriendsListTabViewStyles from './FriendsListTabView.style';
import FriendsListView from '../FriendsListView';
import Analytics from '../../../../core/analytics';
import { ANALYTICS_EVENTS } from '../../../../core/analytics/const';

const initialLayout = { width: Dimensions.get('window').width };

const FriendsListTabView = ({ pageName }: { pageName: string }) => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const styles = useFriendsListTabViewStyles();
  const routes = FRIENDS_LIST_TABS;

  const { page } = useLocalSearchParams();

  const [index, setIndex] = useState(page === 'requests' ? 1 : 0);
  const [pendingCount, setPendingCount] = useState(0);

  useEffect(() => {
    if (page === 'requests') {
      setIndex(1);
    }
  }, [page]);

  const onIndexChange = useCallback(
    (updatedIndex: number) => {
      if (index === 0) {
        Analytics.track(
          ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.VIEWED_FRIENDS_TAB,
          { pageName },
        );
        Analytics.track(
          `${pageName}: ${ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICK_ON_FRIENDS_TAB}`,
        );
      } else {
        Analytics.track(
          ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.VIEWED_PENDING_REQUESTS_TAB,
          { pageName },
        );
        Analytics.track(
          `${pageName}: ${ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICK_ON_REQUESTS_TAB}`,
        );
      }
      setIndex(updatedIndex);
    },
    [index, pageName],
  );

  const renderTabBar = (props: any) => (
    <View style={styles.tabBarContainer}>
      <TabBar
        {...props}
        indicatorStyle={styles.indicator}
        style={styles.tabBar}
        tabStyle={[styles.tabStyle, !isCompactMode && styles.expandedTabStyle]}
        labelStyle={styles.label}
        activeColor={dark.colors.secondary}
        inactiveColor={dark.colors.textDark}
        renderLabel={({ route, focused, color }) => (
          <View style={styles.tabBarLabelRow}>
            <Text style={{ ...styles.label, color }}>{route.title}</Text>
            {route.key === FRIENDS_STATUS.PENDING && pendingCount > 0 ? (
              <View style={styles.pendingCount}>
                <Text style={styles.pendingCountText}>{pendingCount}</Text>
              </View>
            ) : null}
          </View>
        )}
      />
    </View>
  );

  const renderScene = useCallback(
    ({ route }: { route: { key: string } }) => (
      <View style={{ marginHorizontal: isCompactMode ? 16 : 0, flex: 1 }}>
        <FriendsListView
          status={route.key}
          key={route.key}
          onPendingCountFetched={
            route.key === FRIENDS_STATUS.PENDING ? setPendingCount : undefined
          }
          pageName={pageName}
        />
      </View>
    ),
    [isCompactMode, setPendingCount],
  );

  return (
    <View style={styles.tabMainContainer}>
      <TabView
        navigationState={{ index, routes }}
        renderScene={renderScene}
        onIndexChange={onIndexChange}
        initialLayout={initialLayout}
        renderTabBar={renderTabBar}
      />
    </View>
  );
};

export default React.memo(FriendsListTabView);
