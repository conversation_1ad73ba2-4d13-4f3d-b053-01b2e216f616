import React, { useCallback, useEffect, useState } from 'react';
import _map from 'lodash/map';
import useFriendsStore from 'store/useFriendsStore/useZustandFriendsStore';
import _isEmpty from 'lodash/isEmpty';
import { View, Text } from 'react-native';
import _size from 'lodash/size';
import { FriendsFragment } from 'store/useFriendsStore/types';
import SearchUserCard from '../SearchUserCard/SearchUserCard';
import styles from './TopRatedFriends.style';

const TopRatedFriends = () => {
  const { getFriends } = useFriendsStore();

  const [topRatedFriends, setTopRatedFriends] = useState<FriendsFragment[]>([]);

  const renderEmptyFriendsCard = useCallback(
    () => (
      <View style={styles.emptyFriendsContainer}>
        <Text style={styles.noFriendsText}>
          Oh no, you don't have any friends
        </Text>
      </View>
    ),
    [],
  );

  useEffect(() => {
    getFriends(1).then((response) => {
      const { data } = response ?? {};
      const { getFriends: friendsObject } = data ?? {};
      const { results, totalResults } = friendsObject ?? {};
      setTopRatedFriends(results);
    });
  }, [getFriends]);

  return (
    <View style={styles.topFriendsContainer}>
      <Text style={styles.topFriendsText}>TOP FRIENDS</Text>
      {_isEmpty(topRatedFriends)
        ? renderEmptyFriendsCard()
        : _map(topRatedFriends, (friend, index) => (
            <SearchUserCard
              infoData={friend.friendInfo}
              showDivider={index !== _size(topRatedFriends) - 1}
            />
          ))}
    </View>
  );
};

export default React.memo(TopRatedFriends);
