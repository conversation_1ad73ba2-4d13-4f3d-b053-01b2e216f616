import useMediaQuery from "core/hooks/useMediaQuery"
import { useMemo } from "react"
import { StyleSheet } from "react-native"
import dark from "../../../../core/constants/themes/dark"

const createStyles = (isCompactMode) => StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor:dark.colors.background
    },
    headerExpanded: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        marginVertical: 24,
        marginHorizontal:16,
    },
    editTextStyle: {
        color: "white",
        fontSize: 17,
        fontFamily: 'Montserrat-500'
    },
})

const useFollowingsPageStyles = () => {
    const { isMobile } = useMediaQuery()

    const styles = useMemo(() => createStyles(isMobile), [isMobile])

    return styles
}

export default useFollowingsPageStyles