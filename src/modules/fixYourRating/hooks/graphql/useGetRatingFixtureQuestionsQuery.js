import { gql, useQuery } from '@apollo/client'

const GET_RATING_FIXTURE_QUESTIONS = gql`
    query GetRatingFixtureQuestions {
        questions: getRatingFixtureQuestions {
            id
            expression
            description
            options
            answers
            questionType
            rating
            maxTimeLimit
            tags
            fastestTimeTaken
        }
    }
`

const useGetRatingFixtureQuestionsQuery = () => {
    const { data, loading, error } = useQuery(GET_RATING_FIXTURE_QUESTIONS)

    return {
        questions: data?.questions || EMPTY_ARRAY,
        loading,
        error,
    }
}

export default useGetRatingFixtureQuestionsQuery
