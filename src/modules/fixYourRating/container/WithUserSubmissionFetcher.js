import _isEmpty from 'lodash/isEmpty'
import { View, Text } from 'react-native'
import React from 'react'
import useGetRatingFixtureSubmission from '../hooks/graphql/useGetRatingFixtureSubmission'
import Loading from 'atoms/Loading'
import ErrorView from 'atoms/ErrorView'

const WithUserSubmissionFetcher = (Component) => {
    const Wrapper = React.forwardRef((props, ref) => {
        const { submission, loading, error, refetch } =
            useGetRatingFixtureSubmission()
        if (loading) {
            return <Loading />
        }
        if (error || _isEmpty(submission)) {
            return (
                <ErrorView
                    errorMessage="An error occurred while fetching the submission."
                    onRetry={refetch}
                />
            )
        }

        return <Component ref={ref} {...props} submission={submission} />
    })

    return Wrapper
}

const WithUserSubmissionFetcherContainer = (WrappedComponent) => {
    const Wrapper = React.forwardRef((props, ref) => {
        const { submission } = props
        const Component = _isEmpty(submission)
            ? WithUserSubmissionFetcher(WrappedComponent)
            : WrappedComponent

        return <Component ref={ref} {...props} />
    })

    return Wrapper
}

export default WithUserSubmissionFetcherContainer
