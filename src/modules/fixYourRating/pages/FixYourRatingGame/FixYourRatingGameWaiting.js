import _isEmpty from 'lodash/isEmpty';
import { Text, View } from 'react-native';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useSession } from 'modules/auth/containers/AuthProvider';

import Loading from 'atoms/Loading';
import ErrorView from 'atoms/ErrorView';
import TertiaryButton from 'atoms/TertiaryButton';
import { Redirect, useRouter } from 'expo-router';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import FixYourRatingGame from './FixYourRatingGame';
import useGetRatingFixtureSubmission from '../../hooks/graphql/useGetRatingFixtureSubmission';
import useGetRatingFixtureQuestionsQuery from '../../hooks/graphql/useGetRatingFixtureQuestionsQuery';

import styles from './FixYourRatingGame.style';
import FixYourRatingResult from '../FixYourRatingResult';

const FixYourRatingGameWaiting = ({ questions }) => {
  const [timer, setTimer] = useState(5);

  const timerIntervalRef = useRef();
  const router = useRouter();

  const onPressLeaveGame = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.LEFT_RATING_FIXTURE_WAITING_PAGE);
    clearInterval(timerIntervalRef.current);
    router.replace('/home');
  }, [router]);

  useEffect(() => {
    Analytics.track(ANALYTICS_EVENTS.VISITED_RATING_FIXTURE_WAITING_PAGE);
    timerIntervalRef.current = setInterval(
      () => setTimer((prevTimer) => Math.max(0, prevTimer - 1)),
      1000,
    );
    return () => clearInterval(timerIntervalRef.current);
  }, []);

  if (timer <= 0) {
    clearInterval(timerIntervalRef.current);
    return <FixYourRatingGame questions={questions} />;
  }

  return (
    <View style={styles.timerContainer}>
      {/* <FixYourRatingHeader/> */}
      <View
        style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center',
          gap: 16,
        }}
      >
        <View style={styles.gameTimerContainer}>
          <MaterialCommunityIcons
            name="timer-outline"
            size={20}
            color="white"
          />
          <Text style={styles.gameTime}>1:00</Text>
        </View>
        <View style={styles.startingTimerContentContainer}>
          <Text style={styles.startingIn}>Starting in</Text>
          <Text style={styles.leaveChallengeLabel}>{timer}</Text>
        </View>
      </View>
      <View style={{ padding: 16 }}>
        <TertiaryButton title="Leave game" onPress={onPressLeaveGame} />
      </View>
    </View>
  );
};

const FixYourRatingGameWaitingContainer = React.forwardRef((props, ref) => {
  const { loading, error, questions } = useGetRatingFixtureQuestionsQuery();
  const {
    loading: submissionLoading,
    error: submissionError,
    submission,
  } = useGetRatingFixtureSubmission();

  const { user } = useSession();
  const { hasFixedRating = false } = user;

  if (hasFixedRating) {
    return <Redirect href="/home" />;
  }

  if (error) {
    return <ErrorView errorMessage="Something Went Wrong" />;
  }

  if (loading || submissionLoading) {
    return <Loading />;
  }

  if (!_isEmpty(submission)) {
    return <FixYourRatingResult submission={submission} />;
  }

  return (
    <FixYourRatingGameWaiting ref={ref} {...props} questions={questions} />
  );
});

export default FixYourRatingGameWaitingContainer;
