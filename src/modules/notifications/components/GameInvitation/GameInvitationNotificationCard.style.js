import { useMemo } from 'react'
import { StyleSheet } from 'react-native'
import useMediaQuery from 'core/hooks/useMediaQuery'
import dark from '../../../../core/constants/themes/dark'

const createStyles = () =>
    StyleSheet.create({
        container: {
            flexDirection: 'row',
            paddingVertical: 16,
            paddingHorizontal: 24,
            gap: 8,
            backgroundColor: dark.colors.gradientBackground,
            borderBottomColor: dark.colors.tertiary,
            borderBottomWidth: 1,
        },
        imageContainer: {
            height: 38,
            width: 38,
            borderRadius: 4,
            overflow: 'hidden',
        },
        buttonText: {
            fontSize: 11,
            fontFamily: 'Montserrat-600',
            lineHeight: 20,
            color: dark.colors.secondary,
        },
        infoText: {
            fontFamily: 'Montserrat-400',
            fontSize: 11,
            lineHeight: 16,
            letterSpacing: 0.15,
        },
        durationText: {
            fontSize: 10,
            lineHeight: 16,
            letterSpacing: 0.15,
            fontFamily: 'Montserrat-500',
            color: 'white',
        },
        trailingColumn: {
            gap: 2,
            flexDirection: 'column',
        },
        boldInfoText: {
            fontFamily: 'Montserrat-500',
        },
        infoColumn: {
            gap: 4,
            paddingVertical: 8,
        },
    })

const useGameInvitationNotificationCardStyles = () => {
    const { isMobile: isCompactMode } = useMediaQuery()

    return useMemo(() => createStyles(isCompactMode), [isCompactMode])
}

export default useGameInvitationNotificationCardStyles
