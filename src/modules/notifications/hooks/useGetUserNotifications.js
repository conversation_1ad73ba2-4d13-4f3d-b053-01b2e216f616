import { useCallback } from 'react'
import { gql, useLazyQuery } from '@apollo/client'

const GET_USER_NOTIFICATION_QUERY = gql`
    query GetUserNotifications($page: Int, $pageSize: Int) {
        getUserNotifications(page: $page, pageSize: $pageSize) {
            notifications {
                id
                userId
                notificationType
                notificationEventType
                title
                body
                data
                isRead
                sentAt
            }
        }
    }
`

const DEFAULT_PAGE_SIZE = 20

const useGetUserNotifications = ({ pageSize = DEFAULT_PAGE_SIZE }) => {
    const [getUserNotificationsQuery, { loading, error, refetch }] =
        useLazyQuery(GET_USER_NOTIFICATION_QUERY, {
            notifyOnNetworkStatusChange: true,
            fetchPolicy: 'cache-and-network',
        })

    const getUserNotifications = useCallback(
        ({ pageNumber }) => {
            if (loading) return

            return getUserNotificationsQuery({
                fetchPolicy: 'cache-and-network',
                variables: {
                    pageNumber: pageNumber,
                    pageSize,
                },
            })
        },
        [getUserNotificationsQuery, pageSize, refetch, loading]
    )

    return {
        loading,
        error,
        getUserNotifications,
    }
}

export default useGetUserNotifications
