import { gql, useMutation } from '@apollo/client'
import { useCallback } from 'react'

const MARK_NOTIFICATION_READ_QUERY = gql`
    mutation MarkNotificationRead($notificationId: ID!) {
        markNotificationRead(notificationId: $notificationId)
    }
`

const useMarkNotificationRead = () => {
    const [markNotificationReadQuery] = useMutation(
        MARK_NOTIFICATION_READ_QUERY
    )

    const markNotificationRead = useCallback(
        ({ notificationId }) => {
            return markNotificationRead({
                variables: {
                    notificationId,
                },
            })
        },
        [markNotificationReadQuery]
    )

    return {
        markNotificationRead,
    }
}

export default useMarkNotificationRead
