import React from 'react';
import { ScrollView, Text, View } from 'react-native';
import AntDesign from '@expo/vector-icons/AntDesign';
import PropTypes from 'prop-types';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import OnlineUserCard from '../../OnlineUserCard';
import dark from 'core/constants/themes/dark';
import useExpandedOnlineUsersSectionStyles from './ExpandedOnlineUsersSection.style';
import OnlineUserShimmerCard from '../../OnlineUserCard/OnlineUserShimmerCard';

const ExpandedOnlineUsersSection = (props) => {
  const { onlineUsers, navigateToOnlineUsersPage, isFetchingOnlineUsers } =
    props ?? EMPTY_OBJECT;

  const styles = useExpandedOnlineUsersSectionStyles();

  const renderOnlineUsersShimmer = () => (
    <ScrollView
      contentContainerStyle={styles.container}
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}
    >
      {_map(Array.from({ length: 10 }), (item, index) => (
        <OnlineUserShimmerCard
          isLastItem={index === 9}
          key={`online-user-shimmer-${index}`}/>
      ))}
    </ScrollView>
  );

  const renderEmptyUsersPlaceholder = () => (
    <Text style={styles.noUsers}>No Mathletes Online </Text>
  );

  const renderOnlineUsers = () => (
    <ScrollView
      contentContainerStyle={styles.container}
      showsVerticalScrollIndicator={false}
      showsHorizontalScrollIndicator={false}
    >
      {_map(onlineUsers, (userData, index) => (
        <OnlineUserCard
          key={`online-user-${index}`}
          user={userData?.userInfo}
          isLastItem={index === onlineUsers.length - 1}
          currActivity={userData?.currActivity}
        />
      ))}
    </ScrollView>
  );

  return (
    <View style={styles.mainContainer}>
      <View style={styles.headerContainer}>
        <Text style={styles.onlinePeopleText}>ONLINE MATHLETES</Text>
        <AntDesign
          name="arrowright"
          size={15}
          color={dark.colors.secondary}
          onPress={navigateToOnlineUsersPage}
        />
      </View>

      {isFetchingOnlineUsers
        ? renderOnlineUsersShimmer()
        : _isEmpty(onlineUsers)
          ? renderEmptyUsersPlaceholder()
          : renderOnlineUsers()}
    </View>
  );
};

ExpandedOnlineUsersSection.propTypes = {
  onlineUsers: PropTypes.array,
  navigateToOnlineUsersPage: PropTypes.func,
  isFetchingOnlineUsers: PropTypes.bool,
};

export default React.memo(ExpandedOnlineUsersSection);
