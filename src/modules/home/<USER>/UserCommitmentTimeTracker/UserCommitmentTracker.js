import React, {useEffect, useState} from 'react';
import {Text, View} from 'react-native';
import useUserActivityTracker from 'core/hooks/useUserActivityTracker';
import _isNaN from 'lodash/isNaN';
import dark from 'core/constants/themes/dark';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _round from 'lodash/round';
import styles from './UserCommitmentTracker.style';

const UserCommitmentTracker = () => {
  const {getCommitmentTime, totalActivityTime, loading, error} = useUserActivityTracker();
  const [commitmentTime, setCommitmentTime] = useState(0);

  const {isMobile: isCompactMode} = useMediaQuery();

  useEffect(() => {
    const fetchCommitmentData = async () => {
      const res = await getCommitmentTime();
      setCommitmentTime(Math.floor(res / 60000));
    };

    fetchCommitmentData();
  }, [totalActivityTime]);

  const remainingMinutes = Math.max(
    commitmentTime - _round(totalActivityTime / 60000, 0),
    0,
  );
  const totalSpentMinute = _round(totalActivityTime / 60000, 0);

  const renderCommitmentHeader = () => (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
    >
      <Text
        style={{
          fontSize: 10,
          fontFamily: 'Montserrat-600',
          color: dark.colors.textDark,
        }}
      >
        YOUR COMMITMENT
      </Text>
      <Text
        style={{fontSize: 10, fontFamily: 'Montserrat-600', color: 'white'}}
      >
        {totalSpentMinute}/{commitmentTime} MIN
      </Text>
    </View>
  );

  const renderCommitmentProgress = () => (
    <View style={styles.container}>
      <View
        style={[
          styles.completedSection,
          {flex: totalSpentMinute},
          remainingMinutes <= 0 && {backgroundColor: dark.colors.secondary},
        ]}
      />
      <View style={[styles.remainingSection, {flex: remainingMinutes}]}/>
    </View>
  );

  const renderCommitmentValue = () => (
    <Text
      style={{
        fontSize: 10,
        fontFamily: 'Montserrat-500',
        color: dark.colors.textDark,
      }}
    >
      <Text style={{color: 'white', fontFamily: 'Montserrat-700'}}>
        {_round((totalSpentMinute / commitmentTime) * 100, 2)}%
      </Text>{' '}
      OF COMMITMENT DONE
    </Text>
  );

  if (
    (remainingMinutes <= 0 && isCompactMode) ||
    _isNaN(commitmentTime) ||
    commitmentTime === 0 || loading || error
  ) {
    return null;
  }

  return (
    <View
      style={isCompactMode ? styles.compactContainer : styles.expandedContainer}
    >
      {renderCommitmentHeader()}
      <View
        style={
          isCompactMode
            ? styles.compactContentContainer
            : styles.expandedContentContainer
        }
      >
        {renderCommitmentProgress()}
        {renderCommitmentValue()}
      </View>
    </View>
  );
};

export default UserCommitmentTracker;
