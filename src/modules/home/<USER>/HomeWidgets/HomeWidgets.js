import React from 'react'
import { View } from 'react-native'
import useMediaQuery from 'core/hooks/useMediaQuery'

import styles from './HomeWidgets.style'
import ExpandedStreakCard from '../../../profile/components/ExpandedStreakCard/ExpandedStreakCard'

import OnlineUsers from '../OnlineUsers'
import UserCommitmentTracker from '../UserCommitmentTimeTracker/UserCommitmentTracker'

const HomeWidgets = (props) => {
  const { isMobile } = useMediaQuery()

  if (isMobile) {
    return null
  }

  return (
    <View style={styles.container}>
      <ExpandedStreakCard />
      <UserCommitmentTracker />
      {/* <DailyChallengeLeaderboardWidget /> */}

      <OnlineUsers />
    </View>
  )
}

HomeWidgets.propTypes = {}

export default React.memo(HomeWidgets)
