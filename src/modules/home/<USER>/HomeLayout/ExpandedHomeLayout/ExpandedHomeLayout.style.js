import { StyleSheet } from 'react-native';

import APP_LAYOUT_CONSTANTS from 'core/constants/appLayout';
import dark from 'core/constants/themes/dark';

export const CENTER_PANE_PADDING = 16;

const styles = StyleSheet.create({
  superContainer: {
    justifyContent: 'flex-start',
    alignItems: 'center',
    gap: 24,
    flex: 1,
  },
  container: {
    height: '100%',
    width: '100%',
    paddingHorizontal: 36,
    gap: 24,
    flexDirection: 'row',
  },
  userStatContainer: {
    width: '100%',
    alignItems: 'center',
  },
  featuredContestsContainer: {
    width: '100%',
    alignItems: 'center',
  },
  contentContainer: {
    justifyContent: 'center',
    maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
    gap: 16,
    width: '100%',
  },
  dcCategory: {
    width: '100%',
    paddingHorizontal: CENTER_PANE_PADDING,
  },
  gameTypesRow: {
    width: '100%',
    paddingHorizontal: 16,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 28,
  },
  changedLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 24,
    borderColor: dark.colors.tertiary,
    borderWidth: 1,
    paddingHorizontal: 60,
    paddingVertical: 42,
    borderRadius: 24,
    marginHorizontal: 15,
  },
  leftPanel: {
    flex: 1,
    zIndex: 1,
    alignItems: 'center',
    // maxWidth: 360,
  },
  rightPanel: {
    flex: 1,
    width: '100%',
    justifyContent: 'flex-start',
    alignItems: 'center',
    borderLeftColor: dark.colors.tertiary,
    borderLeftWidth: 1,
  },
  rightInnerPanel: {
    width: 328,
  },
  textContainer: {
    flexDirection: 'column',
  },
  playNowText: {
    fontSize: 12,
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-600',
    lineHeight: 12,
  },
  text2: {
    fontSize: 28,
    color: '#FFFFFF',
    fontFamily: 'Montserrat-400',
  },
  gameTypesContainer: {
    gap: 16,
    justifyContent: 'space-between',
  },
  gameType: {
    backgroundColor: dark.colors.tertiary,
    height: 73,
    width: 295,
    marginTop: 10,
  },
  inviteFriendButton: {
    marginTop: 32,
    minWidth: 300,
    fontFamily: 'Montserrat-600',
    borderRadius: 20,
  },
});

export default styles;
