import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    gap: 16,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    width: '100%',
    // paddingHorizontal: 16,
    paddingVertical: 8,
  },
  tabContainer: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    flexDirection: 'row',
    gap: 4,
    minHeight: 30,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    height: 18,
    width: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },

  categoryText: {
    fontSize: 10,
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-600',
  },
});

export default styles;
