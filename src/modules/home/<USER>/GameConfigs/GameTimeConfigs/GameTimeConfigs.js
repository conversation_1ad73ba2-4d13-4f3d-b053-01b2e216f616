import React, { useCallback } from 'react';
import _map from 'lodash/map';

import { Platform, View } from 'react-native';
import { Text } from '@rneui/themed';

import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import PropTypes from 'prop-types';
import InteractivePrimaryButton, {
  BUTTON_TYPES,
} from '@/src/components/atoms/InteractivePrimaryButton';
import dark from '@/src/core/constants/themes/dark';
import { GAME_TYPE_DETAILS, GAME_TYPES } from '../../../constants/gameTypes';
import styles from './GameTimeConfigs.style';
import {
  ABILITY_GAME_CONFIG,
  GAME_CONFIGS,
} from '../../../constants/gameConfig';

const GameTimeConfigs = (props) => {
  const { gameConfig: selectedGameConfig, updateGameConfig, gameType } = props;
  const { isMobile } = useMediaQuery();
  const isWebPlatform = Platform.OS === 'web';

  const renderGameConfigOption = useCallback(
    ({ option, gameConfig }) => {
      const { key: configKey } = gameConfig;
      const { key, label } = option;
      const isSelected = selectedGameConfig[configKey] === key;

      return (
        <InteractivePrimaryButton
          onPress={() => updateGameConfig({ key: configKey, value: key })}
          label={label}
          buttonStyle={[
            styles.timeContainer,
            isSelected && { borderColor: dark.colors.secondary },
            isMobile && isWebPlatform && { minWidth: 165 },
          ]}
          type={BUTTON_TYPES.SECONDARY}
          labelStyle={isSelected && styles.buttonLabelStyle}
          radius={8}
        />
      );
    },
    [updateGameConfig, selectedGameConfig],
  );

  const { title, subtitle } = GAME_TYPE_DETAILS[gameType];

  const renderGameConfig = useCallback(
    (gameConfig) => {
      const { key, label, options } = gameConfig;

      return (
        <View key={key} style={styles.gameConfigContainer}>
          {!isMobile && (
            <View style={{ marginBottom: 14, gap: 8 }}>
              <Text style={styles.title}>{title}</Text>
              <Text style={styles.subTitle}>{subtitle}</Text>
            </View>
          )}

          <View style={styles.configOptionsContainer}>
            {_map(options, (option) =>
              renderGameConfigOption({ option, gameConfig }),
            )}
          </View>
        </View>
      );
    },
    [isMobile, title, subtitle, renderGameConfigOption],
  );

  return (
    <View>
      {_map(
        gameType === GAME_TYPES.ABILITY_DUELS ||
          gameType === GAME_TYPES.DMAS_ABILITY
          ? ABILITY_GAME_CONFIG
          : GAME_CONFIGS,
        renderGameConfig,
      )}
    </View>
  );
};

GameTimeConfigs.propTypes = {
  gameType: PropTypes.string,
};

export default GameTimeConfigs;
