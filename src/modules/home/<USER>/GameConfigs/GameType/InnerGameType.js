import React from 'react'
import {Image, View} from 'react-native'
import {Text} from '@rneui/themed'
import {GAME_TYPE_DETAILS} from '../../../constants/gameTypes'
import {icons} from '../../../constants/gameTypeImages'
import styles from './InnerGameType.style'

const InnerGameType = (props) => {
  const {gameType} = props

  const {icon, title, subtitle} = GAME_TYPE_DETAILS[gameType]

  return (
    <View style={styles.gameTypeContainer}>
      <View style={styles.iconContainer}>
        <Image source={icons[icon]} style={styles.icon}/>
      </View>
      <View style={styles.textContainer}>
        <Text style={styles.titleStyle}>{title}</Text>
        <Text style={styles.subtitleStyle}>{subtitle}</Text>
      </View>
    </View>
  )
}

export default React.memo(InnerGameType)
