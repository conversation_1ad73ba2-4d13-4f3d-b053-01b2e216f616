import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { Image, Pressable, Text, View } from 'react-native';
import { useRouter } from 'expo-router';
import Rive from 'atoms/Rive';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';
import styles from './MostPlayedGameTypeCard.style';
import { icons } from '../../constants/gameTypeImages';

const MostPlayedGameTypeCard = (props) => {
  const { colour } = props;
  const router = useRouter();
  const handleOnPress = useCallback(() => {
    router.push('/search?timeLimit=1');
  }, [router]);

  return (
    <Pressable
      style={({ hovered }) => [
        styles.container,
        { backgroundColor: colour },
        hovered && styles.hoveredContainer,
        hovered && { transform: [{ scale: 1.03 }] },
      ]}
      onPress={handleOnPress}
    >
      <Rive
        url={RIVE_ANIMATIONS.PLAY_NOW_ANIMATION}
        autoPlay
        style={{ width: 600, height: 200, marginBottom: 300 }}
      />
      <View style={[styles.infoText]}>
        <Text style={styles.titleText}>1 MIN SPRINT</Text>
        <View style={styles.mostPlayContainer}>
          <Text style={styles.mostPlayedText}>Most Played</Text>
        </View>
      </View>
      <Image source={icons.sprint} style={styles.image} />
    </Pressable>
  );
};

MostPlayedGameTypeCard.propTypes = {
  colour: PropTypes.string,
};

export default React.memo(MostPlayedGameTypeCard);
