import React from 'react';
import { View } from 'react-native';
import ShimmerView from 'molecules/ShimmerView';
import useExpandedOnlineUserShimmerStyles from './ExpandedOnlineUserShimmer.style';

const ExpandedOnlineUserShimmer = ({ isLastItem }) => {
  const styles = useExpandedOnlineUserShimmerStyles();

  return (
    <View style={[styles.container, isLastItem && { borderBottomWidth: 0 }]}>
      <View style={styles.userInfoRow}>
        <View style={styles.imageContainer}>
          <ShimmerView style={styles.userImage} />
        </View>
        <View>
          <ShimmerView style={styles.name} />
          <ShimmerView style={styles.rating} />
        </View>
      </View>
      <View style={styles.userInfoRow}>
        <ShimmerView style={styles.oneMinDuelText} />
      </View>
    </View>
  );
};

export default ExpandedOnlineUserShimmer;
