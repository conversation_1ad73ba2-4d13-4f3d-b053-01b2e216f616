import { useMemo } from 'react';
import { StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import dark from '../../../../../core/constants/themes/dark';

const createStyles = () =>
  StyleSheet.create({
    container: {
      // flex: 1,
      width: '100%',
      justifyContent: 'space-between',
      alignItems: 'center',
      gap: 8,
      flexDirection: 'row',
      paddingVertical: 10,
      borderBottomColor: dark.colors.tertiary,
      borderBottomWidth: 1,
    },
    userImage: {
      height: 32,
      width: 32,
      borderRadius: 50,
      borderWidth: 0.5,
      overflow: 'hidden',
      borderColor: '#363636',
    },
    activityIndicator: {
      height: 12,
      width: 12,
      borderRadius: 6,
      borderWidth: 2,
      borderColor: dark.colors.gradientBackground,
      position: 'absolute',
      bottom: 0,
      right: 0,
      backgroundColor: dark.colors.inGameIndicator,
    },
    imageContainer: {
      height: 34,
      width: 34,
    },
    name: {
      fontFamily: 'Montserrat-500',
      fontSize: 12,
      lineHeight: 16,
      textAlign: 'left',
      color: 'white',
      maxWidth: 150,
    },
    rating: {
      fontFamily: 'Montserrat-600',
      fontSize: 9,
      lineHeight: 11,
      color: dark.colors.textDark,
      letterSpacing: 1,
    },
    bottomSheetStyle: {
      width: '100%',
      bottom: 0,
      borderLeftWidth: 0,
      backgroundColor: dark.colors.background,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      padding: 13,
      position: 'absolute',
    },
    oneMinDuelText: {
      fontFamily: 'Montserrat-600',
      fontSize: 12,
      lineHeight: 20,
      color: dark.colors.secondary,
      letterSpacing: 1,
      textAlign: 'center',
    },
    userInfoRow: {
      flexDirection: 'row',
      justifyContent: 'flex-start',
      alignItems: 'center',
      gap: 10,
    },
    overlayStyle: {
      borderRadius: 8,
      padding: 0,
      position: 'absolute',
      maxHeight: 85,
      maxWidth: 220,
      backgroundColor: dark.colors.primary,
    },
    userActions: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      gap: 10,
    },
  });

const useExpandedOnlineUserCardStyles = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(() => createStyles(isMobile), [isMobile]);

  return styles;
};

export default useExpandedOnlineUserCardStyles;
