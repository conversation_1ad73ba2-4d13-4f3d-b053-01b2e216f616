import React, { useCallback, useRef, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import UserImage from 'atoms/UserImage';
import PropTypes from 'prop-types';
import userReader from 'core/readers/userReader';
import { showPopover } from 'molecules/Popover/Popover';
import { closeRightPane } from 'molecules/RightPane/RightPane';
import USER_ACTIVITY from 'core/constants/userActivityConstants';
import dark from 'core/constants/themes/dark';
import Entypo from '@expo/vector-icons/Entypo';
import { useRouter } from 'expo-router';
import useMediaQuery from 'core/hooks/useMediaQuery';
import useExpandedOnlineUserCardStyles from './ExpandedOnlineUserCard.style';
import OnlineUsersOptionsOverlay from '../../OnlineUsersOptionsOverlay/OnlineUsersOptionsOverlay';
import useChallengeUser from '../../../../friendsAndFollowers/hooks/mutations/useChallengeUser';
import OnlineUserBottomSheet from '../../OnlineUserBottomSheet';

const ExpandedOnlineUserCard = (props) => {
  const { user, currActivity, isLastItem } = props;
  const styles = useExpandedOnlineUserCardStyles();

  const { challengeUser } = useChallengeUser();

  const router = useRouter();

  const { isMobile: isCompactMode } = useMediaQuery();

  const isExploring = currActivity === USER_ACTIVITY.EXPLORING;

  const [isChallengingFriend, setIsChallengingFriend] = useState(false);

  const onPressChallengeOneMinDuel = useCallback(async () => {
    try {
      if (isChallengingFriend || !isExploring) {
        return;
      }
      setIsChallengingFriend(true);
      await challengeUser({ userId: user?._id });
      closeRightPane?.();
      setIsChallengingFriend(false);
    } catch (e) {
      setIsChallengingFriend(false);
    } finally {
      setIsChallengingFriend(false);
    }
  }, [user?._id, challengeUser, isChallengingFriend, isExploring]);

  const cardRef = useRef();

  const onUserProfilePress = useCallback(() => {
    // closePopover?.();
    closeRightPane?.();
    if (!isCompactMode) {
      router.push(`/profile/${userReader.username(user)}/`);
      return;
    }
    showPopover({
      content: (
        <OnlineUserBottomSheet user={user} currActivity={currActivity} />
      ),
      style: styles.bottomSheetStyle,
      overlayLook: true,
      animationType: 'slide',
    });
  }, [user, currActivity, router, isCompactMode]);

  const onPressOptions = useCallback(() => {
    cardRef.current?.measure((x, y, width, height, pageX, pageY) => {
      const style = [styles.overlayStyle, { top: pageY + 16, right: x + 30 }];
      showPopover({
        content: (
          <OnlineUsersOptionsOverlay user={user} isExploring={isExploring} />
        ),
        style,
      });
    });
  }, [user, isExploring]);

  return (
    <View
      style={[styles.container, isLastItem && { borderBottomWidth: 0 }]}
      ref={cardRef}
    >
      <TouchableOpacity
        style={[styles.userInfoRow, { flex: 1 }]}
        onPress={onUserProfilePress}
      >
        <View style={styles.imageContainer}>
          <UserImage user={user} rounded={false} style={styles.userImage} />
          <View
            style={[
              styles.activityIndicator,
              isExploring && { backgroundColor: dark.colors.secondary },
            ]}
          />
        </View>
        <View>
          <Text style={styles.name} numberOfLines={1}>
            {userReader.username(user)}
          </Text>
          <Text style={styles.rating}>{user?.rating}</Text>
        </View>
      </TouchableOpacity>
      <View style={styles.userInfoRow}>
        <TouchableOpacity onPress={onPressChallengeOneMinDuel}>
          <Text
            style={[
              styles.oneMinDuelText,
              (isChallengingFriend || !isExploring) && {
                color: dark.colors.textDark,
              },
            ]}
          >
            1-min
          </Text>
        </TouchableOpacity>
        {!isCompactMode && (
          <Entypo
            name="dots-three-vertical"
            size={12}
            color={dark.colors.textDark}
            onPress={onPressOptions}
          />
        )}
      </View>
    </View>
  );
};

ExpandedOnlineUserCard.propTypes = {
  user: PropTypes.object,
  currActivity: PropTypes.string,
  isLastItem: PropTypes.bool,
};

export default React.memo(ExpandedOnlineUserCard);
