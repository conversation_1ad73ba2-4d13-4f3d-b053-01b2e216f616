import React, { use<PERSON>allback, useMemo, useState} from 'react';
import { Dimensions, View, Image } from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import PropTypes from 'prop-types';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import { BANNER_TYPES } from 'core/constants/bannerTypes';
import _isNil from 'lodash/isNil';
import _map from 'lodash/map';
import FixYourRatingBanner from '../../../fixYourRating/components/FixRatingBanner/FixYourRatingBanner';
import useMediaQuery from '../../../../core/hooks/useMediaQuery';
import dark from '../../../../core/constants/themes/dark';
import styles from './HomeBannerCarousel.style';
import ResolutionBanner from './ResolutionBanner';
import { Platform } from 'react-native';
import ContestBannerLayout from '@/src/modules/contest/components/ContestBanner';
const { width } = Dimensions.get('window');

import ShowdownBannerLayout from '../../../contest/components/ShowdownBanner';

const BANNER_HEIGHT = 125;

const eventProperties = {
  [PAGE_NAME_KEY]: PAGE_NAMES.ARENA_PAGE,
};

const HomeBannerCarousel = (props) => {
  const { bannerData, containerWidth } = props;
  const [activeIndex, setActiveIndex] = useState(0);
  const isWeb = Platform.OS === 'web';

  const { isMobile: isCompactMode } = useMediaQuery();
  const onProgressChange = useCallback(
    (offsetProgress, absoluteProgress) => {
      const currentIndex = Math.round(absoluteProgress);
      if (currentIndex !== activeIndex) {
        setActiveIndex(currentIndex);
        return;
      }
      if (bannerData.length <= 2) {
        setActiveIndex(0);
      }
    },
    [activeIndex, setActiveIndex, bannerData],
  );

  const renderBanner = useCallback(({ item }) => {
    if (_isNil(item)) return null;
    switch (item.type) {
      case BANNER_TYPES.CONTEST_BANNER:
        return (
          <ContestBannerLayout
            contest={item.data}
            eventProperties={eventProperties}
          />
        );
      case BANNER_TYPES.RATING_FIX_BANNER:
        return <FixYourRatingBanner eventProperties={eventProperties} />;
      case BANNER_TYPES.RESOLUTION_BANNER:
        return <ResolutionBanner />;
      case BANNER_TYPES.SHOWDOWN_BANNER:
        return (
          <ShowdownBannerLayout
            showdown={item.data}
            eventProperties={eventProperties}
          />
        );
      default:
        return null;
    }
  }, []);

  const renderBannerContainer = useCallback(
    ({ item }) => (
      <View style={{ paddingHorizontal: isCompactMode ? 0 : 10 }}>{renderBanner({ item })}</View>
    ),
    [renderBanner],
  );

  const carouselWidth = width;
  const carouselWidthWeb = useMemo(
    () => (containerWidth === 0 ? carouselWidth - 510 : containerWidth),
    [containerWidth, carouselWidth],
  );

  const renderCarousel = useCallback(
    () => (
      <Carousel
        loop={bannerData.length > 1}
        width={isCompactMode ? containerWidth || width : carouselWidthWeb}
        height={isCompactMode ? 90 : 125}
        autoPlay={bannerData.length > 1}
        autoPlayInterval={5000}
        data={bannerData}
        scrollAnimationDuration={1000}
        renderItem={renderBannerContainer}
        onProgressChange={onProgressChange}
        snapEnabled={bannerData.length > 1}

      />
    ),
    [carouselWidth, bannerData, renderBanner, carouselWidthWeb],
  );

  const renderDots = useCallback(
    () => (
      <View style={styles.dotsContainer}>
        {_map(bannerData, (_, index) =>
          activeIndex !== index ? (
            <View key={index} style={styles.dot} />
          ) : (
            <View key={index} style={styles.activeDot}>
              <View
                style={[styles.dot, { backgroundColor: dark.colors.textDark }]}
              />
            </View>
          ),
        )}
      </View>
    ),
    [activeIndex, bannerData],
  );

  return (
    <>
      {renderCarousel()}
      {/* {bannerData.length > 1 && renderDots()} */}
    </>
  );
};

HomeBannerCarousel.propTypes = {
  bannerData: PropTypes.array,
  containerWidth: PropTypes.number,
};

export default React.memo(HomeBannerCarousel);
