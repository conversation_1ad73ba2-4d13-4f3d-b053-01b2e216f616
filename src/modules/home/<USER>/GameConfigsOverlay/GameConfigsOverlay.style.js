import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    gap: 10,
    alignItems: 'center',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '90%',
  },
  headerText: {
    fontFamily: 'Montserrat-700',
    fontSize: 15,
    lineHeight: 24,
    color: 'white',
  },
  gameConfigContainer: {
    paddingTop: 5,
    paddingHorizontal: 10,
  },
  configOptionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 12,
    width: '100%',
    paddingHorizontal: 10,
  },
  buttonContainer: {
    width: '60%',
    maxWidth: 100,
  },
  compactButtonContainer: {
    maxWidth: 140,
  },
  timeContainer: {
    backgroundColor: 'transparent',
    borderColor: dark.colors.tertiary,
    borderWidth: 0.6,
    height: 30,
  },
  buttonLabelStyle: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: 'white',
  },
  imageRow: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    height: 68,
    width: 68,
  },
});

export default styles;
