export interface MathMazePuzzleData {
  grid: string[][];
  result: number;
  solutionPath: [number, number][];
}

/**
 * Parses a JSON string representing MathMaze puzzle data.
 * @param puzzleString The JSON string from the backend.
 * @returns A structured MathMazePuzzleData object, or null if parsing fails.
 */
export function parseMathMazePuzzleFromString(
  puzzleString: string | undefined | null,
): MathMazePuzzleData | null {
  if (!puzzleString) {
    console.error('MathMaze puzzle string is empty or undefined.');
    return null;
  }
  try {
    return JSON.parse(puzzleString);
  } catch (error) {
    console.error('Error parsing MathMaze puzzle string:', error, puzzleString);
    return null;
  }
}

/**
 * Processes the puzzle object from the backend, extracting and parsing the MathMaze-specific data.
 * @param mathMazeSpecifics The raw puzzle object from useGetDailyPuzzle, which should contain typeSpecific.mathMaze.puzzleString.
 * @returns A MathMazePuzzleData object or null if parsing fails or data is not found.
 */
export function getMathMazePuzzleData(
  mathMazeSpecifics: any,
): MathMazePuzzleData | null {
  if (!mathMazeSpecifics) {
    console.error(
      'MathMaze specific data or puzzleString not found in puzzle object:',
    );
    return null;
  }
  return parseMathMazePuzzleFromString(mathMazeSpecifics);
}
