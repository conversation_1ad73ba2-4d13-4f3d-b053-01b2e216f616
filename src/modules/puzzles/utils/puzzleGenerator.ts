import { PuzzleCellType } from 'shared/CrossMathPuzzleQuestion/types/puzzleType';

const _size = (arr: any[]): number => arr?.length;

const _toNumber = (value: any): number => {
  if (typeof value === 'number') {
    return value;
  }
  if (value == null) {
    return 0;
  }
  return Number(value);
};

export const CrossMathCellType = {
  Operator: 'Operator',
  Operand: 'Operand',
  EmptyBlock: 'EmptyBlock',
};

const EmptyBlockValue = {
  value: '',
  type: CrossMathCellType.EmptyBlock,
  isVisible: true,
};

const gcd = (a: number, b: number): number => (b === 0 ? a : gcd(b, a % b));

const calculateLCM = (a: number, b: number): number => (a * b) / gcd(a, b);

const getRandomNumber = (min: number, max: number): number =>
  Math.floor(Math.random() * (max - min + 1)) + min;

const getRandomOperator = (): string => {
  const OPERATORS = ['+', '+', '-', '×', '÷'];

  return OPERATORS[getRandomNumber(0, OPERATORS.length - 1)];
};

const hideGridCells = ({
  grid,
  gridSize,
  numOfHints = 0,
}: {
  grid: PuzzleCellType[][];
  gridSize: number;
  numOfHints?: number;
}): PuzzleCellType[][] => {
  const hiddenCellCount =
    Math.floor(gridSize / 2) * Math.floor(gridSize / 2) - numOfHints;

  let totalHidden = 0;
  gridSize -= 1; // Adjust grid size

  for (let row = 0; row < gridSize && totalHidden < hiddenCellCount; row++) {
    const operandCols: number[] = [];
    for (let col = 0; col < gridSize; col++) {
      if (grid[row][col].type === CrossMathCellType.Operand) {
        operandCols.push(col);
      }
    }

    if (operandCols.length > 0) {
      const randomIndex = Math.floor(Math.random() * operandCols.length);
      grid[row][operandCols[randomIndex]].isVisible = false;
      totalHidden++;
    }
  }

  let rowIndex = 0;
  while (totalHidden < hiddenCellCount) {
    const row = rowIndex % gridSize;
    const visibleOperandCols: number[] = [];

    for (let col = 0; col < gridSize; col++) {
      if (
        grid[row][col].type === CrossMathCellType.Operand &&
        grid[row][col].isVisible
      ) {
        visibleOperandCols.push(col);
      }
    }

    if (visibleOperandCols.length > 0) {
      const randomIndex = Math.floor(Math.random() * visibleOperandCols.length);
      grid[row][visibleOperandCols[randomIndex]].isVisible = false;
      totalHidden++;
    }

    rowIndex++;
    if (rowIndex >= gridSize * 2) {
      break;
    }
  }

  return grid;
};

export const evaluateExpression = (arr: (string | number)[]): number => {
  // First handle multiplication and division (BODMAS Rule)
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] === '×' || arr[i] === '÷') {
      const num1 = _toNumber(arr[i - 1]);
      const operator = arr[i];
      const num2 = _toNumber(arr[i + 1]);

      let result: number;
      if (operator === '×') {
        result = num1 * num2;
      } else {
        result = num1 / num2;
      }

      // Replace the operator and its operands with the result
      arr.splice(i - 1, 3, result);

      // Adjust the loop index since we modified the array
      i -= 2;
    }
  }

  // Now handle addition and subtraction
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] === '+' || arr[i] === '-') {
      const num1 = _toNumber(arr[i - 1]);
      const operator = arr[i];
      const num2 = _toNumber(arr[i + 1]);

      let result: number;
      if (operator === '+') {
        result = num1 + num2;
      } else {
        result = num1 - num2;
      }

      // Replace the operator and its operands with the result
      arr.splice(i - 1, 3, result);

      // Adjust the loop index since we modified the array
      i -= 2;
    }
  }
  // The final result should be the only value left in the array
  return arr[0] as number;
};

export const generatePuzzle = ({
  gridSize = 7,
}: {
  gridSize?: number;
}): PuzzleCellType[][] => {
  // Generate a valid puzzle grid
  const grid: PuzzleCellType[][] = Array(gridSize)
    .fill(null)
    .map(() =>
      Array(gridSize)
        .fill(null)
        .map(() => ({
          id: '',
          value: '_',
          type: CrossMathCellType.Operand,
          isVisible: true,
        })),
    );

  for (let row = 0; row < gridSize - 1; row += 2) {
    grid[row][gridSize - 2] = {
      id: '',
      value: '=',
      type: CrossMathCellType.Operator,
      isVisible: true,
    };
    grid[row + 1][gridSize - 2] = { ...EmptyBlockValue, id: '' };
    grid[row + 1][gridSize - 1] = { ...EmptyBlockValue, id: '' };
  }

  for (let cell = 0; cell < gridSize - 1; cell += 2) {
    grid[gridSize - 2][cell] = {
      id: '',
      value: '=',
      type: CrossMathCellType.Operator,
      isVisible: true,
    };
    grid[gridSize - 2][cell + 1] = { ...EmptyBlockValue, id: '' };
    grid[gridSize - 1][cell + 1] = { ...EmptyBlockValue, id: '' };
  }

  grid[gridSize - 1][gridSize - 1] = { ...EmptyBlockValue, id: '' };

  // Set empty blocks
  for (let x = 1; x < gridSize; x += 2) {
    for (let y = 1; y < gridSize; y += 2) {
      grid[x][y] = { ...EmptyBlockValue, id: '' };
    }
  }

  // set random operators
  for (let row = 0; row < gridSize - 2; row += 1) {
    const isOddRow = row % 2 === 1;
    for (let col = isOddRow ? 0 : 1; col < gridSize - 2; col += 2) {
      grid[row][col] = {
        id: '',
        value: getRandomOperator(),
        type: CrossMathCellType.Operator,
        isVisible: true,
      };
    }
  }

  // for each row and column ensure that both of the operators are not '÷'
  for (let row = 0; row < gridSize - 2; row += 2) {
    const colIndex = 1;
    if (colIndex + 2 < gridSize - 2) {
      if (
        grid[row][colIndex].value === '÷' &&
        grid[row][colIndex + 2].value === '÷'
      ) {
        grid[row][colIndex].value = '+';
      }
      if (
        grid[row][colIndex].value === '×' &&
        grid[row + 2][colIndex].value === '×'
      ) {
        grid[row + 2][colIndex].value = '-';
      }
    }
  }

  // Check and fix consecutive division operators in columns
  for (let col = 0; col < gridSize - 2; col += 2) {
    const rowIndex = 1;
    if (rowIndex + 2 < gridSize - 2) {
      if (
        grid[rowIndex][col].value === '÷' &&
        grid[rowIndex + 2][col].value === '÷'
      ) {
        grid[rowIndex][col].value = '+';
      }
      if (
        grid[rowIndex][col].value === '×' &&
        grid[rowIndex + 2][col].value === '×'
      ) {
        grid[rowIndex + 2][col].value = '-';
      }
    }
  }

  for (let row = gridSize - 3; row >= 0; row -= 2) {
    for (let col = gridSize - 3; col >= 0; col -= 2) {
      if (row === gridSize - 3 && col === gridSize - 3) {
        grid[row][col].value = getRandomNumber(5, 10).toString();
      } else if (row === gridSize - 3) {
        const nextNumber = _toNumber(grid[row][col + 2].value);
        if (grid[row][col + 1].value === '÷') {
          grid[row][col].value = (
            getRandomNumber(5, 10) * nextNumber
          ).toString();
        } else if (grid[row][col + 1].value === '-') {
          grid[row][col].value = getRandomNumber(
            nextNumber + 5,
            nextNumber + 50,
          ).toString();
        } else if (grid[row][col + 1].value === '×') {
          grid[row][col].value = getRandomNumber(2, 10).toString();
        } else {
          grid[row][col].value = getRandomNumber(5, 20).toString();
        }
      } else if (col === gridSize - 3) {
        const nextNumber = _toNumber(grid[row + 2][col].value);
        if (grid[row + 1][col].value === '÷') {
          grid[row][col].value = (
            getRandomNumber(5, 10) * nextNumber
          ).toString();
        } else if (grid[row + 1][col].value === '-') {
          grid[row][col].value = getRandomNumber(
            nextNumber + 5,
            nextNumber + 50,
          ).toString();
        } else if (grid[row + 1][col].value === '×') {
          grid[row][col].value = getRandomNumber(2, 10).toString();
        } else {
          grid[row][col].value = getRandomNumber(5, 20).toString();
        }
      } else if (
        grid[row + 1][col].value === '÷' &&
        grid[row][col + 1].value === '÷'
      ) {
        const lcm = calculateLCM(
          _toNumber(grid[row][col + 2].value),
          _toNumber(grid[row + 2][col].value),
        );
        grid[row][col].value = (lcm * getRandomNumber(2, 5)).toString();
      } else if (grid[row + 1][col].value === '÷') {
        grid[row][col].value = (
          _toNumber(grid[row + 2][col].value) * getRandomNumber(2, 5)
        ).toString();
      } else if (grid[row][col + 1].value === '÷') {
        grid[row][col].value = (
          _toNumber(grid[row][col + 2].value) * getRandomNumber(2, 5)
        ).toString();
      } else if (
        grid[row][col + 1].value === '×' ||
        grid[row + 1][col].value === '×'
      ) {
        grid[row][col].value = getRandomNumber(2, 5).toString();
      } else {
        grid[row][col].value = getRandomNumber(5, 20).toString();
      }
    }
  }

  for (let row = 0; row < gridSize - 2; row += 2) {
    const arr: (string | number)[] = [];
    for (let col = 0; col < gridSize - 2; col += 1) {
      arr.push(grid[row][col].value);
    }
    grid[row][gridSize - 1].value = evaluateExpression(arr).toString();
  }

  for (let col = 0; col < gridSize - 2; col += 2) {
    const arr: (string | number)[] = [];
    for (let row = 0; row < gridSize - 2; row += 1) {
      arr.push(grid[row][col].value);
    }
    grid[gridSize - 1][col].value = evaluateExpression(arr).toString();
  }

  return grid;
};

export const getNewPuzzle = ({
  gridSize = 7,
  numOfHints = 0,
}: {
  gridSize?: number;
  numOfHints?: number;
}) => {
  const grid = generatePuzzle({ gridSize });

  const hiddenGrid = hideGridCells({
    grid,
    gridSize,
    numOfHints,
  });

  const availableAnswers: string[] = [];
  for (let i = 0; i < gridSize - 2; i++) {
    for (let j = 0; j < gridSize - 2; j++) {
      if (
        hiddenGrid[i][j].type === CrossMathCellType.Operand &&
        !hiddenGrid[i][j].isVisible
      ) {
        availableAnswers.push(hiddenGrid[i][j].value);
      }
    }
  }

  // Flatten the grid and add ids
  // const cells = hiddenGrid
  //   .flat()
  //   .map((cell, index) => ({ ...cell, id: index.toString() }));

  return { cells: grid, availableAnswers };
};
