// Code Reference from https://alexandernwilson.com/Gengen/ for KenKen

import _isNil from 'lodash/isNil';
import { decryptJsonData } from 'core/utils/encryptions/decrypt';
import _isString from 'lodash/isString';

interface KenKenSettings {
  size: number;
  seed?: number;
  maxGroupSize: number;
  torus: boolean;
  operations: {
    addition: boolean;
    subtraction: boolean;
    multiplication: boolean;
    division: boolean;
    max: boolean;
    min: boolean;
    range: boolean;
    mod: boolean;
    avg: boolean;
    par: boolean;
    gcd: boolean;
  };
}

interface MersenneTwister {
  random(): number;
}

class KenKenCell {
  kenken: Kenken;

  x: number | undefined;

  y: number | undefined;

  cellGroup: KenKenCellGroup | undefined;

  value: number;

  constructor(kenken: Kenken, value: number) {
    this.kenken = kenken;
    this.x = undefined;
    this.y = undefined;
    this.cellGroup = undefined;
    this.value = value;
  }

  setCellGroup(cellGroup: KenKenCellGroup): void {
    this.cellGroup = cellGroup;
  }

  getNeighbors(): KenKenCell[] {
    const neighbors: KenKenCell[] = [];

    if (_isNil(this.x) || _isNil(this.y)) return neighbors;

    if (this.kenken.settings.torus) {
      if (this.x > 0) neighbors.push(this.kenken.board[this.x - 1][this.y]);
      else neighbors.push(this.kenken.board[this.kenken.size - 1][this.y]);

      if (this.y > 0) neighbors.push(this.kenken.board[this.x][this.y - 1]);
      else neighbors.push(this.kenken.board[this.x][this.kenken.size - 1]);

      if (this.x < this.kenken.size - 1)
        neighbors.push(this.kenken.board[this.x + 1][this.y]);
      else neighbors.push(this.kenken.board[0][this.y]);

      if (this.y < this.kenken.size - 1)
        neighbors.push(this.kenken.board[this.x][this.y + 1]);
      else neighbors.push(this.kenken.board[this.x][0]);
    } else {
      if (this.x > 0) neighbors.push(this.kenken.board[this.x - 1][this.y]);
      if (this.y > 0) neighbors.push(this.kenken.board[this.x][this.y - 1]);
      if (this.x < this.kenken.size - 1)
        neighbors.push(this.kenken.board[this.x + 1][this.y]);
      if (this.y < this.kenken.size - 1)
        neighbors.push(this.kenken.board[this.x][this.y + 1]);
    }

    return neighbors;
  }

  getNeighborsOriented(): Record<string, KenKenCell | undefined> {
    const neighbors: Record<string, KenKenCell | undefined> = {};

    if (_isNil(this.x) || _isNil(this.y)) return neighbors;

    if (this.kenken.settings.torus) {
      neighbors.left =
        this.x > 0
          ? this.kenken.board[this.x - 1][this.y]
          : this.kenken.board[this.kenken.size - 1][this.y];
      neighbors.up =
        this.y > 0
          ? this.kenken.board[this.x][this.y - 1]
          : this.kenken.board[this.x][this.kenken.size - 1];
      neighbors.right =
        this.x < this.kenken.size - 1
          ? this.kenken.board[this.x + 1][this.y]
          : this.kenken.board[0][this.y];
      neighbors.down =
        this.y < this.kenken.size - 1
          ? this.kenken.board[this.x][this.y + 1]
          : this.kenken.board[this.x][0];
    } else {
      if (this.x > 0) neighbors.left = this.kenken.board[this.x - 1][this.y];
      if (this.y > 0) neighbors.up = this.kenken.board[this.x][this.y - 1];
      if (this.x < this.kenken.size - 1)
        neighbors.right = this.kenken.board[this.x + 1][this.y];
      if (this.y < this.kenken.size - 1)
        neighbors.down = this.kenken.board[this.x][this.y + 1];
    }

    return neighbors;
  }
}

class KenKenCellGroup {
  kenken: Kenken;

  groupID: number;

  cells: KenKenCell[];

  currentSize: number;

  operationDescription: string | undefined;

  constructor(kenken: Kenken, cell: KenKenCell, id: number) {
    this.kenken = kenken;
    this.groupID = id;
    this.cells = [cell];
    cell.setCellGroup(this);
    this.currentSize = 1;
    this.operationDescription = undefined;
  }

  getAllValues(): number[] {
    const returnArray: number[] = [];
    for (let i = 0; i < this.cells.length; i++) {
      returnArray.push(this.cells[i].value);
    }
    return returnArray;
  }

  grow(): boolean {
    const startingCellNumber = Math.floor(
      this.cells.length * this.kenken.seed.random(),
    );
    let cellNum = startingCellNumber;

    while (true) {
      const cellToGrowFrom = this.cells[cellNum];
      const cellNeighbors = cellToGrowFrom.getNeighbors();
      const neighborCellNum = Math.floor(
        cellNeighbors.length * this.kenken.seed.random(),
      );

      for (let i = 0; i < cellNeighbors.length; i++) {
        const neighborCell =
          cellNeighbors[(i + neighborCellNum) % cellNeighbors.length];
        if (neighborCell.cellGroup === undefined) {
          this.cells.push(neighborCell);
          neighborCell.setCellGroup(this);
          this.currentSize += 1;
          return true;
        }
      }

      cellNum = (cellNum + 1) % this.cells.length;
      if (cellNum === startingCellNumber) {
        return false;
      }
    }
  }

  getTopLeft(): KenKenCell {
    const { cells } = this;
    let topLeftCell = cells[0];

    for (let i = 1; i < cells.length; i++) {
      if (
        cells[i].x !== undefined &&
        topLeftCell.x !== undefined &&
        cells[i].y !== undefined &&
        topLeftCell.y !== undefined
      ) {
        if (cells[i].x! <= topLeftCell.x!) {
          if (cells[i].y! < topLeftCell.y!) topLeftCell = cells[i];
        }
      }
    }

    return topLeftCell;
  }
}

interface Operation {
  minCells: number;
  maxCells?: number;
  symbol: string;

  operation(arrayOfNumbers: number[]): number | false;
}

class SingleCell implements Operation {
  minCells = 1;

  maxCells = 1;

  symbol = '';

  operation(arrayOfNumbers: number[]): number | false {
    if (
      arrayOfNumbers.length > this.maxCells ||
      arrayOfNumbers.length < this.minCells
    ) {
      return false;
    }
    return arrayOfNumbers[0];
  }
}

class Addition implements Operation {
  minCells = 2;

  maxCells?: number;

  symbol = '+';

  operation(arrayOfNumbers: number[]): number | false {
    if (arrayOfNumbers.length < this.minCells) {
      return false;
    }

    let resultOfOperation = 0;
    for (let i = 0; i < arrayOfNumbers.length; i++) {
      resultOfOperation = arrayOfNumbers[i] + resultOfOperation;
    }
    return resultOfOperation;
  }
}

class Subtraction implements Operation {
  minCells = 2;

  maxCells = 2;

  symbol = '-';

  operation(arrayOfNumbers: number[]): number | false {
    if (
      arrayOfNumbers.length > this.maxCells ||
      arrayOfNumbers.length < this.minCells
    ) {
      return false;
    }

    if (arrayOfNumbers[0] > arrayOfNumbers[1]) {
      return arrayOfNumbers[0] - arrayOfNumbers[1];
    }
    return arrayOfNumbers[1] - arrayOfNumbers[0];
  }
}

class Multiplication implements Operation {
  minCells = 2;

  maxCells?: number;

  symbol = '×';

  operation(arrayOfNumbers: number[]): number | false {
    if (arrayOfNumbers.length < this.minCells) {
      return false;
    }

    let resultOfOperation = 1;
    for (let i = 0; i < arrayOfNumbers.length; i++) {
      resultOfOperation = arrayOfNumbers[i] * resultOfOperation;
    }
    return resultOfOperation;
  }
}

class Division implements Operation {
  minCells = 2;

  maxCells = 2;

  symbol = '÷';

  operation(arrayOfNumbers: number[]): number | false {
    if (
      arrayOfNumbers.length > this.maxCells ||
      arrayOfNumbers.length < this.minCells
    ) {
      return false;
    }

    if ((arrayOfNumbers[0] / arrayOfNumbers[1]) % 1 === 0) {
      return arrayOfNumbers[0] / arrayOfNumbers[1];
    }
    if ((arrayOfNumbers[1] / arrayOfNumbers[0]) % 1 === 0) {
      return arrayOfNumbers[1] / arrayOfNumbers[0];
    }
    return false;
  }
}

class Maximum implements Operation {
  minCells = 2;

  maxCells = 3;

  symbol = 'max';

  operation(arrayOfNumbers: number[]): number | false {
    if (
      arrayOfNumbers.length > this.maxCells ||
      arrayOfNumbers.length < this.minCells
    ) {
      return false;
    }

    let resultOfOperation = arrayOfNumbers[0];
    for (let i = 1; i < arrayOfNumbers.length; i++) {
      if (resultOfOperation < arrayOfNumbers[i]) {
        resultOfOperation = arrayOfNumbers[i];
      }
    }
    return resultOfOperation;
  }
}

class Minimum implements Operation {
  minCells = 2;

  maxCells = 3;

  symbol = 'min';

  operation(arrayOfNumbers: number[]): number | false {
    if (
      arrayOfNumbers.length > this.maxCells ||
      arrayOfNumbers.length < this.minCells
    ) {
      return false;
    }

    let resultOfOperation = arrayOfNumbers[0];
    for (let i = 1; i < arrayOfNumbers.length; i++) {
      if (resultOfOperation > arrayOfNumbers[i]) {
        resultOfOperation = arrayOfNumbers[i];
      }
    }
    return resultOfOperation;
  }
}

class Range implements Operation {
  minCells = 2;

  maxCells = 4;

  symbol = 'range';

  operation(arrayOfNumbers: number[]): number | false {
    if (
      arrayOfNumbers.length > this.maxCells ||
      arrayOfNumbers.length < this.minCells
    ) {
      return false;
    }

    let min = arrayOfNumbers[0];
    let max = arrayOfNumbers[0];
    for (let i = 1; i < arrayOfNumbers.length; i++) {
      if (max < arrayOfNumbers[i]) {
        max = arrayOfNumbers[i];
      } else if (min > arrayOfNumbers[i]) {
        min = arrayOfNumbers[i];
      }
    }
    return max - min;
  }
}

class Mod implements Operation {
  minCells = 2;

  maxCells = 2;

  symbol = '%';

  operation(arrayOfNumbers: number[]): number | false {
    if (
      arrayOfNumbers.length > this.maxCells ||
      arrayOfNumbers.length < this.minCells
    ) {
      return false;
    }

    if (arrayOfNumbers[0] > arrayOfNumbers[1]) {
      return arrayOfNumbers[0] % arrayOfNumbers[1];
    }
    return arrayOfNumbers[1] % arrayOfNumbers[0];
  }
}

class Average implements Operation {
  minCells = 2;

  maxCells = 4;

  symbol = 'avg';

  operation(arrayOfNumbers: number[]): number | false {
    if (
      arrayOfNumbers.length > this.maxCells ||
      arrayOfNumbers.length < this.minCells
    ) {
      return false;
    }

    let sum = 0;
    for (let i = 0; i < arrayOfNumbers.length; i++) {
      sum += arrayOfNumbers[i];
    }
    const average = sum / arrayOfNumbers.length;
    if (average % 1 === 0) {
      return average;
    }
    return false;
  }
}

class Parity implements Operation {
  minCells = 2;

  maxCells = 2;

  symbol = 'par';

  operation(arrayOfNumbers: number[]): number | false {
    if (
      arrayOfNumbers.length > this.maxCells ||
      arrayOfNumbers.length < this.minCells
    ) {
      return false;
    }

    const firstNum = arrayOfNumbers[0];
    const secondNum = arrayOfNumbers[1];
    if ((firstNum + secondNum) % 2 === 0) {
      return 1;
    }
    return 0;
  }
}

class Gcd implements Operation {
  minCells = 2;

  maxCells = 3;

  symbol = 'gcd';

  operation(arrayOfNumbers: number[]): number | false {
    if (
      arrayOfNumbers.length > this.maxCells ||
      arrayOfNumbers.length < this.minCells
    ) {
      return false;
    }

    function gcd(a: number, b: number): number {
      return b ? gcd(b, a % b) : a;
    }

    let currentGCD = arrayOfNumbers[0];
    for (let i = 1; i < arrayOfNumbers.length; i++) {
      currentGCD = gcd(currentGCD, arrayOfNumbers[i]);
    }
    return currentGCD;
  }
}

class SimpleMersenneTwister implements MersenneTwister {
  private seed: number;

  constructor(seed: number) {
    this.seed = seed;
  }

  random(): number {
    const x = Math.sin(this.seed++) * 10000;
    return x - Math.floor(x);
  }
}

class Kenken {
  size: number;

  settings: KenKenSettings;

  board: KenKenCell[][];

  minGroupSize: number;

  defaultMaxGroupSize: number;

  maxGroupSize: number | undefined;

  cellGroups: KenKenCellGroup[];

  operations: Operation[];

  seed: MersenneTwister;

  constructor(settings: KenKenSettings) {
    this.size = settings.size;
    this.settings = settings;
    this.board = [];
    this.minGroupSize = 1;
    this.defaultMaxGroupSize = settings.maxGroupSize;
    this.maxGroupSize = undefined;
    this.cellGroups = [];
    this.operations = [new SingleCell()];

    if (settings.operations.addition) {
      this.operations.push(new Addition());
    }
    if (settings.operations.subtraction) {
      this.operations.push(new Subtraction());
    }
    if (settings.operations.multiplication) {
      this.operations.push(new Multiplication());
    }
    if (settings.operations.division) {
      this.operations.push(new Division());
    }
    if (settings.operations.max) {
      this.operations.push(new Maximum());
    }
    if (settings.operations.min) {
      this.operations.push(new Minimum());
    }
    if (settings.operations.range) {
      this.operations.push(new Range());
    }
    if (settings.operations.mod) {
      this.operations.push(new Mod());
    }
    if (settings.operations.avg) {
      this.operations.push(new Average());
    }
    if (settings.operations.par) {
      this.operations.push(new Parity());
    }
    if (settings.operations.gcd) {
      this.operations.push(new Gcd());
    }

    for (let i = 0; i < this.operations.length; i++) {
      const maxOperationSize = this.operations[i].maxCells;
      if (maxOperationSize === undefined) {
        this.maxGroupSize = this.defaultMaxGroupSize;
        i = this.operations.length;
      } else if (this.maxGroupSize === undefined) {
        this.maxGroupSize = maxOperationSize;
      } else if (maxOperationSize > this.maxGroupSize) {
        this.maxGroupSize = maxOperationSize;
      }
    }

    if (
      this.maxGroupSize !== undefined &&
      this.maxGroupSize > this.defaultMaxGroupSize
    ) {
      this.maxGroupSize = this.defaultMaxGroupSize;
    }

    this.seed = new SimpleMersenneTwister(
      settings.seed || new Date().getTime(),
    );

    const builderArray = this.shuffledNumberArray(this.size);

    for (let x = 0; x < this.size; x++) {
      this.board[x] = [];
      for (let y = 0; y < this.size; y++) {
        this.board[x][y] = new KenKenCell(
          this,
          builderArray[(x + y) % this.size],
        );
      }
    }
    this.shuffleBoard();

    for (let x = 0; x < this.size; x++) {
      for (let y = 0; y < this.size; y++) {
        this.board[x][y].x = x;
        this.board[x][y].y = y;
      }
    }

    let groupID = 1;
    for (let x = 0; x < this.size; x++) {
      for (let y = 0; y < this.size; y++) {
        if (this.board[x][y].cellGroup === undefined) {
          const maxSize = this.maxGroupSize || this.defaultMaxGroupSize;
          const groupSize = Math.floor(
            (maxSize - this.minGroupSize + 1) * this.seed.random() +
              this.minGroupSize,
          );

          const newCellGroup = new KenKenCellGroup(
            this,
            this.board[x][y],
            groupID,
          );

          if (groupSize !== 1) {
            for (let m = 0; m < groupSize - 1; m++) {
              newCellGroup.grow();
            }
          }

          this.shuffleArray(this.operations);

          const randomOperationStart = 0;
          let randomOperation = randomOperationStart;
          let foundOperation = false;

          while (foundOperation === false) {
            const result = this.operations[randomOperation].operation(
              newCellGroup.getAllValues(),
            );
            if (result !== false) {
              newCellGroup.operationDescription =
                this.operations[randomOperation].symbol + result;
              foundOperation = true;
            } else {
              randomOperation = (randomOperation + 1) % this.operations.length;
              if (randomOperation === randomOperationStart) {
                break;
              }
            }
          }

          this.cellGroups.push(newCellGroup);
          groupID++;
        }
      }
    }
  }

  private shuffleBoard(): void {
    for (let i = 0; i < this.size; i++) {
      const column1 = Math.floor(this.size * this.seed.random());
      const column2 = Math.floor(this.size * this.seed.random());

      for (let j = 0; j < this.size; j++) {
        const tempCell = this.board[j][column1];
        this.board[j][column1] = this.board[j][column2];
        this.board[j][column2] = tempCell;
      }

      const row1 = Math.floor(this.size * this.seed.random());
      const row2 = Math.floor(this.size * this.seed.random());

      for (let j = 0; j < this.size; j++) {
        const tempCell = this.board[row1][j];
        this.board[row1][j] = this.board[row2][j];
        this.board[row2][j] = tempCell;
      }
    }
  }

  private shuffleArray<T>(array: T[]): void {
    for (let i = 0; i < array.length - 1; i++) {
      const randomNum = Math.floor((array.length - i) * this.seed.random() + i);
      const temp = array[i];
      array[i] = array[randomNum];
      array[randomNum] = temp;
    }
  }

  private shuffledNumberArray(n: number): number[] {
    const numberArray: number[] = [];
    for (let i = 0; i < n; i++) {
      numberArray.push(i + 1);
    }
    this.shuffleArray(numberArray);
    return numberArray;
  }
}

export function generateKenKen(settings: Partial<KenKenSettings>): Kenken {
  const defaultSettings: KenKenSettings = {
    size: 6,
    seed: new Date().getTime(),
    maxGroupSize: 3,
    torus: false,
    operations: {
      addition: true,
      subtraction: true,
      multiplication: true,
      division: true,
      max: false,
      min: false,
      range: false,
      mod: false,
      avg: true,
      par: false,
      gcd: false,
    },
  };

  const mergedSettings: KenKenSettings = {
    ...defaultSettings,
    ...settings,
    operations: {
      ...defaultSettings.operations,
      ...(settings.operations || {}),
    },
  };

  const kenKen = new Kenken(mergedSettings);

  return kenKen;
}

export { Kenken, KenKenCell, KenKenCellGroup };
export type { KenKenSettings };

export function parseKenKenString(encryptedPuzzle: string): Kenken {
  if (!_isString(encryptedPuzzle)) {
    return encryptedPuzzle;
  }

  const puzzleString = decryptJsonData(encryptedPuzzle);

  const lines = puzzleString.trim().split('\n');

  const sizeMatch = lines[0].match(/Size: (\d+)/);
  if (!sizeMatch) {
    throw new Error('Could not parse puzzle size from the first line.');
  }
  const size = parseInt(sizeMatch[1], 10);

  const boardStartIndex = lines.findIndex(
    (
      line: string, // Added type
    ) => line.trim().startsWith('0 ['),
  );
  if (boardStartIndex === -1) {
    throw new Error('Could not find Board section.');
  }

  const boardValues: number[][] = [];
  for (let i = 0; i < size; i++) {
    const rowIndex = boardStartIndex + i;
    if (rowIndex >= lines.length) {
      throw new Error(
        `Board data incomplete. Expected ${size} rows, found ${i}.`,
      );
    }

    const rowMatches = lines[rowIndex].match(/\[(\d+)\]/g);
    if (!rowMatches || rowMatches.length !== size) {
      throw new Error(
        `Invalid board data in row ${i + 1}. Expected ${size} numbers.`,
      );
    }

    const rowValues = rowMatches.map((match: string) =>
      parseInt(match.replace(/[\[\]]/g, ''), 10),
    );
    boardValues.push(rowValues);
  }

  const groupsStartIndex = lines.findIndex(
    (line: string) => line.trim() === 'Groups:',
  );
  if (groupsStartIndex === -1) {
    throw new Error('Could not find Groups section.');
  }

  const groupsData: {
    groupId: number;
    cells: { x: number; y: number }[];
    operationDescription: string;
  }[] = [];

  const groupRegex = /Group (\d+): ([+\-×÷]?\d+) Cells: (.+)/;

  for (let i = groupsStartIndex + 1; i < boardStartIndex - 1; i++) {
    const line = lines[i].trim();
    if (!line) continue;

    const groupMatch = line.match(groupRegex);
    if (!groupMatch) {
      continue;
    }

    const [, groupIdStr, operationStr, cellsStr] = groupMatch;
    const groupId = parseInt(groupIdStr, 10);

    let operationSymbol = '';
    let result = '';
    if (operationStr.match(/^[+\-×÷]/)) {
      operationSymbol = operationStr.charAt(0);
      result = operationStr.substring(1);
    } else {
      operationSymbol = '';
      result = operationStr;
    }

    const operationDescription = operationSymbol + result;

    const cells: { x: number; y: number }[] = [];
    const cellMatches = cellsStr.match(/\((\d+),(\d+)\)/g);

    if (cellMatches) {
      for (const cellMatch of cellMatches) {
        const [x, y] = cellMatch.slice(1, -1).split(',').map(Number);
        cells.push({ x, y });
      }
    }

    if (cells.length === 0) {
      continue;
    }

    groupsData.push({ groupId, cells, operationDescription });
  }

  const settings: KenKenSettings = {
    size,
    seed: Math.floor(Math.random() * 1000000),
    maxGroupSize: size,
    torus: false,
    operations: {
      addition: true,
      subtraction: true,
      multiplication: true,
      division: true,
      max: false,
      min: false,
      range: false,
      mod: false,
      avg: false,
      par: false,
      gcd: false,
    },
  };

  const kenken = new Kenken(settings);
  kenken.size = size;

  const board: KenKenCell[][] = Array(size)
    .fill(null)
    .map(() => Array(size).fill(null));
  const cellMap = new Map<string, KenKenCell>();

  for (let y = 0; y < size; y++) {
    for (let x = 0; x < size; x++) {
      const cellValue = boardValues[y][x];
      const cell = new KenKenCell(kenken, cellValue);
      cell.x = x;
      cell.y = y;
      board[x][y] = cell;
      cellMap.set(`${x},${y}`, cell);
    }
  }
  kenken.board = board;

  const cellGroups: KenKenCellGroup[] = [];
  for (const groupData of groupsData) {
    const firstCellCoords = `${groupData.cells[0].x},${groupData.cells[0].y}`;
    const firstCell = cellMap.get(firstCellCoords);

    if (!firstCell) {
      continue;
    }

    const group = new KenKenCellGroup(kenken, firstCell, groupData.groupId);
    group.operationDescription = groupData.operationDescription;
    group.cells = [];

    for (const cellData of groupData.cells) {
      const cellCoords = `${cellData.x},${cellData.y}`;
      const cell = cellMap.get(cellCoords);
      if (cell) {
        cell.setCellGroup(group);
        group.cells.push(cell);
      } else {
        //
      }
    }

    group.currentSize = group.cells.length;
    cellGroups.push(group);
  }
  kenken.cellGroups = cellGroups;

  return kenken;
}
