import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useCallback, useEffect } from 'react';
import { Text, View } from 'react-native';
import Rive from 'atoms/Rive';
import PuzzleDateSelectorModal from 'modules/puzzles/components/PuzzleDateSelector/PuzzleDateSelectorModal';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from 'atoms/Icon';
import dark from 'core/constants/themes/dark';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import Analytics from 'core/analytics';
import useMediaQuery from 'core/hooks/useMediaQuery';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import { format } from 'date-fns';
import Header from 'shared/Header';
import { PUZZLE_DETAILS } from 'modules/puzzles/constants/puzzleConstants';
import _includes from 'lodash/includes';
import { VALID_PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import ErrorView from 'atoms/ErrorView';
import { CrossMathInstructions } from 'modules/puzzles/components/PuzzleInstruction';
import WebBackButton from 'shared/WebBackButton';
import useCrossMathPuzzleDailyChallengePageStyles from './PuzzleDailyChallengeByType.style';

let TRACKED_VIEWED_PUZZLE_HOME_PAGE = false;

const PuzzleDailyChallengeByType = ({ puzzleType }: { puzzleType: string }) => {
  const today = new Date(getCurrentTimeWithOffset());
  const formattedTodaysDate = format(today, 'yyyy-MM-dd');
  const styles = useCrossMathPuzzleDailyChallengePageStyles();
  const router = useRouter();
  const { isMobile } = useMediaQuery();
  const riveFileDimension = isMobile ? 300 : 300;

  const onPressDailyPuzzle = useCallback(() => {
    const eventToBeTracked =
      ANALYTICS_EVENTS.CROSS_MATH_PUZZLE?.[puzzleType]
        ?.CLICKED_ON_SOLVE_TODAYS_PUZZLE_CARD;
    Analytics.track(eventToBeTracked, {
      puzzleType,
    });

    router.push(
      `/puzzle/daily-challenge/${formattedTodaysDate}?puzzleType=${puzzleType}`,
    );

    return null;
  }, [router, formattedTodaysDate, puzzleType]);

  const { title, animationUrl } = PUZZLE_DETAILS[puzzleType];

  const renderTrailingComponent = useCallback(
    () => <CrossMathInstructions puzzleType={puzzleType} />,
    [puzzleType],
  );

  useEffect(() => {
    if (TRACKED_VIEWED_PUZZLE_HOME_PAGE) return;
    TRACKED_VIEWED_PUZZLE_HOME_PAGE = true;
    Analytics.track(
      ANALYTICS_EVENTS.CROSS_MATH_PUZZLE?.[puzzleType]
        ?.VIEWED_PUZZLE_TYPE_HOME_PAGE,
    );
  }, [puzzleType]);

  return (
    <View style={[styles.container]}>
      <Header renderTrailingComponent={renderTrailingComponent} />
      {!isMobile && (
        <View
          style={{
            justifyContent: 'space-between',
            flexDirection: 'row',
            width: '100%',
            maxWidth: 600,
            padding: 16,
          }}
        >
          <WebBackButton />
          {renderTrailingComponent()}
        </View>
      )}
      <View style={styles.innerContainer}>
        <Rive
          url={animationUrl ?? RIVE_ANIMATIONS.PUZZLE_ANIMATION}
          autoPlay
          style={{
            width: riveFileDimension,
            height: riveFileDimension,
            maxHeight: 320,
          }}
          stateMachineName="State Machine 1"
        />
        <View style={styles.crossMathPuzzleLabelsContainer}>
          <Text style={styles.crossMathPuzzleTitle}>{title.toUpperCase()}</Text>
          <Text style={styles.crossMathPuzzleDate}>{formattedTodaysDate}</Text>
        </View>
      </View>
      <View style={styles.actionsContainer}>
        <PuzzleDateSelectorModal date={formattedTodaysDate}>
          {({ onPressPuzzleDateTab }: { onPressPuzzleDateTab: () => void }) => (
            <View>
              <InteractiveSecondaryButton
                onPress={onPressPuzzleDateTab}
                type="secondary"
                iconConfig={{
                  name: 'calendar-day',
                  type: ICON_TYPES.FONT_AWESOME_5,
                  color: dark.colors.puzzle.primary,
                  size: 20,
                }}
                buttonContainerStyle={{ width: 48, height: 48 }}
                buttonContentStyle={{
                  paddingHorizontal: 0,
                  paddingVertical: 0,
                }}
              />
            </View>
          )}
        </PuzzleDateSelectorModal>
        <InteractiveSecondaryButton
          onPress={onPressDailyPuzzle}
          type="secondary"
          label={`PLAY TODAY'S PUZZLE`}
          borderColor={dark.colors.puzzle.primary}
          buttonContainerStyle={{ height: 48, flex: 1 }}
          buttonContentStyle={{
            paddingHorizontal: 0,
            paddingVertical: 0,
          }}
        />
      </View>
    </View>
  );
};

const PuzzleDailyChallengeByTypeContainer = () => {
  const { puzzleType }: { puzzleType: string } = useLocalSearchParams();

  if (!_includes(VALID_PUZZLE_TYPES, puzzleType)) {
    return <ErrorView errorMessage="Invalid puzzle type" />;
  }

  return <PuzzleDailyChallengeByType puzzleType={puzzleType} />;
};

export default React.memo(PuzzleDailyChallengeByTypeContainer);
