import React, { useCallback, useEffect, useRef, useState } from 'react';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { Image, Platform, View } from 'react-native';
import puzzleReader from 'modules/puzzles/readers/puzzleReader';
import _isNil from 'lodash/isNil';
import ErrorView from 'atoms/ErrorView';
import { router, useLocalSearchParams, useRouter } from 'expo-router';
import Loading from 'atoms/Loading';
import {
  PUZZLE_TYPES,
  PuzzleType,
  VALID_PUZZLE_TYPES,
} from 'modules/puzzles/types/puzzleType';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import Rive from 'atoms/Rive';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';
import useSound from 'core/hooks/useSound';
import tada_drumroll_sound from 'assets/audio/tada_drumroll.wav';
import _includes from 'lodash/includes';
import PuzzleResultInfoCard from 'modules/puzzles/components/PuzzleResult/PuzzleResultInfoCard';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import dark from '@/src/core/constants/themes/dark';
import { ICON_TYPES } from '@/src/components/atoms/Icon';
import { format, parse } from 'date-fns';
import { getFormattedTimeObject } from '@/src/core/utils/general';
import { openShareableCardFlow } from '@/src/components/shared/ShareResultModal';
import { showPopover } from '@/src/components/molecules/Popover/Popover';
import DownloadPuzzleResultCard from 'modules/puzzles/components/PuzzleResult/DownloadPuzzleResultCard';
import LinearGradient from '@/src/components/atoms/LinearGradient';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import BackButton from '@/src/components/molecules/BackButton';
import GridResultSVG from '@/src/components/svg/puzzles/GridResultSVG';
import Blur from '@/assets/images/puzzle/blur.webp';
import UserResultInfo from '../../components/PuzzleResult/UserResultInfo';
import { getPuzzleRandomLabel } from '../../utils/getRandomResultLabel';
import { PUZZLE_TYPES_VS_LABEL } from '../../constants/puzzleConstants';
import puzzleResultReader from '../../readers/puzzleResultReader';
import usePuzzleDailyChallengeResultPageController from '../../hooks/usePuzzleDailyChallengeResultPageController';
import PuzzleResultFooter from '../../components/PuzzleResult/PuzzleResultFooter';

const TRACKED_VIEWED_RESULT_FOR_DATE = {};

const formatDateToHuman = (dateString: string): string => {
  const date = parse(dateString, 'yyyy-MM-dd', new Date());

  return format(date, 'dd MMM');
};

const PuzzleDailyChallengeResult = ({
  date,
  puzzle,
  puzzleType,
}: {
  date: string | string[];
  puzzle: PuzzleType;
  puzzleType: string;
}) => {
  const [showConfettiAnimation, setShowConfettiAnimation] =
    useState<boolean>(true);
  const puzzleDate = puzzleReader.puzzleDate(puzzle);
  const isWeb = Platform.OS === 'web';
  const { isMobile: isCompactMode } = useMediaQuery();

  const router = useRouter();

  const userResult = puzzleReader.currentUserResult(puzzle);

  const timeSpent = puzzleResultReader.timeSpent(userResult);

  const timeObject = getFormattedTimeObject(timeSpent);
  const puzzleTypeLabel = `${PUZZLE_TYPES_VS_LABEL[puzzleType]} Puzzle`;

  const translateY = useSharedValue(0);

  const scrollViewRef = useRef<any>(null);
  const leaderboardRef = useRef<any>(null);

  const [atBottom, setAtBottom] = useState(false);

  // const scrollToLeaderboard = useCallback(() => {
  //   if (leaderboardRef.current && scrollViewRef.current) {
  //     // Use measureInWindow instead of measureLayout for cross-platform compatibility
  //     leaderboardRef.current.measureInWindow((x, y, width, height) => {
  //       scrollViewRef.current.scrollTo({ y, animated: true });
  //     });
  //   }
  // }, []);

  // const scrollToTop = useCallback(() => {
  //   if (scrollViewRef.current) {
  //     scrollViewRef.current.scrollTo({ y: 0, animated: true });
  //   }
  // }, []);

  // const handleScroll = useCallback(
  //   (event: NativeSyntheticEvent<NativeScrollEvent>) => {
  //     const { layoutMeasurement, contentOffset, contentSize } =
  //       event.nativeEvent;
  //     const paddingToBottom = 20;
  //     const isAtBottom =
  //       layoutMeasurement.height + contentOffset.y >=
  //       contentSize.height - paddingToBottom;
  //     setAtBottom(isAtBottom);
  //   },
  //   [],
  // );

  useSound({
    soundFile: tada_drumroll_sound,
    playOnMount: true,
    config: {
      volume: 0.2,
    },
  });

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setShowConfettiAnimation(false);
    }, 2000);
    if (TRACKED_VIEWED_RESULT_FOR_DATE[date]) return;
    TRACKED_VIEWED_RESULT_FOR_DATE[date] = true;
    Analytics.track(
      ANALYTICS_EVENTS.CROSS_MATH_PUZZLE?.[puzzleType]
        ?.VIEWED_PUZZLE_RESULT_PAGE,
      {
        date,
      },
    );
    return () => {
      clearTimeout(timeoutId);
    };
  }, [date, puzzleType]);

  const renderPuzzleResultCard = useCallback(
    () => (
      <View style={{ flexShrink: 1, height: 250, width: 275 }}>
        <PuzzleResultInfoCard puzzle={puzzle} puzzleType={puzzleType} />
      </View>
    ),
    [puzzle, puzzleType],
  );

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  const onPressShareButton = useCallback(() => {
    const { hours, minutes, seconds } = timeObject;
    const humanReadableDate = formatDateToHuman(puzzleDate);
    const timeSpentString = `${hours > 0 ? `${hours} hours ` : ''}${minutes > 0 ? `${minutes} minutes ` : ''}${seconds} seconds`;
    const label = getPuzzleRandomLabel(
      puzzleType,
      timeSpentString,
      humanReadableDate,
    );

    if (isWeb) {
      showPopover({
        content: (
          <DownloadPuzzleResultCard puzzle={puzzle} puzzleType={puzzleType} />
        ),
        overlayLook: true,
        animationType: 'slide',
      });
    } else {
      translateY.value = withTiming(150, { duration: 300 }, (finished) => {
        if (finished) {
          translateY.value = withTiming(0, { duration: 500 });
        }
      });
      openShareableCardFlow({
        renderResultCard: renderPuzzleResultCard,
        message: label,
        storyBackgroundColors: {
          backgroundBottomColor: dark.colors.red,
          backgroundTopColor: dark.colors.puzzle.share.storyBackgroundColorTop,
        },
      });
    }
  }, [
    timeObject,
    puzzleDate,
    puzzleType,
    isWeb,
    puzzle,
    translateY,
    renderPuzzleResultCard,
  ]);

  const onPress = useCallback(
    (pType: string) => {
      router.push(
        `/puzzle/daily-challenge/leaderboard/${puzzle.id}?puzzleType=${pType}`,
      );
    },
    [router, puzzle.id],
  );

  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'center',
        width: '100%',
        alignItems: 'center',
        alignSelf: 'center',
      }}
    >
      {showConfettiAnimation && (
        <Rive
          url={RIVE_ANIMATIONS.CONFETTI_ANIMATION}
          autoPlay
          loop={false}
          style={{
            position: 'absolute',
            zIndex: 150,
            top: 0,
            left: 0,
            right: 0,
            width: '100%',
            height: '100%',
          }}
        />
      )}
      <View style={{ zIndex: 100, position: 'absolute', top: 20, left: 15 }}>
        <BackButton />
      </View>
      <View
        style={{
          flex: 1,
          width: '100%',
          maxWidth: isCompactMode ? '100%' : 450,
          justifyContent: 'space-around',
          overflow: 'hidden',
        }}
      >
        {/* <ScrollView
          ref={scrollViewRef}
          onScroll={handleScroll}
          scrollEventThrottle={16}
        > */}
        <View
          style={{
            marginTop: 40,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {isCompactMode && (
            <View
              style={{
                position: 'absolute',
                top: -520,
                right: -470,
                zIndex: -2,
                overflow: 'hidden',
              }}
            >
              <GridResultSVG />
            </View>
          )}
          <Image
            source={Blur}
            style={{
              position: 'absolute',
              top: -520,
              right: -470,
              zIndex: -1,
            }}
          />
          <View
            style={{
              width: '100%',
              alignItems: 'center',
              justifyContent: 'center',
              paddingBottom: 20,
              position: 'relative',
            }}
          >
            <View
              style={{
                marginBottom: 25,
              }}
            >
              <PuzzleResultInfoCard puzzle={puzzle} puzzleType={puzzleType} />
            </View>
            <Animated.View style={[animatedStyle]}>
              <InteractiveSecondaryButton
                label="SHARE RESULT"
                labelStyle={{
                  fontSize: 12,
                  fontFamily: 'Montserrat-800',
                  color: dark.colors.textLight,
                }}
                buttonStyle={{ width: 140, height: 36 }}
                buttonBackgroundStyle={{
                  width: 140,
                  height: 30,
                  borderRadius: 10,
                }}
                buttonContainerStyle={{ height: 36 }}
                iconConfig={{
                  name: 'share',
                  type: ICON_TYPES.ENTYPO,
                  size: 16,
                  color: dark.colors.textLight,
                }}
                onPress={onPressShareButton}
              />
            </Animated.View>
            <LinearGradient
              colors={dark.colors.lightToDark}
              style={{
                marginTop: 100,
                width: '100%',
              }}
              start={{ x: 0.5, y: 0 }}
              end={{ x: 0.5, y: 0.4 }}
            >
              <UserResultInfo puzzle={puzzle} />
            </LinearGradient>
          </View>
          <View
            style={{
              position: 'absolute',
              top: 450,
              left: 0,
              right: 0,
              bottom: 0,
              height: 300,
              zIndex: -1,
              backgroundColor: dark.colors.background,
            }}
          />
          {/* <Image
              ref={leaderboardRef}
              source={ResultLeaderboard}
              style={{ width: '100%', resizeMode: 'contain' }}
              /> */}
        </View>
        {/* <PuzzleResultLeaderboard
            puzzleId={puzzle.id}
            puzzleType={puzzleType}
          />
          <InteractiveSecondaryButton
            buttonContainerStyle={{
              margin: 16,
            }}
            label="VIEW ENTIRE LEADERBOARD"
            labelStyle={{ fontSize: 12, fontFamily: 'Montserrat-800' }}
            onPress={() => onPress(puzzleType)}
            borderColor={dark.colors.puzzle.primary}
          />
          <View>
            <InviteFriendToMatiksSection
              mainContainerStyle={{ marginVertical: 90 }}
            />
          </View> */}
        {/* </ScrollView> */}
        {/* <LinearGradient
          colors={dark.colors.lightToDark}
          style={styles.bottomGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
        /> */}
        {/* <View
          style={{
            position: 'absolute',
            bottom: 8,
            width: '100%',
            alignItems: 'center',
            zIndex: 200,
          }}
        >
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: dark.colors.background,
              borderRadius: 20,
              paddingVertical: 8,
              paddingHorizontal: 16,
              borderWidth: 1,
              borderColor: dark.colors.triggerPointborder,
              justifyContent: 'center',
            }}
          >
            <View
              style={{
                width: 12,
                height: 12,
                backgroundColor: dark.colors.offWhite,
                marginRight: 8,
              }}
            />
            <Text
              style={{
                fontFamily: 'Montserrat-500',
                color: dark.colors.textLight,
                fontSize: 10,
              }}
              onPress={atBottom ? scrollToTop : scrollToLeaderboard}
            >
              {atBottom ? 'GO TO TOP' : 'GO TO LEADERBOARD'}
            </Text>
            <View
              style={{
                width: 12,
                height: 12,
                backgroundColor: dark.colors.offWhite,
                marginLeft: 8,
              }}
            />
          </View>
        </View> */}
      </View>
      <PuzzleResultFooter date={date} />
    </View>
  );
};

const PuzzleDCResultContainer = ({ date }: { date: string | string[] }) => {
  const {
    puzzleType = PUZZLE_TYPES.CROSS_MATH_PUZZLE,
  }: { puzzleType: string } = useLocalSearchParams();

  const { puzzle, loading, error } =
    usePuzzleDailyChallengeResultPageController({
      puzzleDate: date,
      puzzleType,
    });

  if (!_includes(VALID_PUZZLE_TYPES, puzzleType)) {
    return <ErrorView errorMessage="Invalid puzzle type" />;
  }

  if (loading) {
    return <Loading label="Loading Puzzle Result" />;
  }

  if (error) {
    return <ErrorView errorMessage="Something went wrong" />;
  }

  const userResult = puzzleReader.currentUserResult(puzzle);

  const renderToPuzzlePage = () => {
    router.replace(`/puzzle/daily-challenge/${date}?puzzleType=${puzzleType}`);
  };

  const emptyUserResultActionConfig = {
    label: 'Solve this puzzle',
    onPress: renderToPuzzlePage,
  };

  if (_isNil(userResult)) {
    return (
      <ErrorView
        errorMessage="You have not solved this Puzzle"
        actionConfig={emptyUserResultActionConfig}
      />
    );
  }

  return (
    <PuzzleDailyChallengeResult
      date={date}
      puzzle={puzzle}
      puzzleType={puzzleType}
    />
  );
};

export default React.memo(PuzzleDCResultContainer);
