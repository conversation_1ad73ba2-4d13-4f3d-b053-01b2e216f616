import React from 'react';
import { View } from 'react-native';
import { router } from 'expo-router';
import ErrorView from 'atoms/ErrorView';

import { isValidCrossMathPracticeConfig } from './utils';

interface CrossMathPuzzlePracticeProps {
  puzzleType: string;
  hintsCount: number;
  gridSize: number;
}

const CrossMathPuzzlePractice: React.FC<CrossMathPuzzlePracticeProps> = () => {
  const x = 4;
  return <View />;
};

const CrossMathPuzzlePracticeContainer: React.FC<
  CrossMathPuzzlePracticeProps
> = (props) => {
  const { hintsCount, gridSize } = props;
  const { isValid, error } = isValidCrossMathPracticeConfig({
    hintsCount,
    gridSize,
  });

  const handleGoToPuzzle = () => {
    router.replace('/puzzle-home');
  };

  if (!isValid) {
    const actionConfig = {
      label: 'Go to Puzzle Home',
      onPress: handleGoToPuzzle,
    };
    return <ErrorView errorMessage={error} actionConfig={actionConfig} />;
  }

  return <CrossMathPuzzlePractice {...props} />;
};

export default React.memo(CrossMathPuzzlePracticeContainer);
