import {
  <PERSON><PERSON>,
  <PERSON>ton,
  <PERSON><PERSON>,
  Sheet,
  Unspaced,
  XS<PERSON>ck,
  YStack,
} from 'tamagui';
import { View } from 'react-native';
import dark from 'core/constants/themes/dark';
import PuzzleDateSelector from 'modules/puzzles/components/PuzzleDateSelector/index';
import AntDesign from '@expo/vector-icons/AntDesign';
import React, { useCallback, useEffect, useState } from 'react';
import usePuzzleDates from 'modules/puzzles/hooks/usePuzzleDates';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { format } from 'date-fns';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import Haptics from 'core/container/Haptics';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import Pressable from '@/src/components/atoms/Pressable';

const getDefaultDate = () => {
  const today = new Date(getCurrentTimeWithOffset());
  return format(today, 'yyyy-MM-dd');
};

const PuzzleDateSelectorModal = ({
  children,
  date: dateFromProp,
  shouldCleanStack = false,
}) => {
  const date = dateFromProp ?? getDefaultDate();
  const { isToday, readableDate } = usePuzzleDates({ date });
  const [sheetPosition, setSheetPosition] = useState(0);
  const [dialogOpen, setDialogOpen] = useState(false);

  const {
    puzzleType = PUZZLE_TYPES.CROSS_MATH_PUZZLE,
  }: { puzzleType: string } = useLocalSearchParams();

  const router = useRouter();

  useEffect(() => {
    if (dialogOpen) {
      setSheetPosition(0);
    }
  }, [dialogOpen]);

  const onPressPuzzleDateTab = () => {
    const eventToBeTracked =
      ANALYTICS_EVENTS.CROSS_MATH_PUZZLE?.[puzzleType]?.CLICKED_ON_DATE_PICKER;
    Analytics.track(eventToBeTracked);
    setDialogOpen(true);
  };

  const closeSheet = () => {
    setSheetPosition(1);
    setTimeout(() => {
      setDialogOpen(false);
    }, 200);
  };

  const navigateToPlayPage = useCallback(
    ({ selectedDate }) => {
      if (shouldCleanStack) {
        router.replace(
          `/puzzle/daily-challenge/${selectedDate}?puzzleType=${puzzleType}`,
        );
      } else {
        router.push(
          `/puzzle/daily-challenge/${selectedDate}?puzzleType=${puzzleType}`,
        );
      }
    },
    [router, shouldCleanStack, puzzleType],
  );

  const onDateSelectedInSheet = useCallback(
    ({ selectedDate }) => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Rigid);
      setSheetPosition(1);
      setTimeout(() => {
        setDialogOpen(false);
      }, 200);
      navigateToPlayPage({ selectedDate });
    },
    [navigateToPlayPage],
  );

  const onDateSelectedInDialog = useCallback(
    ({ selectedDate }) => {
      setDialogOpen(false);
      navigateToPlayPage({ selectedDate });
    },
    [navigateToPlayPage],
  );

  const SheeetCloseButton = () => (
    <XStack
      position="absolute"
      top={-54}
      justifyContent="center"
      flex={1}
      zIndex={100}
      width="100%"
      paddingBottom={16}
      pointerEvents="box-none"
    >
      <Pressable
        onPress={closeSheet}
        style={{
          width: 72,
          height: 44,
          backgroundColor: dark.colors.primary,
          opacity: 0.4,
          borderWidth: 1,
          borderColor: 'white',
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: 25,
        }}
        impactFeedbackStyle={Haptics.ImpactFeedbackStyle.Rigid}
      >
        <View>
          <AntDesign name="close" size={18} color="white" />
        </View>
      </Pressable>
    </XStack>
  );

  const SheetContents = useCallback(
    () => (
      <YStack
        borderTopWidth={4}
        borderRadius={16}
        borderColor={dark.colors.puzzle.primary}
        pointerEvents="auto"
      >
        <YStack marginTop="$4" marginBottom="$2" pointerEvents="auto">
          <PuzzleDateSelector
            onDateSelect={onDateSelectedInSheet}
            date={date}
            puzzleType={puzzleType}
          />
        </YStack>
      </YStack>
    ),
    [date, puzzleType, onDateSelectedInSheet],
  );

  return (
    <Dialog modal open={dialogOpen} onOpenChange={setDialogOpen}>
      <Dialog.Trigger asChild>
        {children?.({ onPressPuzzleDateTab })}
      </Dialog.Trigger>

      <Adapt when="sm" platform="touch">
        <Sheet
          modal
          animation="medium"
          zIndex={200000}
          snapPointsMode="fit"
          position={sheetPosition}
          onPositionChange={setSheetPosition}
          dismissOnSnapToBottom
        >
          <Sheet.Overlay
            animation="lazy"
            enterStyle={{ opacity: 0 }}
            exitStyle={{ opacity: 0 }}
            pointerEvents="auto"
          />
          <SheeetCloseButton />

          <Sheet.Frame
            padding="$0"
            gap="$4"
            backgroundColor={dark.colors.gradientBackground}
            pointerEvents="auto"
          >
            <SheetContents />
          </Sheet.Frame>
        </Sheet>
      </Adapt>

      <Dialog.Portal>
        <Dialog.Overlay
          key="overlay"
          animation="slow"
          opacity={0.5}
          enterStyle={{ opacity: 0 }}
          exitStyle={{ opacity: 0 }}
        />

        <Dialog.Content
          bordered
          elevate
          key="content"
          animateOnly={['transform', 'opacity']}
          animation={[
            'quicker',
            {
              opacity: {
                overshootClamping: true,
              },
            },
          ]}
          enterStyle={{ x: 0, y: -20, opacity: 0, scale: 0.9 }}
          exitStyle={{ x: 0, y: 10, opacity: 0, scale: 0.95 }}
          gap="$4"
        >
          <PuzzleDateSelector
            onDateSelect={onDateSelectedInDialog}
            date={date}
            puzzleType={puzzleType}
          />
          <Unspaced>
            <Dialog.Close asChild>
              <Button
                position="absolute"
                top="$3"
                right="$3"
                size="$2"
                circular
                backgroundColor="transparent"
                icon={<AntDesign name="close" size={18} color="white" />}
              />
            </Dialog.Close>
          </Unspaced>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog>
  );
};

export default React.memo(PuzzleDateSelectorModal);
