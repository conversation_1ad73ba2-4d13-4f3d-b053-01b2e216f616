import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

const CALENDAR_WIDTH = 300;
const DAY_WIDTH = CALENDAR_WIDTH / 7;

export const DATE_DIMENSION = DAY_WIDTH - 7;

const styles = StyleSheet.create({
  todayText: {
    color: 'black',
    fontFamily: 'Montserrat-600',
  },
  futureText: {
    color: withOpacity('#FFFFFF', 0.2),
  },
  dateCell: {
    width: DAY_WIDTH,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dateBorder: {
    width: DAY_WIDTH - 8,
    height: DAY_WIDTH - 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 9,
  },
  todayBackground: {
    backgroundColor: dark.colors.puzzle.primary,
  },
  selectedBackground: {
    borderWidth: 1,
    borderColor: dark.colors.puzzle.primary,
    borderRadius: 9,
  },
  dateText: {
    fontSize: 12,
    fontFamily: 'Montserrat-800',
    color: dark.colors.textDark,
  },
  emptyDateBox: {
    width: DAY_WIDTH,
  },
  crownContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 9,
  },
  crownText: {
    fontSize: 15,
  },
  completedIconContainer: {
    position: 'absolute',
    top: 4,
    left: 4,
    width: 7,
    height: 7,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default styles;
