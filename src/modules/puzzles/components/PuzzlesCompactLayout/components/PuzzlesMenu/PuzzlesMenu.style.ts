import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderTopRightRadius: 36,
    borderTopLeftRadius: 36,
    backgroundColor: dark.colors.background,
    minHeight: 190,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  iosSheetIndicator: {
    width: 60,
    alignSelf: 'center',
    height: 4,
    borderRadius: 2,
    backgroundColor: dark.colors.placeholder,
  },
  actionsContainer: {
    gap: 16,
    flexDirection: 'column',
    paddingTop: 28,
    paddingBottom: 16,
    paddingHorizontal: 8,
  },
});

export default styles;
