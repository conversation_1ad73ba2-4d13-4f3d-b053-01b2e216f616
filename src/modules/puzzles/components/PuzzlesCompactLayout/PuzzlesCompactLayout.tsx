import React from 'react';
import {Text, View} from 'react-native';

import Rive from 'atoms/Rive';

import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import PuzzlesMenu from 'modules/puzzles/components/PuzzlesCompactLayout/components/PuzzlesMenu/PuzzlesMenu';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';
import styles from './PuzzlesCompactLayout.style';

const PuzzlesCompactLayout = () => {
    const renderHeader = () => (
        <View style={styles.headerContainer}>
            <FontAwesome6 name="puzzle-piece" size={18} color="white"/>
            <Text style={styles.headerText}>Puzzles</Text>
        </View>
    );

    const renderPandaPuzzleAnimation = () => (
        <View
            style={{
                width: '100%',
                flexDirection: 'row',
                justifyContent: 'center',
            }}
        >
            <Rive
                url={RIVE_ANIMATIONS.PANDA_ANIMATION}
                autoPlay
                style={{width: 100, height: 100}}
            />
        </View>
    );

    return (
        <View style={{flex: 1}}>
            {renderHeader()}
            <View
                style={{
                    flex: 1,
                    height: '100%',
                    width: '100%',
                }}
            >
                {renderPandaPuzzleAnimation()}
                <Rive
                    url={RIVE_ANIMATIONS.PUZZLE_ANIMATION}
                    autoPlay
                    style={{width: 400, height: 400}}
                />
            </View>
            <View style={styles.puzzlesMenuContainer}>
                <PuzzlesMenu/>
            </View>
        </View>
    );
};

export default React.memo(PuzzlesCompactLayout);
