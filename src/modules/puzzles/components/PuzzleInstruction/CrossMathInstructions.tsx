import React from 'react';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import KenKenInstructionContent from 'modules/puzzles/components/PuzzleInstruction/KenKenInstructionContent';
import { useLocalSearchParams } from 'expo-router';
import PuzzleInstructionContent from 'modules/puzzles/components/PuzzleInstruction/PuzzleInstructionContent';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import PuzzleInstruction from './PuzzleInstruction';

const CrossMathInstructions = ({
  puzzleType: propPuzzleType,
}: { puzzleType?: string } = {}) => {
  const { puzzleType: paramPuzzleType = PUZZLE_TYPES.CROSS_MATH_PUZZLE } =
    useLocalSearchParams();
  const puzzleType = propPuzzleType || paramPuzzleType;

  const ComponentToBeRendered =
    puzzleType === PUZZLE_TYPES.CROSS_MATH_PUZZLE
      ? PuzzleInstructionContent
      : <PERSON><PERSON>enInstructionContent;

  return (
    <PuzzleInstruction
      puzzleType={puzzleType as string}
      instructionContent={<ComponentToBeRendered />}
      analyticsEvent={
        ANALYTICS_EVENTS.CROSS_MATH_PUZZLE?.[puzzleType]?.CLICKED_ON_HOW_TO_PLAY
      }
      buttonLabel="How to Play?"
    />
  );
};

export default CrossMathInstructions;
