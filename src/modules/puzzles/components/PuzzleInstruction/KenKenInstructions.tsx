import React from 'react';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import PuzzleInstruction from './PuzzleInstruction';
import KenKenInstructionContent from './KenKenInstructionContent';

const KenKenInstructions = () => {
  return (
    <PuzzleInstruction
      instructionContent={<KenKenInstructionContent />}
      analyticsEvent={ANALYTICS_EVENTS.CROSS_MATH_PUZZLE.CLICKED_ON_PUZZLE_INFO_ICON} // You might want to create a specific analytics event for KenKen
      buttonLabel="How to Play?"
    />
  );
};

export default KenKenInstructions;
