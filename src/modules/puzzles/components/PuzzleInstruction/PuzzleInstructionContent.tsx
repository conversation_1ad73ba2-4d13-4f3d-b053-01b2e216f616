import React from 'react';
import dark from 'core/constants/themes/dark';
import GenericPuzzleInstructionContent, {
  InstructionStep,
} from './GenericPuzzleInstructionContent';

const crossMathInstructions: InstructionStep[] = [
  {
    title: 'BODMAS rule',
    description:
      'Follow BODMAS rule to solve this puzzle. Division and multiplication will have highest priority',
  },
  {
    title: 'How To Fill',
    description:
      'Tap on the grid cell that you want to fill. Then tap on the number in the footer row. The grid cell will be filled.',
  },
  {
    title: 'Undo Redo',
    description:
      'Tap on the UNDO icon for UNDO and tap on the REDO icon for REDO.',
    icon: {
      name: 'undo-variant',
      size: 18,
      color: dark.colors.textDark,
    },
  },
  {
    title: 'Delete Filled Cell',
    description:
      'You can tap on the filled grid cell to remove it from the grid.',
  },
  {
    title: 'Use Logic',
    description:
      'Start with the cells that have the most constraints (e.g., cells with only one possible number).',
  },
  {
    title: 'Repeat',
    description:
      'Continue filling in numbers and checking your work until the entire grid is complete.',
  },
];

const CrossMathInstructionsContent = () => (
  <GenericPuzzleInstructionContent
    title="How to Solve Cross Math Puzzle"
    steps={crossMathInstructions}
  />
);

export default CrossMathInstructionsContent;
