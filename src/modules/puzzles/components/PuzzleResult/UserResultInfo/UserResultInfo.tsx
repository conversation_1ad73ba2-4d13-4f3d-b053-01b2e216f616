import MetricStatCard from '@/src/components/shared/MetricStatCard';
import StaticCoinGainedMetric from '@/src/components/shared/StaticCoinGainedMetric';
import { View } from 'react-native';
import puzzleReader from '../../../readers/puzzleReader';
import puzzleResultReader from '../../../readers/puzzleResultReader';
import { getFormattedTimeWithMS } from '@/src/core/utils/general';
import puzzleStatsReader from '../../../readers/puzzleUserReader';
import { withOpacity } from '@/src/core/utils/colorUtils';
import dark from '@/src/core/constants/themes/dark';
import React from 'react';

const UserResultInfo = ({ puzzle }: { puzzle: any }) => {
  const puzzleResult: any = puzzleReader.currentUserResult(puzzle);
  const coinsGained = puzzleResultReader.statikCoinsEarned(puzzleResult);
  const userStat = puzzleReader.userStat(puzzle);
  const bestTime = puzzleStatsReader.bestTime(userStat);
  const currentPuzzleTimeTaken = puzzleResultReader.timeSpent(puzzleResult);
  const isNewBestTime = currentPuzzleTimeTaken === bestTime;

  const formattedBestTime = getFormattedTimeWithMS(bestTime);
  return (
    <View
      style={{
        flexDirection: 'row',
        gap: 13,
        width: '100%',
        paddingHorizontal: 16,
      }}
    >
      <View style={{ flex: 1 }}>
        <MetricStatCard
          label={isNewBestTime ? 'NEW BEST TIME' : 'YOUR BEST'}
          value={formattedBestTime}
          backgroundColor={isNewBestTime ? '#5A5975' : dark.colors.tertiary}
        />
      </View>
      <View style={{ flex: 1 }}>
        <StaticCoinGainedMetric coinsGained={coinsGained} />
      </View>
    </View>
  );
};

export default UserResultInfo;
