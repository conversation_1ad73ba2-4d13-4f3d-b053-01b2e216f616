import dark from 'core/constants/themes/dark';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import React, {useCallback, useRef} from 'react';
import {showToast, TOAST_TYPE} from 'molecules/Toast';
import {Platform, View} from 'react-native';
import useCaptureView from 'core/hooks/useCaptureView';
import _toUpper from 'lodash/toUpper';
import {PUZZLE_TYPES_VS_LABEL} from 'modules/puzzles/constants/puzzleConstants';
import PuzzleResultInfoCard from '../PuzzleResultInfoCard';
import styles from './DownloadPuzzleResultCard.style';

const DownloadPuzzleResultCard = ({puzzle, puzzleType}) => {
    const viewShotRef = useRef();
    const isWeb = Platform.OS === 'web';
    const {captureView} = useCaptureView();

    const puzzleTypeNameText = _toUpper(PUZZLE_TYPES_VS_LABEL[puzzleType]);

    const handleDownload = useCallback(() => {
        if (!isWeb || !viewShotRef.current) {
            return;
        }
        const fileName = `puzzle-result.png`;
        const message = `Here is my score for ${puzzleTypeNameText} puzzle`;

        captureView({
            viewRef: viewShotRef,
            message,
            fileName,
        }).catch(() => {
            showToast({
                type: TOAST_TYPE.ERROR,
                description: 'Failed to share profile. Please try again.',
            });
        });
    }, []);

    return (
        <View style={styles.container}>
            <View ref={viewShotRef}>
                <PuzzleResultInfoCard puzzle={puzzle} puzzleType={puzzleType}/>
            </View>
            <InteractiveSecondaryButton
                label="Download"
                buttonContainerStyle={{marginTop: 20, width: 270}}
                labelStyle={{
                    fontFamily: 'Montserrat-600',
                    fontSize: 14,
                    color: dark.colors.textLight,
                }}
                buttonBackgroundStyle={{paddingHorizontal: 16}}
                onPress={handleDownload}
            />
        </View>
    );
};

export default React.memo(DownloadPuzzleResultCard);
