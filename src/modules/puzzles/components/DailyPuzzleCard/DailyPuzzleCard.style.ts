import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { useMemo } from 'react';

const createStyles = (isCompactMode: boolean) =>
  StyleSheet.create({
    container: {
      borderRadius: 14,
      paddingLeft: 12,
      paddingRight: 16,
      paddingVertical: 10,
      backgroundColor: isCompactMode ? dark.colors.purpleLight : 'transparent',
      borderWidth: isCompactMode ? 0 : 0,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
      width: 260,
      height: 72,
    },
    infoText: {
      fontSize: 12,
      fontFamily: 'Montserrat-500',
      lineHeight: 12,
      color: dark.colors.textDark,
      maxWidth: 160,
      marginTop: 2,
    },
    imageContainer: {
      backgroundColor: isCompactMode ? '#3c3640' : dark.colors.primary,
      height: 44,
      width: 44,
      borderRadius: 8,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 0,
      borderColor: 'black',
      zIndex: 2,
    },
    overlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      borderRadius: 8,
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 5,
    },
    titleText: {
      fontFamily: 'Montserrat-800',
      color: dark.colors.textLight,
      opacity: 0.9,
      fontSize: 14,
    },
    date: {
      fontFamily: 'Montserrat-700',
      color: dark.colors.purpleLight2,
      fontSize: isCompactMode ? 14 : 12,
    },
    subTitle: {
      fontSize: 12,
      fontFamily: 'Montserrat-500',
      lineHeight: 12,
      color: dark.colors.textDark,
      maxWidth: 160,
    },
    contentContainer: {
      width: '100%',
      marginBottom: 0,
      justifyContent: 'center',
      alignItems: 'flex-start',
      gap: 6,
    },
  });

const useDailyPuzzleCardStyles = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(() => createStyles(isMobile), [isMobile]);

  return styles;
};

export default useDailyPuzzleCardStyles;
