import React, { useCallback, useMemo } from 'react';
import { Image, Pressable, Text, View } from 'react-native';
import { PUZZLE_DETAILS } from 'modules/puzzles/constants/puzzleConstants';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import Analytics from '@/src/core/analytics';
import { PAGE_NAME_KEY, PAGE_NAMES } from '@/src/core/constants/pageNames';
import { router } from 'expo-router';
import dark from '@/src/core/constants/themes/dark';
import MathMazeSVG from '@/src/components/svg/puzzles/MathMazeSVG';
import KenKenSVG from '@/src/components/svg/puzzles/KenKenSVG';
import CrossMathSVG from '@/src/components/svg/puzzles/CrossMathSVG';
import LinearGradient from 'atoms/LinearGradient';
import TextWithShadow from 'shared/TextWithShadow';
import PuzzleCardBackground from 'assets/images/puzzle/puzzleCardBackground.webp';
import allPuzzleAvgTimeReader from '@/src/core/readers/allPuzzleAvgTimeReader';
import { getFormattedTimeWithMS } from '@/src/core/utils/general';
import useGetPuzzleStatsByType from '../../hooks/queries/useGetPuzzleStatsByType';
import styles from './PuzzleDCTypeCard.style';
import getCurrentTimeWithOffset from '@/src/core/utils/getCurrentTimeWithOffset';
import { format } from 'date-fns';

const PuzzleDCTypeCard = ({ type }: { type: string }) => {
  const { puzzleTypeText } = PUZZLE_DETAILS[type];
  const today = new Date(getCurrentTimeWithOffset());
  const formattedTodaysDate = format(today, 'yyyy-MM-dd');

  const { data } = useGetPuzzleStatsByType(formattedTodaysDate);
  const kenKenAvgTime = allPuzzleAvgTimeReader.kenKenAvg(data);
  const crossMathAvgTime = allPuzzleAvgTimeReader.crossMathAvg(data);
  const mathMazeAvgTime = allPuzzleAvgTimeReader.mathMazeAvg(data);

  const onPressPuzzleTypeCard = useCallback(() => {
    const eventToBeTracked =
      ANALYTICS_EVENTS.CROSS_MATH_PUZZLE?.[type]
        ?.CLICKED_ON_SOLVE_PUZZLE_CARD_HOME_PAGE;

    Analytics.track(eventToBeTracked, {
      [PAGE_NAME_KEY]: PAGE_NAMES.PUZZLE_HOME_PAGE,
      puzzleType: type,
    });
    router.push(`/puzzle/daily-challenge?puzzleType=${type}`);
  }, [type]);

  const getPuzzleImage = useCallback(() => {
    switch (type) {
      case 'MathMaze':
        return <MathMazeSVG />;
      case 'KenKen':
        return <KenKenSVG />;
      case 'CrossMath':
        return <CrossMathSVG />;
      default:
        return <CrossMathSVG />;
    }
  }, [type]);

  const getAvgPuzzleTime = useMemo(() => {
    switch (type) {
      case 'MathMaze':
        return getFormattedTimeWithMS(mathMazeAvgTime);
      case 'KenKen':
        return getFormattedTimeWithMS(kenKenAvgTime);
      case 'CrossMath':
        return getFormattedTimeWithMS(crossMathAvgTime);
      default:
        return 0;
    }
  }, [type, mathMazeAvgTime, kenKenAvgTime, crossMathAvgTime]);

  const getPuzzleLabel = useCallback(
    () => (
      <TextWithShadow
        text={puzzleTypeText.toUpperCase()}
        textStyle={{
          color: dark.colors.textLight,
          fontFamily: 'Montserrat-italic-800',
          fontSize: 24,
          textAlign: 'center',
        }}
      />
    ),
    [puzzleTypeText],
  );

  const renderPuzzleCard = useCallback(
    () => (
      <View
        style={{
          borderRadius: 20,
          overflow: 'hidden',
          width: '100%',
          height: 150,
          position: 'relative',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderWidth: 1,
          backgroundColor: dark.colors.background,
        }}
      >
        <Image
          source={PuzzleCardBackground}
          resizeMode="cover"
          style={{
            position: 'absolute',
            top: '0%',
            left: '-30%',
            width: '175%',
            height: '100%',
            transform: [{ scale: 1 }],
          }}
        />
        <View
          style={{
            flex: 1,
            padding: 10,
            justifyContent: 'space-between',
            height: '100%',
            zIndex: 1,
          }}
        >
          <View style={styles.avgTimeContainer}>
            <Text
              style={{
                fontSize: 12,
                fontFamily: 'Montserrat-600',
                color: '#6A69CC',
              }}
            >
              AVG TIME: {getAvgPuzzleTime}
            </Text>
          </View>
          {getPuzzleLabel()}
        </View>
        <View style={{ zIndex: 1 }}>{getPuzzleImage()}</View>
      </View>
    ),
    [getAvgPuzzleTime, getPuzzleImage, getPuzzleLabel],
  );

  return (
    <Pressable style={styles.container} onPress={onPressPuzzleTypeCard}>
      {type === 'MathMaze' ? (
        <View>
          <View style={styles.newlyAdded}>
            <Text style={styles.newlyAddedLabel}>NEWLY ADDED</Text>
          </View>
          <LinearGradient
            colors={dark.colors.puzzleTabGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.gradientContainer}
          >
            {renderPuzzleCard()}
          </LinearGradient>
        </View>
      ) : (
        renderPuzzleCard()
      )}
    </Pressable>
  );
};

export default React.memo(PuzzleDCTypeCard);
