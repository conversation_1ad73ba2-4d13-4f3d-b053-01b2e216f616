import { PuzzleStatsType } from '../types/puzzleType';
import _get from 'lodash/get';

const puzzleStatsReader = {
  numOfSubmission: (stats: PuzzleStatsType): number | undefined =>
    _get(stats, 'numOfSubmission', 0),
  averageTime: (stats: PuzzleStatsType | null): number | undefined =>
    _get(stats, 'averageTime', 0),
  bestTime: (stats: PuzzleStatsType): number | undefined =>
    _get(stats, 'bestTime', 0),
};

export default puzzleStatsReader;
