import { gql, useLazyQuery, WatchQueryFetchPolicy } from '@apollo/client';
import _get from 'lodash/get';
import { USER_PUBLIC_DETAIL_FRAGMENT } from '@/src/core/graphql/fragments/userPublicDetail';

export const GET_MINIFIED_INSTITUTION_PUZZLE_LEADERBOARD = gql`
  ${USER_PUBLIC_DETAIL_FRAGMENT}
  query GetMinifiedInstitutionPuzzleLeaderboardById($puzzleId: ID!) {
    getMinifiedInstitutionPuzzleLeaderboardById(puzzleId: $puzzleId) {
      totalParticipants
      participants {
        timeSpent
        rank
        user {
          ...UserPublicDetailFields
        }
      }
    }
  }
`;

const useGetMinifiedInstitutionPuzzleLeaderboard = ({
  puzzleId,
  fetchPolicy = 'cache-first',
}: {
  puzzleId: string;
  fetchPolicy?: WatchQueryFetchPolicy;
}) => {
  const [
    fetchMinifiedInstitutionPuzzleLeaderboardQuery,
    { loading, error, data },
  ] = useLazyQuery(GET_MINIFIED_INSTITUTION_PUZZLE_LEADERBOARD, {
    fetchPolicy: fetchPolicy,
    notifyOnNetworkStatusChange: true,
    variables: {
      puzzleId,
    },
  });

  return {
    fetchMinifiedInstitutionPuzzleLeaderboardQuery,
    leaderboard: _get(data, 'getMinifiedInstitutionPuzzleLeaderboardById'),
    loading,
    error,
  };
};

export default useGetMinifiedInstitutionPuzzleLeaderboard;
