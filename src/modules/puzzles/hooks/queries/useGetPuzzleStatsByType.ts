import { gql, useQuery } from '@apollo/client';
import _get from 'lodash/get';

export const GET_PUZZLE_STATS_BY_TYPE = gql`
  query GetPuzzleStatsByType($date: String!) {
    getPuzzleStatsByType(date: $date) {
      puzzleType
      stats {
        averageTime
      }
    }
  }
`;

const useGetPuzzleStatsByType = (date: string) => {
  const { loading, error, data } = useQuery(GET_PUZZLE_STATS_BY_TYPE, {
    variables: { date },
    fetchPolicy: 'cache-first',
  });
  return {
    data: _get(data, 'getPuzzleStatsByType', 0),
    loading,
    error,
  };
};

export default useGetPuzzleStatsByType;
