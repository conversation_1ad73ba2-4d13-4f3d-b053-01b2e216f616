import Header from '@/src/components/shared/Header';
import React, { useCallback, useEffect, useRef } from 'react';
import { ScrollView, View } from 'react-native';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import useFetchUserByUserIdQuery from '@/src/core/hooks/useFetchUserByUserIdQuery';
import Loading from '@/src/components/atoms/Loading';
import _isNil from 'lodash/isNil';
import TextWithShadow from '@/src/components/shared/TextWithShadow';
import dark from '@/src/core/constants/themes/dark';
import TextWithDivider from '@/src/components/shared/TextWithDivider';
import InteractivePrimaryButton from '@/src/components/atoms/InteractivePrimaryButton';
import _map from 'lodash/map';
import _size from 'lodash/size';
import _get from 'lodash/get';
import { VALID_GAME_CATEGORIES_PLAY_WITH_FRIEND } from 'modules/home/<USER>/gameTypes';
import { GAME_TYPES_VS_LABEL } from '../../constants/gameModes';
import LobbyPlayerCard from '../../components/LobbyPlayerCard';
import useCreateLobbyConfig from '../../hooks/useCreateLobbyConfig';
import useCreateLobbyController from '../../hooks/useCreateLobbyController';
import useCreateLobbyStyles from './CreateLobby.style';

const CreateLobby = (props) => {
  const { user } = useSession();
  const friendId = props?.userId;
  const { fetchUserById, user: friend, loading } = useFetchUserByUserIdQuery();
  const hasFetchedRef = useRef(false);

  const styles = useCreateLobbyStyles();
  const {
    gameMode,
    gameType,
    timeConfig,
    updateGameMode,
    updateGameType,
    updateTimeConfig,
    gameTypeOptions,
    timeConfigOptions,
  } = useCreateLobbyConfig();

  const { sendChallengeRequest, isChallengingFriend } =
    useCreateLobbyController(friendId);
  useEffect(() => {
    if (friendId && !hasFetchedRef.current) {
      fetchUserById({ userId: friendId });
      hasFetchedRef.current = true;
    }
  }, [friendId, fetchUserById]);

  const handleCreateLobby = useCallback(() => {
    if (sendChallengeRequest) {
      sendChallengeRequest?.(gameType, timeConfig);
    }
  }, [sendChallengeRequest, gameType, timeConfig]);

  const renderGameModeOption = (mode) => {
    const isSelected = gameMode === mode;
    return (
      <InteractivePrimaryButton
        onPress={() => updateGameMode(mode)}
        label={mode}
        buttonStyle={[
          styles.buttonStyle,
          isSelected && { borderColor: dark.colors.secondary },
        ]}
        buttonContainerStyle={styles.buttonContainerStyle}
        labelStyle={[
          styles.buttonTextStyle,
          isSelected && { color: dark.colors.victoryColor },
        ]}
        buttonContentStyles={{ paddingVertical: 0 }}
        buttonBorderBackgroundStyle={[
          styles.buttonBorderBackgroundStyle,
          isSelected && { backgroundColor: dark.colors.secondary },
        ]}
      />
    );
  };

  const renderGameTypeOption = (type) => {
    const isSelected = gameType === type;
    const label = _get(GAME_TYPES_VS_LABEL, type);

    return (
      <InteractivePrimaryButton
        onPress={() => updateGameType(type)}
        label={label}
        buttonStyle={[
          styles.buttonStyle,
          isSelected && { borderColor: dark.colors.secondary },
        ]}
        buttonContainerStyle={styles.buttonContainerStyle}
        labelStyle={[
          styles.buttonTextStyle,
          isSelected && { color: dark.colors.victoryColor },
        ]}
        buttonContentStyles={{ paddingVertical: 0 }}
        buttonBorderBackgroundStyle={[
          styles.buttonBorderBackgroundStyle,
          isSelected && { backgroundColor: dark.colors.secondary },
        ]}
      />
    );
  };

  const renderTimeConfigOption = (option) => {
    const isSelected = timeConfig === option.key;
    return (
      <InteractivePrimaryButton
        key={option.key}
        onPress={() => updateTimeConfig(option.key)}
        label={option.label}
        buttonStyle={[
          styles.buttonStyle,
          isSelected && { borderColor: dark.colors.secondary },
        ]}
        buttonContainerStyle={styles.buttonContainerStyle}
        labelStyle={[
          styles.buttonTextStyle,
          isSelected && { color: dark.colors.victoryColor },
        ]}
        buttonContentStyles={{ paddingVertical: 0 }}
        buttonBorderBackgroundStyle={[
          styles.buttonBorderBackgroundStyle,
          isSelected && { backgroundColor: dark.colors.secondary },
        ]}
      />
    );
  };

  const renderSelectTimeSection = () => {
    if (_size(timeConfigOptions) === 0) {
      return null;
    }
    return (
      <>
        <TextWithDivider
          label="SELECT TIME"
          containerStyles={{ marginTop: 10 }}
        />
        <View style={styles.gameModeSelectorContainer}>
          {_map(timeConfigOptions, (option) => renderTimeConfigOption(option))}
        </View>
      </>
    );
  };

  if (!_isNil(friendId) && loading) {
    return <Loading label="Initializing Lobby" />;
  }

  return (
    <View style={styles.container}>
      <Header />
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.playersContainer}>
          <LobbyPlayerCard user={user} />
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'row',
            }}
          >
            <TextWithShadow
              text="VS"
              textStyle={styles.versusText}
              containerStyle={styles.versusContainerStyle}
              shadowColor={dark.colors.tertiary}
              shadowWidth={6}
              shadowOffsetX={0}
              shadowOffsetY={0}
              width={60}
              strokeWidth={4}
              strokeColor="#000000"
            />
          </View>
          <LobbyPlayerCard user={friend} />
        </View>
        <TextWithDivider
          label="SELECT GAME MODE"
          containerStyles={{ marginTop: 10 }}
        />
        <View style={styles.gameModeSelectorContainer}>
          {_map(VALID_GAME_CATEGORIES_PLAY_WITH_FRIEND, (mode) =>
            renderGameModeOption(mode),
          )}
        </View>
        <TextWithDivider
          label="SELECT GAME TYPE"
          containerStyles={{ marginTop: 10 }}
        />
        <View style={styles.gameModeSelectorContainer}>
          {_map(gameTypeOptions, (type) => renderGameTypeOption(type))}
        </View>
        {renderSelectTimeSection()}
      </ScrollView>
      <View style={styles.footer}>
        <InteractivePrimaryButton
          onPress={handleCreateLobby}
          label="CREATE LOBBY"
          buttonStyle={styles.playNowButton}
          buttonContainerStyle={styles.playNowButtonContainer}
          labelStyle={styles.playNowButtonText}
          buttonBorderBackgroundStyle={styles.playNowBackground}
        />
      </View>
    </View>
  );
};

export default React.memo(CreateLobby);
