import React from 'react';
import { View, Text, Image } from 'react-native';
import styles from './LobbyPlayerCard.style';
import UserImage from '@/src/components/atoms/UserImage';
import Icon, { ICON_TYPES } from '@/src/components/atoms/Icon';
import dark from '@/src/core/constants/themes/dark';

const LobbyPlayerCard = (props) => {
  const { user } = props;

  if(!user){
    return (
      <View style={[styles.container,{ paddingTop: 10}]}>
        <Icon name="user-circle" type={ICON_TYPES.FONT_AWESOME} size={42} color={dark.colors.tertiary}/>
        <Text style={styles.nameText}>??</Text>
      </View>
    );
  }
  const { username, rating } = user;
  
  return (
    <View style={styles.container}>
      <Text style={styles.ratingText}>{rating}</Text>
      <UserImage user={user} size={42} style={styles.userImage} />
      <Text style={styles.nameText}>{username}</Text>
    </View>
  );
};

export default React.memo(LobbyPlayerCard);
