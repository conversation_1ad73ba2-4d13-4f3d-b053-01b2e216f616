import React, {
  createContext,
  useCallback,
  useContext,
  useReducer,
} from 'react';

import {
  generateKenKen,
  Kenken,
  KenKenSettings,
} from '../../puzzles/utils/kenkenPuzzleGenerator';

interface KenKenPuzzleRushState {
  puzzles: Kenken[];
  currentPuzzleIndex: number;
  score: number;
  rushStartTime: number | null;
  rushEndTime: number | null;
  isLoading: boolean;
  isRushComplete: boolean;
  timePerPuzzle: number[];
}

const RushActionTypes = {
  START_RUSH: 'START_RUSH',
  PUZZLE_SOLVED: 'PUZZLE_SOLVED',
  END_RUSH: 'END_RUSH',
  SET_PUZZLES: 'SET_PUZZLES',
  GENERATION_FAILED: 'GENERATION_FAILED',
};

const rushReducer = (
  state: KenKenPuzzleRushState,
  action: { type: string; payload?: any },
): KenKenPuzzleRushState => {
  switch (action.type) {
    case RushActionTypes.SET_PUZZLES:
      return {
        ...state,
        puzzles: action.payload.puzzles,
        isLoading: false,
        currentPuzzleIndex: 0,
        rushStartTime: Date.now(),
        isRushComplete: action.payload.puzzles.length === 0,
        score: 0,
        timePerPuzzle: [],
        rushEndTime: action.payload.puzzles.length === 0 ? Date.now() : null,
      };
    case RushActionTypes.GENERATION_FAILED:
      return {
        ...state,
        puzzles: [],
        isLoading: false,
        isRushComplete: true,
        rushEndTime: Date.now(),
      };
    case RushActionTypes.PUZZLE_SOLVED: {
      if (state.isRushComplete) return state;

      const { timeSpent } = action.payload;
      const newTimePerPuzzle = [...state.timePerPuzzle];
      newTimePerPuzzle[state.currentPuzzleIndex] = timeSpent;

      const isLastPuzzle = state.currentPuzzleIndex >= state.puzzles.length - 1;
      const nextIndex = isLastPuzzle
        ? state.currentPuzzleIndex
        : state.currentPuzzleIndex + 1;
      const rushComplete = isLastPuzzle;
      const endTime = rushComplete ? Date.now() : state.rushEndTime;

      return {
        ...state,
        score: state.score + 1,
        currentPuzzleIndex: nextIndex,
        isRushComplete: rushComplete,
        rushEndTime: endTime,
        timePerPuzzle: newTimePerPuzzle,
      };
    }
    case RushActionTypes.END_RUSH:
      if (state.isRushComplete) return state;
      return {
        ...state,
        isRushComplete: true,
        rushEndTime: Date.now(),
      };
    default:
      return state;
  }
};

interface KenKenPuzzleRushContextValue {
  state: KenKenPuzzleRushState;
  dispatch: React.Dispatch<{ type: string; payload?: any }>;
  generatePuzzles: (count: number, settings?: Partial<KenKenSettings>) => void;
}

const KenKenPuzzleRushContext = createContext<
  KenKenPuzzleRushContextValue | undefined
>(undefined);

interface KenKenPuzzleRushProviderProps {
  children: React.ReactNode;
}

export const KenKenPuzzleRushContextProvider: React.FC<
  KenKenPuzzleRushProviderProps
> = ({ children }) => {
  const initialState: KenKenPuzzleRushState = {
    puzzles: [],
    currentPuzzleIndex: 0,
    score: 0,
    rushStartTime: null,
    rushEndTime: null,
    isLoading: true,
    isRushComplete: false,
    timePerPuzzle: [],
  };

  const [state, dispatch] = useReducer(rushReducer, initialState);

  const generatePuzzles = useCallback(
    (count: number, settings: Partial<KenKenSettings> = { size: 4 }) => {
      try {
        const generatedPuzzles = Array.from({ length: count }, (_, i) =>
          generateKenKen({
            ...settings,
            seed: Date.now() + i * Math.random() * 1000,
          }),
        );
        dispatch({
          type: RushActionTypes.SET_PUZZLES,
          payload: { puzzles: generatedPuzzles },
        });
      } catch (error) {
        dispatch({ type: RushActionTypes.GENERATION_FAILED });
      }
    },
    [],
  );

  const value = { state, dispatch, generatePuzzles };

  return (
    <KenKenPuzzleRushContext.Provider value={value}>
      {children}
    </KenKenPuzzleRushContext.Provider>
  );
};

export const useKenKenPuzzleRush = () => {
  const context = useContext(KenKenPuzzleRushContext);
  if (!context) {
    throw new Error(
      'useKenKenPuzzleRush must be used within a KenKenPuzzleRushContextProvider',
    );
  }
  return context;
};
