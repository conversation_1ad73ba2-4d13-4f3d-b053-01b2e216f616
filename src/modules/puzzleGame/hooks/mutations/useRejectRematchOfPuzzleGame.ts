import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';
import _toString from 'lodash/toString';

const REJECT_REMATCH_MUTATION = gql`
  mutation RejectRematchForPuzzleGame($gameId: ID!) {
    rejectRematchOfPuzzleGame(gameId: $gameId)
  }
`;

const useRejectRematchRequestOfPuzzleGame = () => {
  const [rejectRematchOfPuzzleGameQuery, { loading }] = useMutation(
    REJECT_REMATCH_MUTATION,
  );

  const rejectRematchRequestOfPuzzleGame = useCallback(
    ({ gameId }: { gameId: string }) =>
      rejectRematchOfPuzzleGameQuery({
        variables: {
          gameId: _toString(gameId),
        },
      }),
    [rejectRematchOfPuzzleGameQuery],
  );

  return {
    rejectRematchRequestOfPuzzleGame,
    isRejectingRematch: loading,
  };
};

export default useRejectRematchRequestOfPuzzleGame;
