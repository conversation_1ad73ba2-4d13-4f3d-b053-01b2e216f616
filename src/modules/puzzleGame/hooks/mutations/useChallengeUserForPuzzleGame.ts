import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';
import { PUZZLE_GAME_FRAGMENT } from 'core/graphql/fragments/puzzleGame';
import {
  PUZZLE_GAME_DEFAULT_TIME_CONFIG,
  PUZZLE_GAME_TYPES,
} from 'modules/home/<USER>/puzzleGameTypes';

const CHALLENGE_USER_FOR_PUZZLE_GAME = gql`
  ${PUZZLE_GAME_FRAGMENT}
  mutation ChallengeUserForPuzzleGame(
    $challengeUserInput: ChallengeUserForPuzzleGameInput
  ) {
    challengeUserForPuzzleGame(challengeUserInput: $challengeUserInput) {
      ...CorePuzzleGameFields
    }
  }
`;
const useChallengeUserForPuzzleGame = () => {
  const [challengeUserForPuzzleGameQuery] = useMutation(
    CHALLENGE_USER_FOR_PUZZLE_GAME,
  );

  const challengeUserForPuzzleGame = useCallback(
    async ({ userId, gameConfig }: { userId: string; gameConfig?: any }) => {
      const selectedGameConfig = gameConfig ?? {
        timeLimit: PUZZLE_GAME_DEFAULT_TIME_CONFIG,
        numPlayers: 2,
        gameType: PUZZLE_GAME_TYPES.CROSS_MATH_PUZZLE_WITH_FRIEND,
      };

      const response = await challengeUserForPuzzleGameQuery({
        variables: {
          challengeUserInput: {
            userId,
            gameConfig: selectedGameConfig,
          },
        },
      });

      return response;
    },
    [challengeUserForPuzzleGameQuery],
  );

  return {
    challengeUserForPuzzleGame,
  };
};

export default useChallengeUserForPuzzleGame;
