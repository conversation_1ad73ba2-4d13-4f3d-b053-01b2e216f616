import { useMutation, gql } from '@apollo/client';
import { useCallback } from 'react';
import _toString from 'lodash/toString';

const REQUEST_REMATCH__FOR_PUZZLE_GAME_MUTATION = gql`
  mutation RequestRematchForPuzzleGame($gameId: ID!) {
    requestRematchForPuzzleGame(gameId: $gameId)
  }
`;

const useRequestRematchForPuzzleGame = () => {
  const [requestRematchForPuzzleGameQuery, { loading }] = useMutation(
    REQUEST_REMATCH__FOR_PUZZLE_GAME_MUTATION,
  );

  const requestRematchForPuzzleGame = useCallback(
    ({ gameId }: { gameId: string }) =>
      requestRematchForPuzzleGameQuery({
        variables: {
          gameId: _toString(gameId),
        },
      }),
    [requestRematchForPuzzleGameQuery],
  );

  return {
    requestRematchForPuzzleGame,
    isRequestingForRematch: loading,
  };
};

export default useRequestRematchForPuzzleGame;
