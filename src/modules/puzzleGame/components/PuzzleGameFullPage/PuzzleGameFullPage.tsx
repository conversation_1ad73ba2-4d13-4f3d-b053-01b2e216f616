import React, { useCallback } from 'react';

import { View } from 'react-native';
import { Text } from '@rneui/themed';
import SecondaryButton from 'atoms/SecondaryButton';
import { useRouter } from 'expo-router';
import styles from './PuzzleGameFullPage.style';

const PuzzleGameFullPage = () => {
  const router = useRouter();

  const handleGoToHome = useCallback(() => {
    router.replace('/home');
  }, [router]);

  return (
    <View style={styles.container}>
      <Text style={styles.errorMessage}>
        Game is already full and You are not the part of this game.
      </Text>
      <SecondaryButton
        label="Create a new puzzle game"
        onPress={handleGoToHome}
        buttonStyle={styles.buttonStyle}
      />
    </View>
  );
};

export default React.memo(PuzzleGameFullPage);
