import dark from 'core/constants/themes/dark';
import { StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';

const createStyles = (isCompactMode: boolean) =>
  StyleSheet.create({
    footer: {
      width: '100%',
      flexDirection: 'column',
      marginTop: 0,
      bottom: 0,
    },

    footerContainer: {
      height: 60,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingBottom: 10,
    },
    bottomButtonLabel: {
      fontSize: 12,
      fontFamily: 'Montserrat-700',
      color: 'white',
      alignSelf: 'center',
      opacity: 0.9,
    },
    bottomButtonStyle: {
      flex: 1,
      backgroundColor: dark.colors.card,
      paddingHorizontal: 8,
      minWidth: 150,
      alignItems: 'center',
      justifyContent: 'center',
      borderColor: dark.colors.secondary,
      borderWidth: 0.5,
      maxWidth: 160,
      borderRadius: 12,
    },
    bottomButtonBackgroundStyle: {
      flex: 1,
      minWidth: 140,
      backgroundColor: dark.colors.secondary,
      opacity: 0.2,
      borderRadius: 12,
    },
  });

const useCrossMathPuzzleGameResultFooterStyles = () => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const styles = useMemo(() => createStyles(isCompactMode), [isCompactMode]);
  return styles;
};

export default useCrossMathPuzzleGameResultFooterStyles;
