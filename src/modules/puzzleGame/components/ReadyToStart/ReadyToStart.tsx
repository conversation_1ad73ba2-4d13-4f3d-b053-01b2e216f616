import React, { useCallback, useMemo } from 'react';
import { View } from 'react-native';
import _size from 'lodash/size';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { Button, Text } from '@rneui/themed';
import { PuzzleGame } from 'modules/puzzleGame/types/puzzleGame';
import WaitingForGameCreatorToStart from 'modules/game/pages/WaitingToStart';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import useStartPuzzleGame from 'modules/puzzleGame/hooks/mutations/useStartPuzzleGame';
import LinearGradientButton from 'atoms/LinearGradientButton';
import styles from './ReadyToStart.style';
import { PUZZLE_GAME_STATUS } from '../../constants/puzzleGame';
import useLeavePuzzleGame from '../../hooks/mutations/useLeavePuzzleGame';
import PuzzleGameLobbyPlayerCards from '../PuzzleGameLobbyPlayerCards/PuzzleGameLobbyPlayerCards';

const PlayWithFriendReadyToStart = ({ game }: { game: PuzzleGame }) => {
  const { players, createdBy, gameStatus, _id: gameId } = game ?? EMPTY_OBJECT;

  const { userId } = useSession();

  const isGameOwner = useMemo(
    () =>
      gameStatus === PUZZLE_GAME_STATUS.READY &&
      _size(players) === 2 &&
      userId === createdBy,
    [gameStatus, userId, players, createdBy],
  );

  const { leavePuzzleGame } = useLeavePuzzleGame();
  const { startPuzzleGame } = useStartPuzzleGame();

  const onPressLeaveGame = () => {
    leavePuzzleGame({ gameId });
  };

  const onPressStartGame = useCallback(() => {
    startPuzzleGame(gameId);
    // TODO: ADD ANALYTICS
    showToast({
      type: TOAST_TYPE.LOADING,
      description: 'Starting Game',
    });
  }, [startPuzzleGame, gameId]);

  const renderStartButtonOrWaitingAnimatedText = useCallback(() => {
    if (isGameOwner) {
      return (
        <View style={styles.startGameContainer}>
          <LinearGradientButton
            label="Start Game"
            onPress={onPressStartGame}
            buttonStyle={styles.buttonBox}
            labelStyle={{}}
          />
        </View>
      );
    }
    return <WaitingForGameCreatorToStart game={game} />;
  }, [game, isGameOwner, onPressStartGame]);

  return (
    <View style={styles.container}>
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          <PuzzleGameLobbyPlayerCards />
          {renderStartButtonOrWaitingAnimatedText()}
        </View>
      </View>
      <Button type="clear" onPress={onPressLeaveGame}>
        <Text style={styles.leaveGameStyle}>Leave game</Text>
      </Button>
    </View>
  );
};

export default React.memo(PlayWithFriendReadyToStart);
