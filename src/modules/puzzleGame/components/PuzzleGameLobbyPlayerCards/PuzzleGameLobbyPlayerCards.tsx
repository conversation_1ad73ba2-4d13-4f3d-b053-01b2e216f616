import React, { useMemo, useState } from 'react';
import { Image, View } from 'react-native';
import { Text } from '@rneui/themed';
import vsImage from '@/assets/images/LinearGradientIcons/vs.png';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Dark from '@/src/core/constants/themes/dark';
import _toNumber from 'lodash/toNumber';
import { GAME_TYPES } from 'modules/home/<USER>/gameTypes';
import { useSession } from 'modules/auth/containers/AuthProvider';
import styles from './PuzzleGameLobbyPlayerCards.style';
import Card from '../usercard/UserCard';
import usePuzzleGameContext from '../../hooks/usePuzzleGameContext';

const PuzzleGameLobbyPlayerCards = () => {
  const { players, game } = usePuzzleGameContext();

  const { gameType } = game ?? EMPTY_OBJECT;

  const timeLimit = game?.config?.timeLimit ?? 0;

  const { user } = useSession();

  const [cardDimensions, setCardDimensions] = useState({
    width: 0,
    height: 0,
  });

  const handleLayout = (event: any) => {
    const { width: parentWidth } = event.nativeEvent.layout;
    const cardWidth = Math.min(Math.max(parentWidth * 0.32, 90), 100);
    const cardHeight = cardWidth * 1.1;
    setCardDimensions({ width: cardWidth, height: cardHeight });
  };

  return (
    <View style={styles.cont}>
      <View style={styles.container} onLayout={handleLayout}>
        <View
          style={{ width: cardDimensions.width, height: cardDimensions.height }}
        >
          <Card
            user={players ? players[0] : user}
            key={players ? players[0]?._id : user._id}
            gameType={gameType}
          />
        </View>
        <View style={styles.vsContainer}>
          <Image source={vsImage} style={styles.vsImage} />

          <View style={{ flexDirection: 'row' }}>
            <MaterialIcons
              name="timer"
              color={Dark.colors.textDark}
              size={20}
            />
            <Text style={styles.gameTime}> {_toNumber(timeLimit) / 60}:00</Text>
          </View>
        </View>
        <View
          style={{ width: cardDimensions.width, height: cardDimensions.height }}
        >
          <Card
            user={players?.[1]}
            key={players?.[1]?._id}
            gameType={gameType}
          />
        </View>
      </View>
    </View>
  );
};

export default React.memo(PuzzleGameLobbyPlayerCards);
