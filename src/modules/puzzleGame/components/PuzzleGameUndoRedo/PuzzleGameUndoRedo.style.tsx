import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  button: {
    flex: 1,
    width: '100%',
    maxWidth: 160,
    height: 32,
    borderRadius: 16,
    backgroundColor: dark.colors.cardBackground,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontSize: 14,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textDark,
  },
});

export default styles;
