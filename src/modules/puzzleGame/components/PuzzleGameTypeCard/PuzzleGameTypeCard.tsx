import dark from '@/src/core/constants/themes/dark';
import React from 'react';
import { ImageBackground, Text, View } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { router } from 'expo-router';
import Rive from 'atoms/Rive';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';

const PuzzleGameTypeCard = () => {
  const onPressCreatePuzzleGame = () => {
    router.push('/puzzle/search-opponent');
  };

  return (
    <TouchableOpacity onPress={onPressCreatePuzzleGame}>
      <ImageBackground
        source={require('assets/images/puzzle/puzzleResultBackground.png')}
        style={{
          width: 158,
          height: 205,
          paddingHorizontal: 8,
          paddingVertical: 12,
          borderRadius: 12,
          overflow: 'hidden',
          gap: 20,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Rive
          url={RIVE_ANIMATIONS.PUZZLE_DUELS_ANIMATION}
          autoPlay
          style={{ width: 120, height: 120 }}
        />
        <View style={{ gap: 6 }}>
          <Text
            style={{
              fontSize: 14,
              fontFamily: 'Montserrat-600',
              color: 'white',
              textAlign: 'center',
            }}
          >
            Cross Math
          </Text>
          <Text
            style={{
              fontSize: 8,
              fontFamily: 'Montserrat-600',
              letterSpacing: 2,
              textAlign: 'center',
              color: dark.colors.textDark,
            }}
          >
            BASIC MATH PUZZLES
          </Text>
        </View>
      </ImageBackground>
    </TouchableOpacity>
  );
};

export default React.memo(PuzzleGameTypeCard);
