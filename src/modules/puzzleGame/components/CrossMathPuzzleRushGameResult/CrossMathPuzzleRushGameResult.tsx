import React, { useCallback, useEffect, useState } from 'react';
import { Dimensions, Text, View } from 'react-native';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from 'atoms/Icon';
import dark from 'core/constants/themes/dark';
import BackToHomeButton from 'molecules/BackToHomeButton';
import MetricStatCard from 'shared/MetricStatCard/MetricStatCard';
import { withOpacity } from 'core/utils/colorUtils';
import StaticCoinGainedMetric from 'shared/StaticCoinGainedMetric';
import useNativeUrlSharing from 'core/hooks/useNativeUrlSharing';
import Rive from 'atoms/Rive';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';
import _toNumber from 'lodash/toNumber';
import useSound from 'core/hooks/useSound';
import celebrationSoundEffect from 'assets/audio/celebrationSoundEffect.wav';
import styles from './CrossMathPuzzleRushGameResult.style';

interface CrossMathPuzzleRushGameResultProps {
  score: number;
  isNewBestScore: boolean;
  bestScore: number;
  coinsGained: number;
  resetGame: () => void;
}

const CrossMathPuzzleRushGameResult = ({
  score,
  isNewBestScore,
  bestScore,
  coinsGained,
  resetGame,
}: CrossMathPuzzleRushGameResultProps) => {
  const puzzleRushUrl = `https://www.matiks.com/puzzle/rush}`;
  const { handleShare } = useNativeUrlSharing({ url: puzzleRushUrl });
  const [showCelebrationAnimation, setShowCelebrationAnimation] =
    useState(isNewBestScore);

  useSound({
    soundFile: celebrationSoundEffect,
    playOnMount: isNewBestScore,
    config: {
      volume: 0.2,
    },
  });

  const onPressShareButton = useCallback(() => {
    const label = isNewBestScore
      ? `I've made a new high score in cross math puzzle rush with a score of ${score}. Beat me on Matiks! 🏆`
      : `Well played in Cross Math Puzzle Rush! 💪 With Score of ${score} 🥳, Think you can do better? Prove me on `;

    handleShare({
      label,
      clipboardLabel: 'Copied Puzzle rush link to clipboard',
    });
  }, [handleShare, isNewBestScore, score]);

  const riveAnimationWidth = Math.min(
    _toNumber(Dimensions.get('window').width),
    400,
  );

  useEffect(() => {
    if (isNewBestScore) {
      setTimeout(() => {
        setShowCelebrationAnimation(false);
      }, 4500);
    }
  }, []);

  return (
    <View style={styles.container}>
      <View style={{ maxWidth: 400, flex: 1, width: '100%' }}>
        {showCelebrationAnimation && (
          <Rive
            url={RIVE_ANIMATIONS.CONFETTI_ANIMATION}
            autoPlay
            loop={false}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              width: riveAnimationWidth,
              height: 400,
            }}
          />
        )}
        <View style={styles.headerContainer}>
          <BackToHomeButton />
          <InteractiveSecondaryButton
            onPress={onPressShareButton}
            iconConfig={{
              name: 'share',
              type: ICON_TYPES.ENTYPO,
              color: dark.colors.secondaryButtonBorder,
              size: 20,
            }}
            buttonContainerStyle={{ width: 36, height: 36 }}
          />
        </View>

        <View
          style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
        >
          <Text style={styles.scoreText}>{score}</Text>
          <Text style={styles.puzzlesSolvedText}>SCORE</Text>
        </View>

        <View style={{ gap: 18, paddingBottom: 25 }}>
          <View style={{ flexDirection: 'row', gap: 13, width: '100%' }}>
            <View style={{ flex: 1 }}>
              <MetricStatCard
                label={isNewBestScore ? 'NEW BEST SCORE' : 'BEST SCORE'}
                value={bestScore}
                backgroundColor={
                  isNewBestScore
                    ? withOpacity(dark.colors.puzzle.primary, 0.4)
                    : dark.colors.tertiary
                }
              />
            </View>
            <View style={{ flex: 1 }}>
              <StaticCoinGainedMetric coinsGained={coinsGained} />
            </View>
          </View>
          <View style={{ width: '100%' }}>
            <InteractiveSecondaryButton
              label="PLAY AGAIN"
              labelStyle={{ fontSize: 12 }}
              onPress={resetGame}
              borderColor={dark.colors.puzzle.primary}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

export default React.memo(CrossMathPuzzleRushGameResult);
