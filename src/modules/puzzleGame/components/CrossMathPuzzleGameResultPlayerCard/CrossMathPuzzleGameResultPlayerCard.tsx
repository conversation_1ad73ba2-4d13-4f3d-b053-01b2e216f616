import React from 'react';
import { View, Image, StyleSheet, Text } from 'react-native';
import rec1 from '@/assets/images/Rectangle5.png';
import rec2 from '@/assets/images/Rectangle6.png';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import UserImage from 'atoms/UserImage/UserImage';
import _isEmpty from 'lodash/isEmpty';
import userReader from '@/src/core/readers/userReader';
import { GAME_TYPES } from '@/src/modules/home/<USER>/gameTypes';
import _truncate from 'lodash/truncate';
import styles from './CrossMathPuzzleGameResultPlayerCard.style';

const CrossMathPuzzleGameResultPlayerCard = ({ user }: { user: any }) => {
  const { user: currentUser } = useSession();
  const isCurrentUser = user?._id === currentUser?._id;
  const { score, isWinner } = user;
  const username = _truncate(userReader.username(user), {
    length: 10,
    omission: '...',
  });

  if (_isEmpty(user)) {
    return null;
  }

  const userRating = userReader.puzzleRating(user);

  return (
    <View
      style={[
        styles.background,
        isWinner
          ? isCurrentUser
            ? styles.winnerContainer
            : styles.loserContainer
          : styles.defaultContainer,
      ]}
    >
      <View style={{ borderRadius: 14, minHeight: 108 }}>
        <Image
          source={rec1}
          style={[styles.image1, !isCurrentUser && { opacity: 0 }]}
        />
        <Image
          source={rec2}
          style={[styles.image2, !isCurrentUser && { opacity: 0 }]}
        />
        <View style={styles.card}>
          <UserImage size={34} user={user} />
          <View style={styles.userInfo}>
            <Text style={styles.userName}>{username}</Text>
            <Text style={styles.userScore}>({userRating})</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default React.memo(CrossMathPuzzleGameResultPlayerCard);
