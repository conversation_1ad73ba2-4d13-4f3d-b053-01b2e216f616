import React from 'react';
import { Text, View } from 'react-native';
import _padStart from 'lodash/padStart';
import { isToday } from 'date-fns';
import dark from '@/src/core/constants/themes/dark';
import userReader from '@/src/core/readers/userReader';
import styles from './MessageListItem.style';
import { Message } from 'modules/chatV2/types/messages';
import MessageContent from './MessageContent';
import FallbackImage from '@/src/components/atoms/helpers/fallbackImage';
import dummyAvatar from '@/assets/images/avatar/dummy.png';

const getTime = (time: Date) => {
  const date = new Date(time);

  const hours = _padStart(date.getHours().toString(), 2, '0');
  const minutes = _padStart(date.getMinutes().toString(), 2, '0');
  if (isToday(date)) {
    return `${hours}:${minutes}`;
  }
  return `${date.getDate()} ${date.toLocaleString('default', { month: 'short' })}, ${hours}:${minutes}`;
};

const MessageItem = ({
  item,
  userId,
  showSenderInfo,
}: {
  item: Message;
  userId: string;
  showSenderInfo: boolean;
}) => {
  const isSender = item?.sender === userId;
  const needToShowSenderInfo = !isSender && showSenderInfo;

  return (
    <View style={styles.container}>
      {needToShowSenderInfo ? (
        <FallbackImage
          source={{ uri: item?.senderInfo?.profileImageUrl }}
          fallbackSource={dummyAvatar}
          style={styles.headerAvatar}
        />
      ) : null}
      <View
        style={[
          styles.messageWrapper,
          isSender ? styles.sentMessage : styles.receivedMessage,
        ]}
      >
        <View
          style={[
            styles.messageContainer,
            isSender ? { marginLeft: 'auto' } : { marginRight: 'auto' },
          ]}
        >
          <View
            style={[
              styles.messageBubble,
              isSender
                ? { backgroundColor: dark.colors.primary }
                : { backgroundColor: dark.colors.gradientBackground },
            ]}
          >
            {showSenderInfo ? (
              <Text style={styles.senderInfoText}>
                {isSender ? 'You' : userReader.username(item?.senderInfo)}
              </Text>
            ) : null}
            <MessageContent content={item?.content} />
            <Text style={styles.messageTime}>{getTime(item?.createdAt)}</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default React.memo(MessageItem);
