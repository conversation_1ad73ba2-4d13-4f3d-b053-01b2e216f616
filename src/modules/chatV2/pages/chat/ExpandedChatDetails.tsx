import { View, Text, Image } from 'react-native';
import React, { useCallback } from 'react';
import ExpandedPeopleList from 'modules/chatV2/components/PeopleList/ExpandedPeopleList';
import MessageList from 'modules/chatV2/components/MessageList';
import useEscKey from 'core/hooks/useEscKey';
import { useRouter } from 'expo-router';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Dark from '@/src/core/constants/themes/dark';
import noFriendsPookie from '@/assets/images/pookie/no_friends_pookie.png';
import useChatStore from 'store/useChatStore';
import Loading from 'atoms/Loading';
import { expandedStyles } from './style';

const ChatUnselectedEmptyState = React.memo(() => {
  const { isMessageGroupsLoading } = useChatStore((state) => ({
    isMessageGroupsLoading: state.isMessageGroupsLoading,
  }));

  if (isMessageGroupsLoading) {
    return <Loading />;
  }
  return (
    <View style={expandedStyles.emptyStateContainer}>
      <View style={expandedStyles.imageContainer}>
        <Image source={noFriendsPookie} style={expandedStyles.emptyImage} />
      </View>

      <Text style={expandedStyles.emptyTitle}>No Chat Selected</Text>

      <View style={expandedStyles.subtitleContainer}>
        <Text style={expandedStyles.emptySubtitle}>
          Select a conversation to start chatting with your friends
        </Text>
      </View>
      <View style={expandedStyles.actionHint}>
        <MaterialIcons
          name="arrow-back"
          size={16}
          color={Dark.colors.textDark}
        />
        <Text style={expandedStyles.actionHintText}>
          Select a chat to begin
        </Text>
      </View>
    </View>
  );
});

const ExpandedChatDetails = ({ groupId }: { groupId: string }) => {
  const router = useRouter();

  useEscKey(
    useCallback(() => {
      router.replace('/chat');
    }, [router]),
  );

  const renderMessageList = useCallback(() => {
    if (!groupId) {
      return <ChatUnselectedEmptyState />;
    }
    return (
      <View style={expandedStyles.messageListContainer}>
        <MessageList currentGroupId={groupId} />
      </View>
    );
  }, [groupId]);

  return (
    <View style={expandedStyles.expandedWrapper}>
      <View style={expandedStyles.expandedContainer}>
        <ExpandedPeopleList />
        {renderMessageList()}
      </View>
    </View>
  );
};

export default React.memo(ExpandedChatDetails);
