type Attachment = {
  type: string;
  url: string;
};

export interface UserDetailsForMessage {
  _id: string;
  name: string;
  username: string;
  profileImageUrl: string;
  rating: number;
}

export interface Message {
  _id: string;
  groupId: string;
  sender: string; // Need to make it obj of sender details
  senderId: string;
  content: string;
  createdAt: Date;
  attachment?: Attachment;
  senderInfo: UserDetailsForMessage
}

export type CreateMessageInput = {
  _id: string;
  groupId: string;
  sender: string;
  content: string;
  attachment?: Attachment;
  createdAt: Date;
};

const randomId = () => Math.random().toString(36).substring(2, 12);
const randomContent = () => [
  'Hello!',
  'How are you?',
  "Let's meet up!",
  'Check this out.',
  'Good morning!',
  'See you soon.',
  'What do you think?',
  'Interesting!',
  'Nice!',
  'Great job!',
  'Can you help me?',
  "I'll be there.",
  'No worries.',
  "Let's do it.",
  'Awesome!',
  'Looking forward to it.',
  'Congrats!',
  'Take care.',
  'See you later!',
  'Have a nice day!',
  'Stay safe!',
  'Sounds good.',
  'On my way.',
  "I'll call you later.",
  'Good luck!',
  'Let me know.',
  'Sure!',
  'Thank you!',
  "That's amazing!",
  'Good night!',
];

const randomAttachment = (): Attachment | undefined => {
  if (Math.random() > 0.7) {
    return {
      type: ['image', 'video', 'document'][Math.floor(Math.random() * 3)],
      url: `https://example.com/${randomId()}`,
    };
  }
  return undefined;
};

export const generateMessages = (count: number): Message[] =>
  Array.from({ length: count }, () => ({
    _id: randomId(),
    groupId: randomId(),
    sender: `user_${randomId()}`,
    content:
      randomContent()[Math.floor(Math.random() * randomContent().length)],
    createdAt: new Date(Date.now() - Math.floor(Math.random() * 1e10)),
    attachment: randomAttachment(),
  })).sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
