import useChatStore from 'store/useChatStore';
import { useCallback, useRef } from 'react';
import _trim from 'lodash/trim';
import { ChatState, MESSAGE_SOURCE_TYPE } from 'store/useChatStore/types';
import randomId from 'lodash/uniqueId';
import useWebsocketStore from 'store/useWebSocketStore';
import { WEBSOCKET_CHANNELS } from 'core/constants/websocket';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { CreateMessageInput } from '../types/messages';

const useSendMessage = (currentGroupId: string) => {
  const { userId }: any = useSession();
  const { addMessage } = useChatStore((state: ChatState) => ({
    addMessage: state.addMessage,
  }));

  const _sendMessage = useWebsocketStore((state) => state.sendMessage);

  const _sendMessageRef = useRef(_sendMessage);
  _sendMessageRef.current = _sendMessage;
  const _addMessageRef = useRef(addMessage);
  _addMessageRef.current = addMessage;

  const sendMessage = useCallback(
    (message: string) => {
      const channel = WEBSOCKET_CHANNELS.UserEvents(userId);

      if (_trim(message).length === 0) {
        return;
      }
      const MessagePayload: CreateMessageInput = {
        _id: `${Date.now().toString()}_${randomId()}`,
        content: _trim(message),
        groupId: currentGroupId ?? 'EMPTY_ID',
        sender: userId,
        createdAt: new Date(),
      };
      _addMessageRef.current(
        MessagePayload,
        currentGroupId,
        MESSAGE_SOURCE_TYPE.SELF,
      );
      _sendMessageRef.current?.({
        type: 'addMessage',
        channel,
        data: {
          ...MessagePayload,
        },
      });
    },
    [currentGroupId, userId],
  );

  return { sendMessage };
};

export default useSendMessage;
