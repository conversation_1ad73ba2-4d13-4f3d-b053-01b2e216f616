import React, { use<PERSON>allback, useMemo, useState } from 'react';
import { View, Text, Dimensions, ScrollView, Image } from 'react-native';
import { TabView, TabBar } from 'react-native-tab-view';
import DarkTheme from 'core/constants/themes/dark';
import dark from '../../../core/constants/themes/dark';
import styles from './ShowdownExpandedTabBar.style';
import _isEmpty from 'lodash/isEmpty';
import Analytics from '../../../core/analytics';
import { PAGE_NAME_KEY, PAGE_NAMES } from '../../../core/constants/pageNames';
import ContestTabBarShimmer from '../../contest/shimmers/ContestDetailTabBarShimmer';
import { getShowdownPropertiesToTrack } from '../utils/showdownEvents';
import { TAB_KEY_VS_EVENT, TAB_KEYS } from '../constants/showdownDetails';
import { ACCORDIAN } from '../constants';
import isEmpty from 'lodash/isEmpty';
import ExpandableContainer from './ExpandableContainer';
import useShowdownFixtures from '../hooks/query/useFixturesDetails';
import ShowdownLeaderboard from './ShowdownLeaderboard';

import useShowdownLeaderboardController from '../hooks/useShowdownLeaderboardController';
import Fixtures from './Fixtures';
import showdownReader from '../readers/showdownReader';
import _get from 'lodash/get';

const DetailsRoute = React.memo(({ showdown }) => {
  const [activeAccordian, setActiveAccordian] = useState('');
  const changeActiveAccordian = (accordian) => {
    setActiveAccordian((prev) => {
      if (prev === accordian) {
        return '';
      }
      return accordian;
    });
  };
  return (
    <ScrollView
      style={styles.mainContainer}
      showsVerticalScrollIndicator={false}
    >
      {!isEmpty(ACCORDIAN)
        ? ACCORDIAN.map((accordian, index) => {
            const accordianContent = _get(
              showdown,
              ['details', accordian.key],
              '',
            );
            return (
              <ExpandableContainer
                key={`SHOWDOWN_DETAIL_${index}`}
                title={accordian?.title}
                isActive={activeAccordian === accordian?.title}
                content={accordianContent}
                changeActiveAccordian={changeActiveAccordian}
              />
            );
          })
        : null}
    </ScrollView>
  );
});

const initialLayout = { width: Dimensions.get('window').width, elevation: 0 };

const ShowdownTabBar = React.memo(
  ({ state, onAction, fixtures, fixturesLoading }) => {
    const { isLive, hasUserRegistered, hasEnded, showdown, currentRound } =
      state;
    const [index, setIndex] = useState(0);

    const showdownId = showdownReader.id(showdown);

    const { leaderboard } = useShowdownLeaderboardController({
      showdownId,
      page: 1,
    });

    const routes = useMemo(() => {
      const availableRoutes = [];
      if ((!_isEmpty(fixtures) || isLive) && !hasEnded && hasUserRegistered) {
        availableRoutes.push({ key: TAB_KEYS.FIXTURES, title: 'Fixtures' });
      }
      if (!_isEmpty(leaderboard) && leaderboard?.participants?.length > 0) {
        availableRoutes.push({
          key: TAB_KEYS.LEADERBOARD,
          title: 'Leaderboard',
        });
      }
      availableRoutes.push({ key: TAB_KEYS.DETAILS, title: 'Details' });
      return availableRoutes;
    }, [hasUserRegistered, hasEnded, fixtures, leaderboard, isLive]);

    const onIndexChange = useCallback(
      (updatedIndex) => {
        setIndex(updatedIndex);
        const route = routes[updatedIndex];
        if (TAB_KEY_VS_EVENT?.[route?.key]) {
          Analytics.track(TAB_KEY_VS_EVENT[route.key], {
            ...getShowdownPropertiesToTrack({
              showdown,
            }),
            [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_DETAILS,
          });
        }
      },
      [setIndex, routes, showdown],
    );

    const renderScene = useCallback(
      ({ route, jumpTo }) => {
        // eslint-disable-next-line default-case
        switch (route.key) {
          case TAB_KEYS.DETAILS:
            return <DetailsRoute showdown={showdown} />;
          case TAB_KEYS.FIXTURES:
            return (
              <Fixtures
                fixtures={fixtures}
                state={state}
                onAction={onAction}
                loading={fixturesLoading}
              />
            );
          case TAB_KEYS.LEADERBOARD:
            return (
              <ShowdownLeaderboard
                showdown={showdown}
                leaderboard={leaderboard}
                currentRound={currentRound}
              />
            );
        }
      },
      [
        onAction,
        state,
        showdown,
        fixtures,
        leaderboard,
        fixturesLoading,
        currentRound,
      ],
    );

    const renderTabBar = (props) =>
      routes.length > 1 ? (
        <View style={styles.tabBarContainer}>
          <TabBar
            {...props}
            indicatorStyle={styles.indicator}
            style={styles.tabBar}
            tabStyle={styles.tabStyle}
            labelStyle={styles.label}
            activeColor={dark.colors.secondary}
            inactiveColor={DarkTheme.colors.textDark}
            renderLabel={({ route, focused, color }) => (
              <Text style={{ ...styles.label, color: color }}>
                {route.title}
              </Text>
            )}
          />
          <View style={styles.fullWidthLine} />
        </View>
      ) : (
        <View style={{ height: 20 }}></View>
      );

    return (
      <TabView
        navigationState={{ index, routes }}
        renderScene={renderScene}
        onIndexChange={onIndexChange}
        initialLayout={initialLayout}
        renderTabBar={renderTabBar}
      />
    );
  },
);

const ShowDownTabBarContainer = (props) => {
  const { state } = props;
  const showdownId = showdownReader.id(state.showdown);

  const { fixtures, loading, error } = useShowdownFixtures({
    showdownId: showdownId,
  });

  if (loading && _isEmpty(fixtures)) {
    return <ContestTabBarShimmer />;
  }

  return (
    <ShowdownTabBar {...props} fixtures={fixtures} fixturesLoading={loading} />
  );
};

export default React.memo(ShowDownTabBarContainer);
