import React from 'react'
import { View } from 'react-native'
import _isEmpty from 'lodash/isEmpty'
import styles from './JoinNowOrRegisterButtonsAndInfo.style'
import ShowdownTimer from '../ShowdownTimer'
import ShowDownRoundTimerInfo from '../ShowDownRoundTimerInfo'
import ShowdownRegistrationCount from './ShowdownRegistrationCount'
import ShowDownCTAButton from "../ShowDownCTAButton";

const JoinNowOrRegisterButtonsAndInfos = ({ state, onAction }) => {
  const { showdown, isLive } = state

  return (
    <View style={styles.infoContainer}>
      <ShowdownRegistrationCount showdown={showdown} />
      {!isLive ? (
        <ShowdownTimer state={state} />
      ) : (
        <ShowDownRoundTimerInfo state={state} />
      )}
      <View style={{ marginTop: 20, width: '100%' }}>
        <ShowDownCTAButton state={state} onAction={onAction}/>
      </View>
    </View>
  )
}

const ShowdownRightPaneContainer = (props) => {
  const { state } = props

  const { showdown } = state

  if (_isEmpty(showdown)) {
    return null
  }

  return <JoinNowOrRegisterButtonsAndInfos {...props} />
}

export default React.memo(ShowdownRightPaneContainer)
