import React, { useMemo } from 'react';
import _isEmpty from 'lodash/isEmpty'
import { View, Text, Image, TouchableOpacity, } from 'react-native';
import LinearGradient from '../../../../components/atoms/LinearGradient';
import styles from './CompactLeaderboard.style';
import useGetUserRankInContest from '../../hooks/useGetUserRankInContest';
import UserScoreCardShimmer from '../../shimmers/UserScoreCardShimmer';
import UserImage from '../../../../components/atoms/UserImage';
import scoreIcon from 'assets/images/score.png'
import groupIcon from 'assets/images/group.png'
import timerIcon from 'assets/images/timer.png'
import { getFormattedTimeWithMS } from 'core/utils/general'
import { getTimeSpentByUser } from '../../utils/contest';

const CompactUserScoreCard = ({ contest = EMPTY_OBJECT } = EMPTY_OBJECT) => {
    const { _id: contestId } = contest;
    const {
        loading,
        error,
        userRankInfo
    } = useGetUserRankInContest({ contestId })

    const formattedTimeTaken = useMemo(() => {
        const timeTaken = getTimeSpentByUser({ contest, participantSubmission: userRankInfo });
        return getFormattedTimeWithMS(timeTaken)
    }, [contest, userRankInfo]);

    if (loading && _isEmpty(userRankInfo)) {
        return <UserScoreCardShimmer />
    }

    if (error) {
        return <View style={{ height: 20 }}></View>
    }

    return (<LinearGradient colors={['#8C784F', '#CA4A4A']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradientBox}>
        <View style={styles.rankCard}>
            <UserImage user={userRankInfo?.user} style={styles.userImage} rounded={false} />
            <View style={styles.rankInfo}>
                <Text style={styles.rankText}># {userRankInfo?.rank}</Text>
                <View style={styles.statsRow}>
                    <View style={styles.statDisplay}>
                        <Image source={scoreIcon} style={styles.iconStyle} />
                        <Text style={styles.stat}>{userRankInfo?.questionsSolved}</Text>
                    </View>
                    <View style={styles.statDisplay}>
                        <Image source={timerIcon} style={styles.iconStyle} />
                        <Text style={styles.stat}>{formattedTimeTaken}</Text>
                    </View>
                    <View style={styles.statDisplay}>
                        <Image source={groupIcon} style={styles.iconStyle} />
                        <Text style={styles.stat}>{userRankInfo?.totalParticipants}</Text>
                    </View>

                </View>
            </View>
            {/* <TouchableOpacity onPress={onShare} style={styles.shareButton}>
                <View style={{ flexDirection: 'row', gap: 10, justifyContent: 'center' }}>
                    <Text style={styles.shareText}>Share</Text>
                </View>
            </TouchableOpacity> */}
        </View>
    </LinearGradient>)
}

export default CompactUserScoreCard