import React, {useMemo, useState} from 'react';
import {ScrollView, Text, View} from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import ExpandableSection from './ExpandableSection';
import styles from './CompactShowdownDetail.style';
import {ACCORDIAN} from '../../constants';
import ShowdownTimer from '../ShowdownTimer';
import ShowDownRoundTimerInfo from '../ShowDownRoundTimerInfo';
import _isEmpty from "lodash/isEmpty";

const ShowdownDetailRoute = ({state}) => {
  const {showdown: showdownDetails, isLive} = state;
  const [activeAccordian, setActiveAccordian] = useState('');

  const changeActiveAccordian = (accordian) => {
    setActiveAccordian((prev) => {
      if (prev === accordian) {
        return '';
      }
      return accordian;
    });
  };

  const renderShowdownTimers = useMemo(() => {
    if (!isLive) {
      return <ShowdownTimer state={state}/>;
    }
    return <ShowDownRoundTimerInfo state={state}/>;
  }, [state]);

  return (
    <ScrollView contentContainerStyle={styles.contestDetailContainer}>
      <View style={styles.showdownDetailBoxContainer}>
        <View style={styles.showdownDetailBox}>
          <View
            style={{
              flex: 1,
              width: '48%',
              height: 36,
              gap: 8,
              flexDirection: 'row',
            }}
          >
            <View
              style={{
                width: 36,
                height: '100%',
                borderRadius: 8,
                backgroundColor: '#292929',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <MaterialCommunityIcons
                name="account-group-outline"
                size={20}
                color="#D9D9D9"
              />
            </View>
            <View
              style={{
                height: '100%',
                paddingVertical: 3,
                gap: 6,
              }}
            >
              <Text
                style={{
                  color: '#BABABA',
                  fontSize: 11,
                  lineHeight: 12.5,
                  fontFamily: 'Montserrat-500',
                }}
              >
                Registered
              </Text>
              <Text
                style={{
                  color: '#FFFFFF',
                  fontSize: 12,
                  lineHeight: 12,
                  fontFamily: 'Montserrat-600',
                }}
              >
                {showdownDetails?.registrationCount ?? 0}
              </Text>
            </View>
          </View>
          <View style={{flex: 1, width: '48%'}}>
            <View>{renderShowdownTimers}</View>
          </View>
        </View>
      </View>

      {!_isEmpty(ACCORDIAN)
        ? ACCORDIAN.map((accordian, index) => (
          <ExpandableSection
            key={`SHOWDOWN_DETAIL_${index}`}
            title={accordian?.title}
            expanded={activeAccordian === accordian?.title}
            content={showdownDetails?.details?.[accordian?.key] ?? ''}
            onToggle={() => changeActiveAccordian(accordian?.title)}
          />
        ))
        : null}
    </ScrollView>
  );
};

export default React.memo(ShowdownDetailRoute);
