import React, { useCallback, useMemo } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import PropTypes from 'prop-types';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { register } from 'numeral';
import _isNaN from 'lodash/isNaN';
import _slice from 'lodash/slice';
import useShowdownCurrentStatus from '../../hooks/useShowdownCurrentStatus';
import SHOWDOWN_ACTIONS from '../../constants/showdownActions';
import styles from './ShowDownCTAButton.style';
import { showToast, TOAST_TYPE } from '../../../../components/molecules/Toast';
import useRoundDetailInfo from '../../hooks/useRoundInfo';
import { SHOWDOWN_PLAYER_STATUS } from '../../constants/showdownPlayerStatus';
import dark from '../../../../core/constants/themes/dark';
import useCountDownTimer from '../../hooks/useCountdownTimer';

const ShowDownCTAsForRegisteredUsers = ({ state, onAction }) => {
  const { isMobile } = useMediaQuery();
  const {
    canUserWithdrawRegistration,
    contestIsLiveAndNotEnded,
    hasUserGotBye,
    hasOpponentNotShown,
  } = useShowdownCurrentStatus({ state });
  const {
    hasEnded,
    isBreak,
    canUserJoin,
    hasUserCompletedTheRound,
    showdown,
    isRoundActive,
    isLive,
    playerStatus,
    joinEndTime,
  } = state;

  const { timer: timerToJoin, formattedTime: timeToJoinRound } =
    useCountDownTimer({
      targetTimeStamp: joinEndTime,
    });

  const didNotJoin = playerStatus === SHOWDOWN_PLAYER_STATUS.DID_NOT_PLAY;
  const bothUserDidNotPlay =
    playerStatus === SHOWDOWN_PLAYER_STATUS.BOTH_DID_NOT_PLAY;

  const { currentRoundInfo } = useRoundDetailInfo({ showdown });

  const buttonStyles = useMemo(
    () => ({
      button: {
        borderRadius: isMobile ? 50 : 8,
        height: isMobile ? 40 : 36,
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
      },
      unRegisterButton: {
        backgroundColor: dark.colors.primary,
        paddingVertical: 10,
      },
      unregisterText: {
        fontFamily: 'Montserrat-600',
        fontSize: 13,
        lineHeight: 20,
        color: '#FF7777',
      },
      inactiveButtonText: {
        color: dark.colors.textDark,
        fontSize: isMobile ? 14 : 16,
        fontFamily: 'Montserrat-600',
      },
      registerButton: {
        backgroundColor: dark.colors.secondary,
        padding: 10,
        width: '100%',
      },
      inactiveButton: {
        backgroundColor: dark.colors.tertiary,
        padding: 10,
      },
      registerText: {
        color: dark.colors.card,
        fontSize: isMobile ? 14 : 16,
        fontFamily: 'Montserrat-600',
      },
    }),
    [isMobile],
  );

  const onPressUnRegister = useCallback(() => {
    onAction?.({ type: SHOWDOWN_ACTIONS.UN_REGISTER_FROM_SHOWDOWN });
  }, [onAction]);

  const onPressInactiveButton = useCallback(
    ({ message, type = TOAST_TYPE.INFO }) =>
      () => {
        showToast({ description: message, type });
      },
    [],
  );

  const onPressJoinGame = useCallback(() => {
    onAction?.({ type: SHOWDOWN_ACTIONS.JOIN_NOW });
  }, [onAction]);

  const renderUnRegisterButton = () => (
    <TouchableOpacity
      style={[buttonStyles.button, buttonStyles.unRegisterButton]}
      onPress={onPressUnRegister}
    >
      <Text style={buttonStyles.unregisterText}>Unregister</Text>
    </TouchableOpacity>
  );

  const renderInactiveButton = ({ label, message }) => (
    <TouchableOpacity
      style={[buttonStyles.button, buttonStyles.inactiveButton]}
      onPress={onPressInactiveButton({ message })}
    >
      <Text style={buttonStyles.inactiveButtonText}>{label}</Text>
    </TouchableOpacity>
  );
  // TODO @mohan handle the case when both of the users have not shown.
  if (canUserWithdrawRegistration) {
    return renderUnRegisterButton();
  }
  if (hasEnded) {
    return (
      <View style={[buttonStyles.button, buttonStyles.inactiveButton]}>
        <Text style={styles.inactiveButtonText}>Showdown Ended</Text>
      </View>
    );
  }
  if (contestIsLiveAndNotEnded) {
    // isBreak
    if (isBreak) {
      return renderInactiveButton({
        label: 'Break',
        message: 'On Break, Please wait for next round to start',
      });
    }
    if (hasUserGotBye) {
      return renderInactiveButton({
        label: 'Got Bye',
        message:
          'You Got Bye in this round, Please wait for next round to start',
      });
    }
    if (bothUserDidNotPlay) {
      return renderInactiveButton({
        label: 'Both Players missed',
        message:
          'You both did not show up in this round, Please wait for next round to start',
      });
    }
    if (hasOpponentNotShown) {
      return renderInactiveButton({
        label: 'Opponent Not Shown',
        message:
          'You won the round as your opponent did not show up, Please wait for next round to start',
      });
    }
    if (hasUserCompletedTheRound) {
      return renderInactiveButton({
        label: 'Round Completed',
        message: 'You Completed the round, Please wait for next round to start',
      });
    }
    if (canUserJoin) {
      return (
        <TouchableOpacity
          onPress={onPressJoinGame}
          style={[buttonStyles.button, buttonStyles.registerButton]}
        >
          <Text style={buttonStyles.registerText}>
            Join Now
            {!_isNaN(timerToJoin) && timerToJoin > 1
              ? ` (${timeToJoinRound})`
              : ''}
          </Text>
        </TouchableOpacity>
      );
    }
    if (currentRoundInfo?.isRoundEnded) {
      return renderInactiveButton({
        label: 'Wait for next round',
        message: 'Please wait for next round to start',
      });
    }
    if (isRoundActive && didNotJoin) {
      // TODO @mohan this is getting rendered
      return renderInactiveButton({
        label: 'Missed The Round',
        message: 'You missed this round, You can still join next round.',
      });
    }
    if (isLive && isRoundActive && currentRoundInfo?.hasFailedToPlay) {
      return (
        <View style={[buttonStyles.button, buttonStyles.registerButton]}>
          <Text style={styles.registerText}>Failed to join</Text>
        </View>
      );
    }
  }
  return null;
};

ShowDownCTAsForRegisteredUsers.propTypes = {
  state: PropTypes.object.isRequired,
  onAction: PropTypes.func.isRequired,
};

export default React.memo(ShowDownCTAsForRegisteredUsers);
