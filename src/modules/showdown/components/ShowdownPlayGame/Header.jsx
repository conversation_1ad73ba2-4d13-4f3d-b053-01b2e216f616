import React, { useCallback, useEffect, useRef, useState } from 'react'
import { StyleSheet, Image, View } from 'react-native'
import { Text } from '@rneui/themed'
import useGameContext from '../../../hooks/useGameContext'

import Card2 from '../usercard/CardDuringGame'
import Card3 from '../usercard//CardDuringMobileGame'
import useMediaQuery from '@/src/core/hooks/useMediaQuery'
import _toNumber from 'lodash/toNumber'
import _isEmpty from 'lodash/isEmpty'
import cardBackground from '@/assets/images/gameMobBack.png'
import MaterialIcons from '@expo/vector-icons/MaterialIcons'
import dark from '../../../../../core/constants/themes/dark'
import getCurrentTimeWithOffset from '../../../../core/utils/getCurrentTimeWithOffset'
import gameReader from '@/src/core/readers/gameReader'

const styles = StyleSheet.create({
    container: {
        maxWidth: 420,
        width: '98%',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexDirection: 'row',
        height: 54,
        gap: 16,
    },
    containerMob: {
        borderWidth: 0.5,
        width: '90%',
        borderColor: '#666',
        borderRadius: 12,
        overflow: 'hidden',
    },
    timerBox: {
        flexDirection: 'row',
        alignItems: 'flex-end',
        width: 90,
        justifyContent: 'center',
    },
    timerText: {
        marginBottom: -1,
        fontSize: 18,
        fontFamily: 'Montserrat-700',
        color: dark.colors.textDark
    },
    timerText2: {
        marginBottom: -1,
        fontSize: 14,
        fontFamily: 'Montserrat-700',
        color: '#FFFFFF',
    },
    backImg: {
        position: 'absolute',
        width: '100%',
        height: '100%',
        resizeMode: 'stretch',
    },
})

const Header = ({ playersScores, onGameEnded }) => {
    const { players, game } = useGameContext()
    const { config = EMPTY_OBJECT, startTime } = game

    const { isMobile } = useMediaQuery()

    // timeLimit in seconds
    const timeLimit = gameReader.timeLimit(game)

    const startTimeDate = new Date(startTime)
    const currentTime = getCurrentTimeWithOffset()

    const endTime = startTimeDate.getTime() + _toNumber(timeLimit) * 1000
    const timeLeftInSecond = Math.max(
        Math.ceil((endTime - currentTime) / 1000),
        0
    )

    const [timer, setTimer] = useState(timeLeftInSecond)

    const formatTime = useCallback((seconds) => {
        const minutes = Math.floor(seconds / 60)
        const remainingSeconds = seconds % 60
        return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${Math.floor(remainingSeconds)}`
    }, [])

    const currTimeRef = useRef()

    const getUserScore = useCallback(
        ({ user }) => {
            if (_isEmpty(user)) return 0
            const { _id: userId } = user
            return playersScores[userId]
        },
        [playersScores]
    )

    const renderPlayer = useCallback(
        ({ user }) => {
            const score = getUserScore({ user })
            return <Card2 user={user} score={score} />
        },
        [getUserScore]
    )
    const renderPlayer2 = useCallback(
        ({ user }) => {
            const score = getUserScore({ user })
            return <Card3 user={user} score={score} />
        },
        [getUserScore]
    )

    useEffect(() => {
        if (currTimeRef.current) {
            clearInterval(currTimeRef.current)
        }

        currTimeRef.current = setInterval(() => {
            const timeLeft = (endTime - getCurrentTimeWithOffset())/1000;
            if(timeLeft <= 0){
                onGameEnded();
                clearInterval(currTimeRef.current);
                return;
            }
            setTimer(Math.max(timeLeft, 0));
        }, 1000)

        return () => clearInterval(currTimeRef.current)
    }, [timer, endTime, onGameEnded])

    useEffect(() => {
        if (timer <= 0) {
            onGameEnded()
        }
    }, [timer, onGameEnded])

    if (isMobile) {
        return (
            <View style={[styles.container, styles.containerMob]}>
                <Image source={cardBackground} style={styles.backImg} />
                {renderPlayer2({ user: players[0] })}
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'flex-end',
                    }}
                >
                    <MaterialIcons name={'timer'} color={'white'} size={16} />
                    <Text style={styles.timerText2}> {formatTime(timer)}</Text>
                </View>

                {renderPlayer2({ user: players[1] })}
            </View>
        )
    } else
        return (
            <View style={styles.container}>
                {renderPlayer({ user: players[0] })}
                <View style={styles.timerBox}>
                    <MaterialIcons name={'timer'} color={dark.colors.textDark} size={20} />
                    <Text style={styles.timerText}> {formatTime(timer)}</Text>
                </View>
                {renderPlayer({ user: players[1] })}
            </View>
        )
}


export default Header
