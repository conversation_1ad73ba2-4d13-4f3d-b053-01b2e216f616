import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  infoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 4,
    paddingBottom: 3,
    flex: 2,
  },
  currentRoundTitle: {
    color: dark.colors.textLight,
    fontSize: 10,
    lineHeight: 12.9,
    fontWeight: '700',
  },
  winsInfo: {
    color: dark.colors.textLight,
    fontSize: 14,
    lineHeight: 17,
    marginTop: 8,
  },
  gamesPlayedInfo: {
    color: dark.colors.textDark,
    fontSize: 10,
    lineHeight: 12.9,
    fontWeight: '500',
    marginTop: 4,
  },
  timer: {
    borderWidth: 1,
    borderRadius: 12,
    height: 20,
    marginTop: 20,
    borderColor: dark.colors.tertiary,
    backgroundColor: dark.colors.primary,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  timerTextStyle: {
    color: '#fff',
    fontSize: 10,
    lineHeight: 12.9,
    // marginTop: 8,
  },
  liveText: {
    color: '#fff',
    letterSpacing: 1,
    fontSize: 14,
    fontWeight: '700',
  },
});

export default styles;
