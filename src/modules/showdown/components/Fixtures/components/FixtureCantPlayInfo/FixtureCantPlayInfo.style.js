import { StyleSheet } from 'react-native'
import dark from 'core/constants/themes/dark'

const styles = StyleSheet.create({
  container: {
    // paddingHorizontal: 16,
    position: 'relative',
    width: '100%',
  },
  contentContainer: {
    position: 'relative',
    overflow: 'hidden',
    borderRadius: 12,
    backgroundColor: dark.colors.gradientBackground,
    padding: 16,
    minHeight: 105,
    paddingBottom: 21,
    width: '100%',
  },
  roundInfoText: {
    color: dark.colors.textDark,
    fontSize: 8,
    lineHeight: 9.75,
    fontFamily: 'Montserrat-800',
  },
  cardTitle: {
    fontSize: 10,
    lineHeight: 12.9,
    fontFamily: 'Montserrat-800',
    marginTop: 6,
  },
  cardDescription: {
    color: 'white',
    fontSize: 11,
    lineHeight: 16,
    fontFamily: 'Montserrat-500',
    marginTop: 6,
  },
})

export default styles
