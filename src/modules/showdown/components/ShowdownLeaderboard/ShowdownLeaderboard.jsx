import reduce from 'lodash/reduce';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ScrollView, View, Text, FlatList, Image } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _get from 'lodash/get';
import { PagesComponent } from 'shared/PaginatedList/PaginatedList';
import Loading from '@/src/components/atoms/Loading';
import showdownReader from '../../readers/showdownReader';
import styles from './ShowdownLeaderboard.style';
import useShowdownLeaderboard from '../../hooks/query/useLeaderboardDetails';

const SCORE = {
  1: '1',
  0.5: '0.5',
  0: '0',
};

const LeaderBoardRow = React.memo(({ item, rows, rowWidth }) => {
  const rounds = _get(item, ['participant', 'rounds'], []);
  const userInfo = _get(item, ['participant', 'userInfo'], EMPTY_OBJECT);
  const total = useMemo(
    () =>
      reduce(rounds, (_total, round) => _total + _get(round, 'score', 0), 0),
    [rounds],
  );

  return (
    <View style={styles.row}>
      <View style={[styles.cell, styles.rankCell]}>
        <Text style={styles.rankText}>{item.rank}</Text>
      </View>
      <View style={[styles.cell, styles.nameCell]}>
        <Image
          style={{ width: 32, height: 32 }}
          source={{
            uri: _get(userInfo, ['profileImageUrl'], ''),
          }}
        />
        <View style={{ flex: 1 }}>
          <Text style={styles.name}>{_get(userInfo, ['username'], '')}</Text>
          <Text style={styles.ratingText}>
            {_get(userInfo, ['rating'], '')}
          </Text>
        </View>
      </View>
      <View style={[styles.cell, styles.scoreCell, { width: rowWidth + 10 }]}>
        <Text style={styles.scoreText}>{total}</Text>
      </View>
      {rows.map((_, index) => (
        <View
          key={index}
          style={[styles.cell, styles.scoreCell, { width: rowWidth }]}
        >
          <Text style={styles.scoreText}>
            {SCORE?.[_get(rounds[index], 'score', 0)] ?? '-'}
          </Text>
        </View>
      ))}
    </View>
  );
});

const Leaderboard = ({ showdown, currentRound }) => {
  const showdownId = showdownReader.id(showdown) ?? '';
  const registrationCount = showdownReader.registrationCount(showdown);
  const [pageNumber, setPageNumber] = useState(1);
  const { leaderboard, loading, refetch } = useShowdownLeaderboard({
    showdownId,
    page: pageNumber,
  });
  const roundsCount = showdownReader.roundsCount(showdown);
  const rows = useMemo(() => Array(roundsCount).fill(null), [roundsCount]);
  const { isMobile } = useMediaQuery();
  const rowWidth = isMobile ? 46 : 70;
  const renderLeaderBoardRow = useCallback(
    ({ item }) => (
      <LeaderBoardRow rowWidth={rowWidth} item={item} rows={rows} />
    ),
    [rowWidth, rows],
  );

  useEffect(() => {
    if (currentRound > 0) {
      refetch?.();
    }
  }, [currentRound, refetch]);

  const totalPages = useMemo(
    () => Math.ceil(_get(leaderboard, 'count', registrationCount) / 100),
    [registrationCount, leaderboard],
  );

  const renderHeader = useCallback(
    () => (
      <View style={[styles.row, styles.headerRow]}>
        <Text style={[styles.headerTitle]}>Mathlete</Text>
        <Text style={[styles.headerRoundTitle, { width: rowWidth + 10 }]}>
          Total
        </Text>
        {rows.map((_, index) => (
          <Text
            key={`leaderboard-row-${index}`}
            style={[styles.headerRoundTitle, { width: rowWidth }]}
          >
            {index + 1}
          </Text>
        ))}
      </View>
    ),
    [rowWidth, rows],
  );

  if (loading) {
    return <Loading />;
  }

  const participantsLength = _get(leaderboard, 'participants', []).length;
  if (!participantsLength) {
    return (
      <View style={styles.NoDataContainer}>
        <Text style={styles.noDataText}>No data available</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      <ScrollView
        showsHorizontalScrollIndicator={false}
        horizontal
        style={[styles.container, isMobile && { paddingBottom: 54 }]}
      >
        <FlatList
          showsVerticalScrollIndicator={false}
          data={_get(leaderboard, 'participants', [])}
          renderItem={renderLeaderBoardRow}
          keyExtractor={(item, index) => index.toString()}
          ListHeaderComponent={renderHeader}
        />
      </ScrollView>
      {totalPages > 1 ? (
        <PagesComponent
          setPage={setPageNumber}
          totalPages={totalPages}
          page={pageNumber}
        />
      ) : null}
    </View>
  );
};

export default React.memo(Leaderboard);
