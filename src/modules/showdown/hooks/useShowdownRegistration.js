import { useCallback, useState } from 'react';

import { showToast, TOAST_TYPE } from 'molecules/Toast'

import Analytics from "../../../core/analytics";
import {ANALYTICS_EVENTS} from "../../../core/analytics/const";
import showdownReader from "../readers/showdownReader";
import {PAGE_NAME_KEY, PAGE_NAMES} from "../../../core/constants/pageNames";
import useRegisterForShowdown from './mutations/useRegisterForShowdown';
import useUnRegisterFromShowdown from './mutations/useUnregisterFromShowdown';
import { getShowdownPropertiesToTrack } from '../utils/showdownEvents';

const useShowdownRegistration = ({ showdown, reFetchShowDown, onShowdownRegistrationSuccess, onShowdownRegistrationFailure  }) => {
    const [isRegisteringForShowDown, setIsRegisteringForShowDown] = useState(false);

    const showdownId = showdownReader.id(showdown);

    const { registerForShowdown } = useRegisterForShowdown();
    const { unregisterFromShowdown } = useUnRegisterFromShowdown();

    const onPressRegister = useCallback(
        async (formData) => {
            setIsRegisteringForShowDown(true)
            try {
                Analytics.track(ANALYTICS_EVENTS.SHOWDOWN.CLICK_ON_REGISTER_FOR_SHOWDOWN, {
                    ...getShowdownPropertiesToTrack({ showdown }),
                    [PAGE_NAME_KEY]: PAGE_NAMES.SHOWDOWN_DETAILS,
                })
                const result = await registerForShowdown({
                    showdownId,
                });
                if (result.data.registerForShowdown) {
                    onShowdownRegistrationSuccess?.();
                    showToast({
                        type: TOAST_TYPE.SUCCESS,
                        description:
                            'Successfully registered for the showdown!',
                    })
                    reFetchShowDown?.()
                } else {
                    showToast({
                        type: TOAST_TYPE.ERROR,
                        description: `Something went wrong while Registering`,
                    })
                    Analytics.track(ANALYTICS_EVENTS.SHOWDOWN.SHOWDOWN_REGISTRATION_FAILED, {
                        ...getShowdownPropertiesToTrack({ showdown }),
                        error: result.data.registerForShowdown.message,
                        [PAGE_NAME_KEY]: PAGE_NAMES.SHOWDOWN_DETAILS,
                    })
                }
            } catch (error) {
                onShowdownRegistrationFailure?.();
                showToast({
                    type: TOAST_TYPE.ERROR,
                    description: `Something went wrong while Registering`,
                })
                Analytics.track(ANALYTICS_EVENTS.SHOWDOWN.SHOWDOWN_REGISTRATION_FAILED, {
                    ...getShowdownPropertiesToTrack({ showdown }),
                    error: error,
                    [PAGE_NAME_KEY]: PAGE_NAMES.SHOWDOWN_DETAILS,
                })
            } finally {
                setIsRegisteringForShowDown(false)
            }
        },
        [showdown, reFetchShowDown, registerForShowdown, onShowdownRegistrationSuccess, onShowdownRegistrationFailure]
    )

    const onPressUnRegister = useCallback(async () => {
        try {
            const result = await unregisterFromShowdown({ showdownId })
            Analytics.track(ANALYTICS_EVENTS.SHOWDOWN.CLICK_ON_UNREGISTERED_FROM_SHOWDOWN, {
                ...getShowdownPropertiesToTrack({ showdown }),
                [PAGE_NAME_KEY]: PAGE_NAMES.SHOWDOWN_DETAILS,
            })
            if (result.data.unregisterFromShowdown) {
                showToast({
                    type: TOAST_TYPE.SUCCESS,
                    description:
                        'Un-Registered from showdown!',
                })
                reFetchShowDown?.()
            } else {
                showToast({
                    type: TOAST_TYPE.ERROR,
                    description: `Something went wrong while removing the registration`,
                })
            }
        } catch (error) {
            showToast({
                type: TOAST_TYPE.ERROR,
                description: `Something went wrong while removing the registration`,
            })
        }
    }, [showdown, unregisterFromShowdown, reFetchShowDown])

    return {
        isRegisteringForShowDown,
        onPressRegister,
        handleSubmit: onPressRegister,
        onPressUnRegister
    }
}

export default useShowdownRegistration;