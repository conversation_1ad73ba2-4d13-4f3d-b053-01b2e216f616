import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Image, Text, View } from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import _findIndex from 'lodash/findIndex';
import _isNaN from 'lodash/isNaN';
import _map from 'lodash/map';
import _isNil from 'lodash/isNil';
import useMediaQuery from 'core/hooks/useMediaQuery';
import useWeeklyLeagueInfoImagesStyles from './WeeklyLeagueInfoImages.style';
import { WEEKLY_LEAGUE_TYPES } from '../../constants/weeklyLeagueTypes';

const WeeklyLeagueInfoImages = (props: any) => {
  const styles = useWeeklyLeagueInfoImagesStyles();

  const { userLeague } = props ?? EMPTY_OBJECT;

  const [carouselWidth, setCrauselWidth] = useState(300);

  const { isMobile: isCompactMode } = useMediaQuery();
  const carouselRef = useRef(null);
  const cardRef = useRef(null);
  const [activeIndex, setActiveIndex] = useState(0);

  const onProgressChange = useCallback(
    (offsetProgress: number, absoluteProgress: number) => {
      const currentIndex = Math.round(absoluteProgress);
      if (currentIndex !== activeIndex) {
        setActiveIndex(currentIndex);
      }
    },
    [activeIndex, setActiveIndex],
  );

  const userLeagueIndex = useMemo(() => {
    const userLeagueIndex = _findIndex(
      WEEKLY_LEAGUE_TYPES,
      (league) => league.key === userLeague?.league,
    );
    return userLeagueIndex === -1 ? 0 : userLeagueIndex;
  }, [userLeague]);

  useEffect(() => {
    setActiveIndex(userLeagueIndex);
  }, [userLeagueIndex]);

  const renderItem = useCallback(
    ({ item, index }) => {
      const isActive = index === activeIndex;
      const isHigherBadge = index > userLeagueIndex;

      return (
        <View
          style={{
            justifyContent: 'center',
            height: 140,
            alignItems: 'center',
          }}
          key={`${index}`}
        >
          <View
            style={[
              styles.imageContainer,
              !isActive && {
                alignItems: 'center',
              },
            ]}
          >
            <Image
              source={
                isHigherBadge
                  ? require('assets/images/weeklyLeague/lockedLeague.png')
                  : item.image
              }
              style={[
                isActive && !isHigherBadge
                  ? { width: 120, height: 120 }
                  : styles.image,
              ]}
              resizeMode="contain"
            />
          </View>
        </View>
      );
    },
    [activeIndex, styles.image, styles.imageContainer, userLeagueIndex],
  );

  const onCardLayout = (event: any) => {
    const { width: containerWidth } = event.nativeEvent.layout;

    if (!_isNil(containerWidth) && !_isNaN(containerWidth)) {
      setCrauselWidth(containerWidth);
    }
  };

  const renderImagesWithText = () => {
    if (isCompactMode) {
      return (
        <Carousel
          ref={carouselRef}
          loop={false}
          width={carouselWidth}
          height={140}
          defaultIndex={userLeagueIndex ?? 0}
          data={WEEKLY_LEAGUE_TYPES}
          scrollAnimationDuration={1000}
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'hidden',
          }}
          renderItem={renderItem}
          mode="parallax"
          modeConfig={{
            parallaxScrollingScale: 0.96,
            parallaxScrollingOffset: 260,
          }}
          onProgressChange={onProgressChange}
          autoPlayInterval={2000}
        />
      );
    }
    return (
      <View style={{ flexDirection: 'row', gap: 30, justifyContent: 'center' }}>
        {_map(WEEKLY_LEAGUE_TYPES, (item, index) =>
          renderItem({ item, index }),
        )}
      </View>
    );
  };

  return (
    <View
      onLayout={onCardLayout}
      ref={cardRef}
      style={{
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      {renderImagesWithText()}
      <Text
        style={[
          styles.leagueNameText,
          !(activeIndex > userLeagueIndex) && {
            color: WEEKLY_LEAGUE_TYPES[activeIndex]?.textColor,
          },
        ]}
      >
        {activeIndex > userLeagueIndex
          ? 'MATIKS LEAGUE'
          : WEEKLY_LEAGUE_TYPES[activeIndex]?.labelName}
      </Text>
    </View>
  );
};

export default React.memo(WeeklyLeagueInfoImages);
