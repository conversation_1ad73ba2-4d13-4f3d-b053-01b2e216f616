import PropTypes from 'prop-types'
import React from 'react'
import { KeyboardAvoidingView, Platform, View } from 'react-native'
import LeagueDetailCard from '../../../components/LeagueDetailCard'
import LeagueDetailTabBar from '../../../components/LeagueDetailTabBar'
import CompactLeagueButtonView from '../../../components/CompactLeagueButtonView'
import useMediaQuery from '@/src/core/hooks/useMediaQuery'
import { useLocalSearchParams } from 'expo-router'
import { TAB_KEYS } from '../../../constants/leagueDetails'

const CompactLeagueDetailsPage = (props) => {
  const { leagueDetails, refetchLeagueDetails } = props ?? EMPTY_OBJECT

  const { isMobile: isCompactMode } = useMediaQuery()
  const { tab: activeTab } = useLocalSearchParams() ?? EMPTY_OBJECT

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 24}
    >
      <View style={{ overflow: 'hidden', flex: 1 }}>
        <LeagueDetailCard leagueDetails={leagueDetails} />
        <LeagueDetailTabBar
          leagueDetails={leagueDetails}
          refetchLeagueDetails={refetchLeagueDetails}
        />

        {isCompactMode && activeTab !== TAB_KEYS.CHATS && (
          <View
            style={{
              position: 'absolute',
              bottom: 10,
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <CompactLeagueButtonView
              leagueDetails={leagueDetails}
              refetchLeagueDetails={refetchLeagueDetails}
            />
          </View>
        )}
      </View>
    </KeyboardAvoidingView>
  )
}

CompactLeagueDetailsPage.propTypes = {
  leagueDetails: PropTypes.object,
  leagueId: PropTypes.string,
}

export default React.memo(CompactLeagueDetailsPage)
