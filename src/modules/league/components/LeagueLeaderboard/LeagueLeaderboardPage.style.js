import { StyleSheet } from 'react-native'
import dark from 'core/constants/themes/dark'

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    width: '90%',
    marginHorizontal: '10%',
  },
  title: {
    color: '#fff',
    fontSize: 20,
    fontFamily: 'Montserrat-700',
    maxWidth: '80%',
  },
  list: {
    flexGrow: 1,
  },
  row: {
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
    paddingVertical: 18,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: dark.colors.tertiary,
  },
  rank: {
    textAlign: 'left',
    flex: 1,
    color: '#fff',
    fontFamily: 'Montserrat-500',
    fontSize: 13,
  },
  name: {
    textAlign: 'left',
    color: '#fff',
    maxWidth: 160,
    fontFamily: 'Montserrat-500',
    fontSize: 13,
  },
  score: {
    color: '#fff',
    fontFamily: 'Montserrat-500',
    fontSize: 13,
    textAlign: 'right',
  },
  time: {
    flex: 2,
    textAlign: 'right',
    color: '#fff',
    fontFamily: 'Montserrat-500',
    fontSize: 13,
  },
  paginationContainer: {
    backgroundColor: dark.colors.background,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 6,
    borderRadius: 10,
    bottom: 0,
    position: 'absolute',
    alignItems: 'center',
  },
  pageButton: {
    marginHorizontal: 5,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 10,
  },
  selectedPage: {
    backgroundColor: dark.colors.primary,
  },
  pageText: {
    color: dark.colors.textDark,
    fontSize: 16,
  },
  selectedPageText: {
    color: dark.colors.secondary,
    fontSize: 16,
  },
  headerExpanded: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 24,
    marginHorizontal: 16,
  },
  editTextStyle: {
    color: 'white',
    fontSize: 17,
    fontFamily: 'Montserrat-500',
  },
  activityIcon: {
    height: 12,
    width: 12,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
  },
})

export default styles
