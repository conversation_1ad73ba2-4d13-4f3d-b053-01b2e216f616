import { FlatList, Pressable, Text, View } from 'react-native';
import React, { useCallback } from 'react';

import BackButton from '@/src/components/molecules/BackButton';
import InteractivePrimaryButton from '@/src/components/atoms/InteractivePrimaryButton';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import ProgressBar from '@/src/components/atoms/ProgressBar/ProgressBar';
import dark from '@/src/core/constants/themes/dark';
import styles from './GoalScreen.style';
import { useRouter } from 'expo-router';

const GoalView = () => {
  const router = useRouter();
  const onSkip = useCallback(() => {
    router.replace('/home');
  }, [router]);

  const [selectedGoalIndex, setSelectedGoalIndex] = React.useState(null);

  const data = [
    {
      id: 4,
      label: '4 Mins/Day',
    },
    {
      id: 9,
      label: '9 Mins/Day',
    },
    {
      id: 15,
      label: '15 Mins/Day',
    },
    {
      id: 20,
      label: '20 Mins/Day',
    },
  ];

  const renderItem = ({ item, index }: any) => {
    return (
      <InteractiveSecondaryButton
        borderColor={
          selectedGoalIndex === index
            ? dark.colors.secondary
            : dark.colors.tertiary
        }
        label={item.label}
        onPress={() => setSelectedGoalIndex(index)}
      />
    );
  };

  const handleContinue = useCallback(async () => {}, [router]);
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <BackButton />
        <ProgressBar currentStep={4} totalSteps={4} />
      </View>
      <View style={styles.buttonContainer}>
        <FlatList
          contentContainerStyle={{ paddingBottom: 40, gap: 16 }}
          data={data}
          renderItem={renderItem}
        />
      </View>
      <View style={[styles.footer]}>
        <InteractivePrimaryButton
          onPress={handleContinue}
          label={'BRING IT ON'}
          buttonStyle={[styles.continueButton]}
          buttonContainerStyle={styles.continueButtonContainer}
          labelStyle={styles.continueButtonText}
          buttonBorderBackgroundStyle={styles.continueBackground}
        />

        <Pressable style={styles.skipButton} onPress={onSkip}>
          <Text style={styles.noReferralCode}>SKIP</Text>
        </Pressable>
      </View>
    </View>
  );
};

export default GoalView;
