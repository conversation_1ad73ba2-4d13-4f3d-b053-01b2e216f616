import { StyleSheet } from 'react-native';
import dark from '../../../../core/constants/themes/dark';

export default StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  skipButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: 2,
  },
  skipButtonTitle: {
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-500',
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 12,
    marginBottom: 36,
  },
  footerContainer: {
    paddingHorizontal: 16,
    paddingVertical: 24,
  },
  icon: {
    fontSize: 128,
    lineHeight: 156,
  },
  textDark: {
    color: dark.colors.textDark,
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    lineHeight: 24,
  },
  titleDescriptionContainer: {
    gap: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    color: 'white',
    fontSize: 18,
    fontFamily: 'Montserrat-500',
    marginBottom: 12,
    marginHorizontal: 16,
    testAlign: 'center',
  },
  why: {
    color: 'white',
    fontSize: 24,
    fontFamily: 'Montserrat-800',
    marginBottom: 24,
    marginHorizontal: 16,
    testAlign: 'center',
  },
  description: {
    color: dark.colors.textDark,
    textAlign: 'center',
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    lineHeight: 17,
  },
  contentItemContainer: {
    width: '100%',
    height: 85,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: 8,
    borderWidth: 1,
    borderColor: dark.colors.primary,
    backgroundColor: dark.colors.background,
    paddingHorizontal: 10,
    borderRadius: 10,
  },
  accuracyTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    color: dark.colors.secondary,
    fontFamily: 'Montserrat-600',
  },
  contentItemDescription: {
    fontSize: 16,
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-500',
  },
  backgroundImage: {
    position: 'absolute',
    alignItems: 'flex-end',
    zIndex: -1,
    height: '100%',
    width: '60%',
    right: 0,
  },
});
