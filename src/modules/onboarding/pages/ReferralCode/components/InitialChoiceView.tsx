import React from 'react';
import { View } from 'react-native';

import InteractivePrimaryButton from '@/src/components/atoms/InteractivePrimaryButton';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import { InitialChoiceViewProps } from '../types';

const InitialChoiceView: React.FC<InitialChoiceViewProps> = ({ styles, onNo, onYes }) => {
  return (
    <View style={styles.referralButtonContainer}>
      <View style={styles.referralButtonInnerContainer}>
        <View style={styles.referralButtonNoContainer}>
          <InteractiveSecondaryButton
            label="NO, I DON'T"
            onPress={onNo}
            buttonContainerStyle={styles.referralButtonNoInnerContainer}
            labelStyle={styles.continueButtonText}
          />
        </View>
        <View style={styles.referralButtonYesContainer}>
          <InteractivePrimaryButton
            onPress={onYes}
            label="YES, I DO"
            buttonContainerStyle={styles.referralButtonNoInnerContainer}
            buttonStyle={[styles.continueButton]}
            labelStyle={styles.continueButtonText}
            buttonBorderBackgroundStyle={styles.continueBackground}
          />
        </View>
      </View>
    </View>
  );
};

export default InitialChoiceView;
