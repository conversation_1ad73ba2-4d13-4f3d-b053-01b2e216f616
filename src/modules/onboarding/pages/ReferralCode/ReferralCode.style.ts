import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import fonts from '@/src/theme/fonts';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    mainContainer: { flex: 1, backgroundColor: dark.colors.background },
    container: { flex: 1, justifyContent: 'center', alignItems: 'center' },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingRight: 20,
      paddingTop: isCompactMode ? 20 : 40,
      paddingBottom: 20,
      zIndex: 1,
      gap: 12,
      paddingLeft: 20,
    },
    contentContainer: {
      flex: 1,
      flexDirection: 'column',
      justifyContent: 'space-around',
    },
    title: {
      fontSize: 12,
      fontFamily: 'Montserrat-700',
      letterSpacing: 1,
      marginBottom: 30,
      color: dark.colors.textLight,
    },
    mainContainerStyle: { marginTop: 20 },
    innerContainerStyle: { flexDirection: 'column' },
    referralInputContainer: {
      width: '80%',
      marginHorizontal: 50,
      alignItems: 'center',
      marginTop: 70,
      position: 'relative',
    },
    textInputStyle: {
      height: 64,
      borderRightColor: 'transparent',
      borderLeftColor: 'transparent',
      borderTopColor: 'transparent',
      fontFamily: 'Montserrat-500',
      color: dark.colors.textDark,
      textAlign: 'center',
      marginHorizontal: 50,
      width: '80%',
      backgroundColor: dark.colors.background,
    },
    footer: {
      backgroundColor: dark.colors.background,
      paddingBottom: isCompactMode ? 20 : 50,
    },
    buttonContainer: { width: '100%', gap: 25, alignItems: 'center' },
    errorContainer: {
      width: '100%',
      alignItems: 'center',
      paddingHorizontal: 16,
      marginTop: 10,
    },
    continueButton: {
      borderWidth: 0.8,
      borderColor: dark.colors.secondary,
      borderRadius: 10,
      height: 51,
    },
    continueButtonContainer: {
      flex: 1,
      width: '100%',
      minWidth: 200,
      justifyContent: 'center',
      alignItems: 'center',
      maxWidth: 400,
      height: 51,
      borderRadius: 12,
    },
    continueButtonText: {
      fontSize: 12,
      fontFamily: 'Montserrat-800',
      color: dark.colors.textLight,
      letterSpacing: 2,
    },
    continueBackground: { flex: 1, backgroundColor: dark.colors.victoryColor },
    skipButton: {
      height: 51,
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    },
    noReferralCode: {
      fontFamily: fonts.Montserrat_700,
      fontSize: 12,
      color: dark.colors.textLight,
      opacity: 0.8,
      letterSpacing: 1,
    },
    error: {
      alignSelf: 'center',
      color: dark.colors.errorRed,
      marginTop: 24,
      fontSize: 10,
      fontFamily: 'Montserrat-700',
      letterSpacing: 1,
      textAlign: 'center',
      lineHeight: 20,
    },
    image: { width: '100%', height: '80%' },
    buttonContainerStyle: {
      width: '100%',
      minWidth: 200,
      justifyContent: 'center',
      alignItems: 'center',
      height: 51,
      borderRadius: 12,
    },
    textInputBottomBorder: {
      width: '80%',
      height: 1,
      position: 'absolute',
      bottom: 0,
    },
    skipForNow: {
      fontFamily: fonts.Montserrat_700,
      fontSize: 12,
      color: dark.colors.textLight,
      opacity: 0.8,
      letterSpacing: 1,
    },
    skipForNowButton: {
      height: 51,
      justifyContent: 'center',
      alignItems: 'center',
    },
    referralButtonContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      width: '100%',
      paddingHorizontal: 20,
      marginTop: 70,
    },
    referralButtonInnerContainer: {
      flexDirection: 'row',
      width: '100%',
      height: 51,
      gap: 12,
    },
    referralButtonNoContainer: {
      flex: 1,
      marginRight: 6,
    },
    referralButtonYesContainer: {
      flex: 1,
      marginLeft: 6,
    },
    referralButtonNoInnerContainer: {
      flex: 1,
      height: 51,
    },
    footerError: {
      flexDirection: 'row',
      justifyContent: 'space-evenly',
      paddingHorizontal: 16,
    },
  });

export const useReferralCodePageStyles = () => {
  const { isMobile } = useMediaQuery();
  const styles = useMemo(() => createStyles(isMobile), [isMobile]);
  return styles;
};
