import { useState } from 'react';
import dark from '@/src/core/constants/themes/dark';
import _trim from 'lodash/trim';
import _toUpper from 'lodash/toUpper';
import { useUsernameValidation } from './useUsernameValidation';

interface UseUsernameStateProps {
  initialUsername?: string;
}

interface UsernameStatus {
  message: string;
  color: string;
  inputBorderColor: string;
}

interface UsernameStateData {
  username: string;
  setUsername: (username: string) => void;
  validation: {
    isAvailable: boolean;
    loading: boolean;
    error: any;
    hasChecked: boolean;
    isValidLength: boolean;
  };
  status: UsernameStatus;
  canContinue: boolean;
  cantProceed: boolean;
}

const getStatusFromValidation = (
  validation: UsernameStateData['validation'],
  username: string,
): UsernameStatus => {
  const { isAvailable, error, hasChecked } = validation ?? EMPTY_OBJECT;
  const trimmedUsername = _trim(username);

  if (!trimmedUsername) {
    return {
      message: '',
      color: dark.colors.textDark,
      inputBorderColor: dark.colors.victoryColor,
    };
  }

  if (error) {
    return {
      message: _toUpper('Error checking username'),
      color: dark.colors.error,
      inputBorderColor: dark.colors.error,
    };
  }

  if (hasChecked) {
    if (isAvailable) {
      return {
        message: '',
        color: dark.colors.victoryColor,
        inputBorderColor: dark.colors.victoryColor,
      };
    }
    return {
      message: _toUpper('username already exists'),
      color: dark.colors.errorRed,
      inputBorderColor: dark.colors.errorRed,
    };
  }

  return {
    message: '',
    color: dark.colors.whiteLight,
    inputBorderColor: dark.colors.victoryColor,
  };
};

export const useUsernameState = ({
  initialUsername = '',
}: UseUsernameStateProps): UsernameStateData => {
  const [username, setUsername] = useState(initialUsername);

  const validation = useUsernameValidation({ username });
  const status = getStatusFromValidation(validation, username);

  const canContinue =
    validation.isValidLength &&
    validation.isAvailable &&
    validation.hasChecked &&
    !validation.loading;
  const cantProceed =
    validation.hasChecked &&
    !validation.isAvailable &&
    validation.isValidLength;

  return {
    username,
    setUsername,
    validation,
    status,
    canContinue,
    cantProceed,
  };
};
