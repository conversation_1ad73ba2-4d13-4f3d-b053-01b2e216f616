import React from 'react';
import { Text } from 'react-native';
import { useUsernameStyles } from '../Username.style';

interface UsernameStatusProps {
  message: string;
  color: string;
}

export const UsernameStatus: React.FC<UsernameStatusProps> = ({
  message,
  color,
}) => {
  const styles = useUsernameStyles();

  if (!message) {
    return null;
  }

  return (
    <Text style={[{ color }, styles.errorText]}>
      {message}
    </Text>
  );
};
