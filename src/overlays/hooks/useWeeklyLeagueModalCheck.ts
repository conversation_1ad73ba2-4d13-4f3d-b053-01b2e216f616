import { useCallback, useEffect, useRef } from 'react';
import {
  LAST_WEEKLY_LEAGUE_MODAL_SHOWN_CACHE_KEY,
  useSession,
} from 'modules/auth/containers/AuthProvider';
import useHandleJoinedWeeklyLeagueEvent from '@/src/overlays/hooks/useHandleJoinedWeeklyLeagueEvent';
import userReader from 'core/readers/userReader';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import useLocalCache from '../../core/hooks/useLocalCache';

const MODAL_CHECK_DURATION_IN_MILLI_SEC = 1000;

const getCurrentWeekDatesUTC = (): string[] => {
  const today = new Date(getCurrentTime());
  const currentDayUTC = today.getUTCDay();
  const offsetToMonday = currentDayUTC === 0 ? 6 : currentDayUTC - 1;

  const firstDayOfWeekUTC = new Date(
    Date.UTC(
      today.getUTCFullYear(),
      today.getUTCMonth(),
      today.getUTCDate() - offsetToMonday,
    ),
  );

  const weekDates: string[] = [];
  for (let i = 0; i < 7; i++) {
    const day = new Date(firstDayOfWeekUTC);
    day.setUTCDate(firstDayOfWeekUTC.getUTCDate() + i);
    weekDates.push(day.toISOString().split('T')[0]);
  }
  return weekDates;
};

const getTodayDateUTC = (): string => new Date().toISOString().split('T')[0];

const useWeeklyLeagueModalCheck = () => {
  const { getData, setData } = useLocalCache(
    LAST_WEEKLY_LEAGUE_MODAL_SHOWN_CACHE_KEY,
  );
  const { user } = useSession();

  const userCurrLeagueInfo = userReader.userCurrLeagueInfo(user);

  const { handleUserJoinedWeeklyLeagueEvent } =
    useHandleJoinedWeeklyLeagueEvent();

  const checkHasShownWeeklyLeagueModal = useCallback(
    async (userLeagueInfo: any) => {
      try {
        const lastShownDateUTC = await getData();
        const currentWeekDates = getCurrentWeekDatesUTC();
        const todayUTC = getTodayDateUTC();

        if (_isEmpty(userCurrLeagueInfo)) return;

        if (
          !lastShownDateUTC ||
          !_includes(currentWeekDates, lastShownDateUTC)
        ) {
          await setData(todayUTC);
          if (!userReader.isGuest(user)) {
            handleUserJoinedWeeklyLeagueEvent({
              payload: {
                leagueInfo: userLeagueInfo ?? userCurrLeagueInfo,
              },
            });
          }
        }
      } catch (error) {
        console.error('Error in useWeeklyLeagueModalCheck:', error);
      }
    },
    [
      getData,
      userCurrLeagueInfo,
      setData,
      user,
      handleUserJoinedWeeklyLeagueEvent,
    ],
  );

  const checkHasShownWeeklyLeagueModalRef = useRef(
    checkHasShownWeeklyLeagueModal,
  );
  checkHasShownWeeklyLeagueModalRef.current = checkHasShownWeeklyLeagueModal;

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      checkHasShownWeeklyLeagueModalRef?.current();
    }, MODAL_CHECK_DURATION_IN_MILLI_SEC);
    return () => clearTimeout(timeoutId);
  }, []);

  return {
    checkHasShownWeeklyLeagueModal,
  };
};

export default useWeeklyLeagueModalCheck;
