import { Dimensions } from 'react-native'
import React from 'react'
import { SafeAreaView } from 'react-native-safe-area-context'

const WithSafeAreaView = (Component) => {
    const Wrapper = (props) => {
        const windowHeight = Dimensions.get('window').height

        return (
            <SafeAreaView
                style={{ flex: 1, maxHeight: windowHeight, width: '100%' }}
            >
                <Component {...props} />
            </SafeAreaView>
        )
    }

    return Wrapper
}

export default WithSafeAreaView
