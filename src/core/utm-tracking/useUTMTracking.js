import { useEffect } from 'react';
import * as Linking from 'expo-linking';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { Platform } from 'react-native';
import _size from 'lodash/size'; // UTM parameters we want to track

// UTM parameters we want to track
const UTM_PARAMS = [
  'utm_source',
  'utm_medium',
  'utm_campaign',
  'utm_term',
  'utm_content',
  'utm_referrer',
];

let hasUtmParamTracked = false;

export const useUtmTracking = () => {
  // Parse URL parameters
  const parseUrlParams = (url) => {
    try {
      const parsedUrl = new URL(url);
      const params = {};

      if (Platform.OS === 'web') {
        if (_size(document?.referrer) > 0) {
          params.referrer = document.referrer;
        }
      }

      UTM_PARAMS.forEach((param) => {
        const value = parsedUrl.searchParams.get(param);
        if (value) params[param] = value;
      });
      return Object.keys(params).length > 0 ? params : null;
    } catch (error) {
      console.error('Error parsing URL params:', error);
      return null;
    }
  };

  // Check for UTM parameters in the initial URL (on app load)
  const checkInitialUrl = async () => {
    try {
      const url = await Linking.getInitialURL();
      if (url) {
        const utmParams = parseUrlParams(url);
        if (utmParams) {
          if (!hasUtmParamTracked) {
            Analytics.track(ANALYTICS_EVENTS.UTM_TRACKING, utmParams);
            hasUtmParamTracked = true;
          }
        }
      }
    } catch (error) {
      console.error('Error checking initial URL:', error);
    }
  };

  useEffect(() => {
    checkInitialUrl();
  }, []);
};
