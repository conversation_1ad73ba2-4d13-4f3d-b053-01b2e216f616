import _get from 'lodash/get';
import _find from 'lodash/find';

interface PuzzleStats {
  averageTime: number;
}

interface PuzzleData {
  puzzleType: string;
  stats: PuzzleStats | null;
}

type PuzzleDataArray = PuzzleData[];

const allPuzzleAvgTimeReader = {
  kenKenAvg: (puzzleDataArray: PuzzleDataArray) => {
    const kenKenPuzzle = _find(puzzleDataArray, { puzzleType: 'KenKen' });
    return _get(kenKenPuzzle, ['stats', 'averageTime'], 0);
  },
  crossMathAvg: (puzzleDataArray: PuzzleDataArray) => {
    const crossMathPuzzle = _find(puzzleDataArray, { puzzleType: 'CrossMath' });
    return _get(crossMathPuzzle, ['stats', 'averageTime'], 0);
  },
  mathMazeAvg: (puzzleDataArray: PuzzleDataArray) => {
    const mathMazePuzzle = _find(puzzleDataArray, { puzzleType: 'MathMaze' });
    return _get(mathMazePuzzle, ['stats', 'averageTime'], 0);
  },
  hectocAvg: (puzzleDataArray: PuzzleDataArray) => {
    const hectocPuzzle = _find(puzzleDataArray, { puzzleType: 'Hectoc' });
    return _get(hectocPuzzle, ['stats', 'averageTime'], 0);
  },
};

export default allPuzzleAvgTimeReader;
