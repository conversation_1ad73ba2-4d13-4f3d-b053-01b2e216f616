/* eslint-disable import/extensions */
import { gql, useMutation } from '@apollo/client';

import { CURRENT_USER_FRAGMENT } from '../../graphql/fragments/user';
import { useCallback } from 'react';
import { useStorageState } from 'core/hooks/useStorageState';

const LEGACY_GOOGLE_LOGIN_QUERY = gql`
  ${CURRENT_USER_FRAGMENT}
  mutation LegacyGoogleLogin($idToken: String!, $guestId: ID) {
    legacyGoogleLogin(idToken: $idToken, guestId: $guestId) {
      ...CurrentUserFields
      isSignup
    }
  }
`;

const useGoogleLoginQuery = () => {
  const [login] = useMutation(LEGACY_GOOGLE_LOGIN_QUERY);
  const [guestId] = useStorageState('guestId');

  const loginWithGoogle = useCallback(
    ({ idToken }) => {
      console.info('Sending Google login mutation with:', {
        hasIdToken: !!idToken,
        idTokenLength: idToken?.length,
        hasGuestId: !!guestId,
        guestId
      });

      return login({
        variables: {
          idToken,
          guestId,
        },
      });
    },
    [login, guestId],
  );

  return {
    loginWithGoogle,
  };
};

export default useGoogleLoginQuery;
