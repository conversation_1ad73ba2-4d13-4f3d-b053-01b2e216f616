import { useMutation, gql } from '@apollo/client';
import { useCallback } from 'react';

import { useStorageState } from 'core/hooks/useStorageState';
import { CURRENT_USER_FRAGMENT } from '../../graphql/fragments/user';

const GOOGLE_LOGIN_QUERY = gql`
  ${CURRENT_USER_FRAGMENT}
  mutation GoogleLogin($code: String!, $guestId: ID) {
    googleLogin(auth_code: $code, guestId: $guestId) {
      ...CurrentUserFields
      isSignup
    }
  }
`;

const useGoogleLoginQuery = () => {
  const [login] = useMutation(GOOGLE_LOGIN_QUERY);
  const [guestId] = useStorageState('guestId');

  const loginWithGoogle = useCallback(
    ({ code }) =>
      login({
        variables: {
          code,
          guestId,
        },
      }),
    [login, guestId],
  );

  return {
    loginWithGoogle,
  };
};

export default useGoogleLoginQuery;
