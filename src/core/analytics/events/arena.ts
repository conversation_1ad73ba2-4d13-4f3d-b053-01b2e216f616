export const EVENTS = {
  ARENA: {
    VIEWED_ARENA: 'view arena page',

    // fix rating banners
    VIEWED_DISCOVER_RATING_BANNER: 'view discover rating banner',
    CLICKED_ON_ARENA_DISCOVER_RATING_BANNER: 'click on discover rating banner',
    // discord banner
    VIEWED_JOIN_THE_FUN_BANNER: 'view join discord community banner',
    CLICKED_ON_ARENA_JOIN_THE_FUN_BANNER:
      'click on join discord community banner',
    CLICKED_ON_ARENA_ONLINE_USERS_IMAGE: 'arena: Click on online user image',
    CLICKED_ON_ARENA_ONLINE_USERS_SEE_MORE:
      'arena: Click on online users see more ',
    CLICKED_ON_ARENA_MOST_PLAYED: 'click on most played',
    CLICKED_ON_ARENA_ONLINE_DUELS: 'click on online duels',
    CLICKED_ON_ARENA_MOST_PLAYED_DUELS: 'click on most played duels',
    CLICKED_ON_ARENA_FRIEND: 'click on play with friend',
    CLICKED_ON_ARENA_GROUP_PLAY: 'click on play with group',
    CLICKED_ON_ARENA_PRACTICE: 'click on practice',
    CLICKED_ON_ARENA_FLASH_ANZAN: 'click on flash anzan',
    CLICKED_ON_ARENA_FASTEST_FINGER: 'click on fastest finger',
    CLICKED_ON_ARENA_DAILY_CHALLENGE: 'click on play daily challenge banner',
    CLICKED_ON_ARENA_LIVE_LEADERBOARD:
      'click on see daily challenge live leaderboard',
    CLICKED_ON_ARENA_PUZZLE_DUELS: 'click on puzzle duels',
    CLICKED_ON_ARENA_PLAY_NOW: 'click on play now button',
    CLICKED_ON_ARENA_PUZZLE_DAILY_CHALLENGE:
      'arena: Click on puzzle daily challenge',
    CLICKED_ON_ARENA_ABILITY_DUELS: 'arena: Click on ability duels',
    CLICKED_ON_ARENA_AGAINST_TIME: 'arena: Click on duels against time',
    CLICKED_ON_ARENA_PLAY_NOW_BUTTON: 'arena: Click on Play now',
    CLICKED_ON_SHORT_CUTS: `arena: Click on quick link `,
    CLICKED_ON_ARENA_FEED: 'arena: Click on Feed',
    CLICKED_ON_LEAGUES: 'arena: Click on Leagues',

    // Online User Bottom Sheet
    CLICKED_ON_ARENA_BOTTOM_SHEET_QUICK_CHALLENGE:
      'arena_BottomSheet: Click on quick challenge ',
    CLICKED_ON_ARENA_BOTTOM_SHEET_CROSS_MATH_CHALLENGE:
      'arena_BottomSheet: Click on Cross Math challenge',
    CLICKED_ON_ARENA_BOTTOM_SHEET_FASTEST_FINGER:
      'arena_BottomSheet: Click on fastest finger challenge',
    CLICKED_ON_ARENA_BOTTOM_SHEET_FLASH_ANZAN:
      'arena_BottomSheet: Click on flash anzan challenge',
    CLICKED_ON_ARENA_BOTTOM_SHEET_CUSTOM_CHALLENGE:
      'arena_BottomSheet: Click on custom challenge',
    CLICKED_ON_ARENA_BOTTOM_SHEET_USER_PROFILE:
      'arena_BottomSheet: Click on view users profile',

    // BottomNavBar
    CLICKED_ON_ARENA_BOTTOM_NAV_PUZZLE: 'arena_BottomNav: Click on Puzzle',
    CLICKED_ON_ARENA_BOTTOM_NAV_NETS: 'arena_BottomNav: Click on Nets',
    CLICKED_ON_ARENA_BOTTOM_NAV_COMPETE: 'arena_BottomNav: Click on Compete',
    CLICKED_ON_ARENA_BOTTOM_NAV_MORE: 'arena_BottomNav: Click on More',
  },
};
