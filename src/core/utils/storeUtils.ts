import { Platform, Linking } from 'react-native';
import { APPSTORE_LINK, PLAYSRORE_LINK } from '../constants/appConstants';

/**
 * Get the appropriate app store URL based on platform
 * @returns string - App store URL
 */
export const getAppStoreUrl = (): string => {
  if (Platform.OS === 'ios') {
    return APPSTORE_LINK;
  }
  return PLAYSRORE_LINK;
};

/**
 * Open the app store for the current platform
 * @returns Promise<void>
 */
export const openAppStore = async (): Promise<void> => {
  const storeUrl = getAppStoreUrl();
  await Linking.openURL(storeUrl);
};

/**
 * Check if the app store can be opened
 * @returns Promise<boolean>
 */
export const canOpenAppStore = async (): Promise<boolean> => {
  const storeUrl = getAppStoreUrl();
  try {
    return await Linking.canOpenURL(storeUrl);
  } catch {
    // Silently fail and return false
    return false;
  }
};
