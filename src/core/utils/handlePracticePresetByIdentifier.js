import _isNil from "lodash/isNil"
import {showPopover} from "molecules/Popover/Popover" 
import AddConfigOverlay from "@/src/modules/practice/components/AddConfigOverlay"

const handlePracticePresetByIdentifier = ({ identifier }) => {
    if (_isNil(identifier)) {
        return
    }

    showPopover({
        content: <AddConfigOverlay identifier={identifier} />,
        overlayLook: true,
        style: {
            borderRadius: 12,
            padding: 0,
            margin: 0
        }
    })
}

export default handlePracticePresetByIdentifier