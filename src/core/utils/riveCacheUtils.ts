import riveCacheManager from 'core/services/RiveCacheManager';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';

/**
 * Utility functions for Rive cache management
 */
export class RiveCacheUtils {
  /**
   * Get all Rive animation URLs from constants
   */
  static getAllRiveUrls(): string[] {
    const urls: string[] = [];

    const extractUrls = (obj: any) => {
      for (const key in obj) {
        if (typeof obj[key] === 'string' && obj[key].includes('.riv')) {
          urls.push(obj[key]);
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          extractUrls(obj[key]);
        }
      }
    };

    extractUrls(RIVE_ANIMATIONS);
    return [...new Set(urls)]; // Remove duplicates
  }

  /**
   * Get cache statistics
   */
  static async getCacheInfo() {
    if (!riveCacheManager.isReady()) {
      return {
        isReady: false,
        error: 'Cache manager not initialized',
      };
    }

    try {
      const stats = riveCacheManager.getCacheStats();
      const allUrls = this.getAllRiveUrls();

      // Get platform-specific info
      let platformInfo = {};
      if (Platform.OS === 'web') {
        platformInfo =
          (await (riveCacheManager as any).getCacheStorageInfo?.()) || {};
      } else {
        platformInfo =
          (await (riveCacheManager as any).getCacheDirectoryInfo?.()) || {};
      }

      return {
        isReady: true,
        stats,
        platformInfo,
        totalAnimations: allUrls.length,
        urls: allUrls,
      };
    } catch (error) {
      return {
        isReady: true,
        error: error.message,
      };
    }
  }

  /**
   * Clear all cached files
   */
  static async clearCache(): Promise<{ success: boolean; error?: string }> {
    try {
      await riveCacheManager.clearCache();
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Force re-cache all animations
   */
  static async recacheAllAnimations(
    onProgress?: (progress: any[]) => void,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      if (!riveCacheManager.isReady()) {
        await riveCacheManager.initialize();
      }

      await riveCacheManager.cacheAllAnimations(onProgress);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if a specific URL is cached
   */
  static async isUrlCached(url: string): Promise<boolean> {
    try {
      const cachedUrl = await riveCacheManager.getCachedUrl(url);
      return cachedUrl !== url; // If different, it's cached
    } catch {
      return false;
    }
  }

  /**
   * Get cached URL for a specific animation
   */
  static async getCachedUrl(url: string): Promise<string> {
    try {
      return await riveCacheManager.getCachedUrl(url);
    } catch {
      return url; // Fallback to original URL
    }
  }

  /**
   * Validate all animation URLs
   */
  static async validateAllUrls(): Promise<{
    valid: string[];
    invalid: string[];
    errors: Record<string, string>;
  }> {
    const urls = this.getAllRiveUrls();
    const valid: string[] = [];
    const invalid: string[] = [];
    const errors: Record<string, string> = {};

    for (const url of urls) {
      try {
        const response = await fetch(url, { method: 'HEAD' });
        if (response.ok) {
          valid.push(url);
        } else {
          invalid.push(url);
          errors[url] = `HTTP ${response.status}: ${response.statusText}`;
        }
      } catch (error) {
        invalid.push(url);
        errors[url] = error.message;
      }
    }

    return { valid, invalid, errors };
  }

  /**
   * Format file size for display
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
  }

  /**
   * Debug function to log cache information
   */
  static async debugCacheInfo(): Promise<void> {
    console.group('🎬 Rive Cache Debug Info');

    try {
      const info = await this.getCacheInfo();

      if (!info.isReady) {
        console.error('❌ Cache not ready:', info.error);
        console.groupEnd();
        return;
      }

      if (info.error) {
        console.error('❌ Cache error:', info.error);
        console.groupEnd();
        return;
      }

      console.log('✅ Cache Status: Ready');
      console.log('📊 Statistics:', info.stats);
      console.log('💾 Platform Info:', info.platformInfo);
      console.log('🎯 Total Animations:', info.totalAnimations);

      if (info.stats) {
        console.log(`📁 Cached Files: ${info.stats.totalFiles}`);
        console.log(
          `💽 Cache Size: ${this.formatFileSize(info.stats.totalSize)}`,
        );
        console.log(`🎯 Hit Rate: ${info.stats.hitRate.toFixed(2)}%`);
      }

      // Check individual URLs
      console.group('🔍 Individual URL Status');
      for (const url of info.urls || []) {
        const isCached = await this.isUrlCached(url);
        const filename = url.split('/').pop()?.split('?')[0] || 'unknown';
        console.log(`${isCached ? '✅' : '❌'} ${filename}`);
      }
      console.groupEnd();
    } catch (error) {
      console.error('❌ Debug failed:', error);
    }

    console.groupEnd();
  }
}

// Global debug function for development
if (__DEV__) {
  (global as any).debugRiveCache = RiveCacheUtils.debugCacheInfo;
  (global as any).clearRiveCache = RiveCacheUtils.clearCache;
  (global as any).recacheRiveAnimations = RiveCacheUtils.recacheAllAnimations;
}

export default RiveCacheUtils;
