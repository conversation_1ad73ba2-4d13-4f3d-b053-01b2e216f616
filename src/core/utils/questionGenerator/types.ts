export interface BaseConfig {
  noOfQuestions?: number;
  rows?: number;
  typingDirection?: string;
}

export interface BasePreset {
  id?: string;
  config: BaseConfig;
  categoryId: string;
}

export interface BaseQuestionType {
  id?: string;
  expression?: string[];
  answers?: string[] | number[];
  rows?: number;
  tag?: string;
  category?: string;
}

export interface RandomNumberOptions {
  min?: number;
  max?: number;
  excludeNumbers?: number[];
}
