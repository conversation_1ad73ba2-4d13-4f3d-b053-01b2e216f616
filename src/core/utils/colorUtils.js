export const withOpacity = (color, opacity) => {
    if (opacity < 0) opacity = 0
    if (opacity > 1) opacity = 1

    // Helper function to convert hex to RGB
    const hexToRgb = (hex) => {
        let r = 0,
            g = 0,
            b = 0
        // 3 digits hex
        if (hex.length === 4) {
            r = parseInt(hex[1] + hex[1], 16)
            g = parseInt(hex[2] + hex[2], 16)
            b = parseInt(hex[3] + hex[3], 16)
        }
        // 6 digits hex
        else if (hex.length === 7) {
            r = parseInt(hex[1] + hex[2], 16)
            g = parseInt(hex[3] + hex[4], 16)
            b = parseInt(hex[5] + hex[6], 16)
        }
        return [r, g, b]
    }

    // Check if color is in hex format
    if (color.startsWith('#')) {
        const [r, g, b] = hexToRgb(color)
        return `rgba(${r}, ${g}, ${b}, ${opacity})`
    }

    // Check if color is in rgb format
    if (color.startsWith('rgb(')) {
        return color.replace('rgb(', 'rgba(').replace(')', `, ${opacity})`)
    }

    return color
}
