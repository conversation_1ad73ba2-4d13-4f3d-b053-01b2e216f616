/**
 * Compare two version strings
 * @param currentVersion - Current app version (e.g., "1.8.000")
 * @param latestVersion - Latest app version (e.g., "1.9.000")
 * @returns number - 1 if latest > current, 0 if equal, -1 if current > latest
 */
export const compareVersions = (
  currentVersion: string,
  latestVersion: string,
): number => {
  if (!currentVersion || !latestVersion) {
    return 0;
  }

  const current = currentVersion.split('.').map((num) => parseInt(num, 10));
  const latest = latestVersion.split('.').map((num) => parseInt(num, 10));

  const maxLength = Math.max(current.length, latest.length);

  for (let i = 0; i < maxLength; i += 1) {
    const currentPart = current[i] || 0;
    const latestPart = latest[i] || 0;

    if (latestPart > currentPart) {
      return 1; // Update available
    }
    if (currentPart > latestPart) {
      return -1; // Current version is newer
    }
  }

  return 0; // Versions are equal
};

/**
 * Check if an update is available
 * @param currentVersion - Current app version
 * @param latestVersion - Latest app version
 * @returns boolean - true if update is available
 */
export const isUpdateAvailable = (
  currentVersion: string,
  latestVersion: string,
): boolean => compareVersions(currentVersion, latestVersion) === 1;

/**
 * Get update type based on version difference
 * @param currentVersion - Current app version
 * @param latestVersion - Latest app version
 * @returns string - 'major', 'minor', or 'patch'
 */
export const getUpdateType = (
  currentVersion: string,
  latestVersion: string,
): string => {
  if (!currentVersion || !latestVersion) {
    return 'patch';
  }

  const current = currentVersion.split('.').map((num) => parseInt(num, 10));
  const latest = latestVersion.split('.').map((num) => parseInt(num, 10));

  if ((latest[0] || 0) > (current[0] || 0)) {
    return 'major';
  }
  if ((latest[1] || 0) > (current[1] || 0)) {
    return 'minor';
  }
  return 'patch';
};
