import mime from 'mime';
import { Platform } from 'react-native';
import { ReactNativeFile } from 'apollo-upload-client';

const convertBlobUrlToFile = async (blobUrl, fileName) => {
  try {
    const response = await fetch(blobUrl);
    const blob = await response.blob();
    return new File([blob], fileName, { type: blob.type || 'image/jpeg' });
  } catch (error) {
    console.error('Error converting blob URL to file:', error);
    throw new Error('Failed to convert blob URL to file');
  }
};

const convertBase64ToFile = (fileUri, fileName) => {
  try {
    if (!fileUri.includes(',')) {
      throw new Error('Invalid base64 data URL format');
    }

    const [metadata, base64Data] = fileUri.split(',');

    if (!base64Data || base64Data.trim() === '') {
      throw new Error('Empty base64 data');
    }

    let binaryData;
    try {
      binaryData = atob(base64Data);
    } catch (error) {
      throw new Error(`Invalid base64 encoding: ${error.message}`);
    }

    const byteArray = new Uint8Array(binaryData.length);
    for (let i = 0; i < binaryData.length; i++) {
      byteArray[i] = binaryData.charCodeAt(i);
    }

    let mimeType = 'image/jpeg';
    if (metadata && metadata.includes(':')) {
      const typeMatch = metadata.match(/data:([^;]+)/);
      if (typeMatch) {
        mimeType = typeMatch[1];
      }
    }

    const blob = new Blob([byteArray], { type: mimeType });
    return new File([blob], fileName, { type: blob.type });
  } catch (error) {
    console.error('Error converting base64 to file:', error);
    throw error;
  }
};

export const base64ToFile = async ({ fileUri, fileName }) => {
  if (Platform.OS !== 'web') {
    return new ReactNativeFile({
      uri: fileUri,
      type: mime.getType(fileUri) || 'image',
      name: fileName,
    });
  }

  try {
    if (fileUri.startsWith('blob:')) {
      return await convertBlobUrlToFile(fileUri, fileName);
    }

    if (fileUri.startsWith('data:')) {
      return convertBase64ToFile(fileUri, fileName);
    }

    if (fileUri.startsWith('http://') || fileUri.startsWith('https://')) {
      return await convertBlobUrlToFile(fileUri, fileName);
    }

    if (fileUri.includes(',')) {
      return convertBase64ToFile(fileUri, fileName);
    }

    throw new Error(
      `Unsupported file URI format: ${fileUri.substring(0, 50)}...`,
    );
  } catch (error) {
    console.error('Error in base64ToFile:', error);
    throw new Error(
      `Failed to convert file URI to File object: ${error.message}`,
    );
  }
};
