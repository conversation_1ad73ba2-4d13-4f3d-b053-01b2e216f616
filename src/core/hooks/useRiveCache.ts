import { useCallback, useEffect, useState } from 'react';
import type {
  CacheStats,
  DownloadProgress,
} from 'core/services/RiveCacheManager';
import riveCacheManager from 'core/services/RiveCacheManager';

export interface UseRiveCacheReturn {
  // State
  isInitialized: boolean;
  isInitializing: boolean;
  isCaching: boolean;
  downloadProgress: DownloadProgress[];
  cacheStats: CacheStats | null;
  error: string | null;

  // Methods
  initializeCache: () => Promise<void>;
  cacheAllAnimations: () => Promise<void>;
  getCachedUrl: (url: string) => Promise<string>;
  clearCache: () => Promise<void>;
  refreshStats: () => void;
}

export const useRiveCache = (): UseRiveCacheReturn => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [isCaching, setIsCaching] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState<DownloadProgress[]>(
    [],
  );
  const [cacheStats, setCacheStats] = useState<CacheStats | null>(null);
  const [error, setError] = useState<string | null>(null);

  /**
   * Initialize the cache manager
   */
  const initializeCache = useCallback(async () => {
    if (isInitializing || isInitialized) return;

    setIsInitializing(true);
    setError(null);

    try {
      await riveCacheManager.initialize();
      setIsInitialized(true);

      // Update stats after initialization
      const stats = riveCacheManager.getCacheStats();
      setCacheStats(stats);
    } catch (err) {
      const errorMessage = err?.message || 'Failed to initialize cache';
      setError(errorMessage);
      console.error('Cache initialization failed:', err);
    } finally {
      setIsInitializing(false);
    }
  }, [riveCacheManager, isInitializing, isInitialized]);

  /**
   * Cache all animations
   */
  const cacheAllAnimations = useCallback(async () => {
    if (!isInitialized || isCaching) return;

    setIsCaching(true);
    setError(null);
    setDownloadProgress([]);

    try {
      await riveCacheManager.cacheAllAnimations((progress) => {
        setDownloadProgress([...progress]);
      });

      // Update stats after caching
      const stats = riveCacheManager.getCacheStats();
      setCacheStats(stats);
    } catch (err) {
      const errorMessage = err?.message || 'Failed to cache animations';
      setError(errorMessage);
      console.error('Animation caching failed:', err);
    } finally {
      setIsCaching(false);
    }
  }, [riveCacheManager, isInitialized, isCaching]);

  /**
   * Get cached URL for an animation
   */
  const getCachedUrl = useCallback(
    async (url: string): Promise<string> => {
      try {
        return await riveCacheManager.getCachedUrl(url);
      } catch (err) {
        console.warn(`Failed to get cached URL for ${url}:`, err);
        return url; // Fallback to original URL
      }
    },
    [riveCacheManager],
  );

  /**
   * Clear all cached files
   */
  const clearCache = useCallback(async () => {
    try {
      await riveCacheManager.clearCache();
      setCacheStats({
        totalFiles: 0,
        totalSize: 0,
        hitRate: 0,
        lastCleanup: Date.now(),
      });
      setDownloadProgress([]);
    } catch (err) {
      const errorMessage = err?.message || 'Failed to clear cache';
      setError(errorMessage);
      console.error('Cache clearing failed:', err);
    }
  }, [riveCacheManager]);

  /**
   * Refresh cache statistics
   */
  const refreshStats = useCallback(() => {
    if (isInitialized) {
      const stats = riveCacheManager.getCacheStats();
      setCacheStats(stats);
    }
  }, [riveCacheManager, isInitialized]);

  /**
   * Auto-initialize on mount
   */
  useEffect(() => {
    initializeCache();
  }, [initializeCache]);

  /**
   * Check if cache manager is ready
   */
  useEffect(() => {
    const checkReady = () => {
      const ready = riveCacheManager.isReady();
      if (ready !== isInitialized) {
        setIsInitialized(ready);
        if (ready) {
          refreshStats();
        }
      }
    };

    const interval = setInterval(checkReady, 1000);
    return () => clearInterval(interval);
  }, [riveCacheManager, isInitialized, refreshStats]);

  return {
    // State
    isInitialized,
    isInitializing,
    isCaching,
    downloadProgress,
    cacheStats,
    error,

    // Methods
    initializeCache,
    cacheAllAnimations,
    getCachedUrl,
    clearCache,
    refreshStats,
  };
};

/**
 * Hook for getting a cached URL for a specific animation
 */
export const useRiveCachedUrl = (originalUrl: string) => {
  const [cachedUrl, setCachedUrl] = useState<string>(originalUrl);
  const [isLoading, setIsLoading] = useState(true);
  const { getCachedUrl, isInitialized } = useRiveCache();

  useEffect(() => {
    if (!isInitialized) {
      setCachedUrl(originalUrl);
      setIsLoading(false);
      return;
    }

    const loadCachedUrl = async () => {
      setIsLoading(true);
      try {
        const cached = await getCachedUrl(originalUrl);
        setCachedUrl(cached);
      } catch (error) {
        console.warn('Failed to get cached URL:', error);
        setCachedUrl(originalUrl);
      } finally {
        setIsLoading(false);
      }
    };

    loadCachedUrl();
  }, [originalUrl, getCachedUrl, isInitialized]);

  return {
    url: cachedUrl,
    isLoading,
    isCached: cachedUrl !== originalUrl,
  };
};

export default useRiveCache;
