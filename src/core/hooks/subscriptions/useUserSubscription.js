import { gql, useSubscription } from '@apollo/client'
import { useEffect, useState } from 'react'

const USER_EVENT_SUBSCRIPTION = gql`
    subscription UserEventSubscription($userId: ID) {
        userEvents(userId: $userId) {
            __typename
            ... on RematchRequestOutput {
                gameId
                requestedBy
                status
                user {
                    _id
                    email
                    token
                    name
                    profileImageUrl
                    rating
                    countryCode
                    isGuest
                    isBot
                    globalRank
                    previousGlobalRank
                    countryRank
                    previousCountryRank
                }
                newGameId
                waitingTime
            }
            ... on BadgeAssignedEvent {
                initialBadge
                newBadge
            }
        }
    }
`

const useUserSubscription = (userId) => {
    const [rematchEvent, setRematchEvent] = useState(null)

    const { data, loading } = useSubscription(USER_EVENT_SUBSCRIPTION, {
        variables: { userId },
    })

    const { __typename } = data?.userEvents ?? EMPTY_OBJECT

    useEffect(() => {
        switch (__typename) {
            case 'RematchRequestOutput':
            case 'BadgeAssignedEvent':
        }
    }, [__typename])

    return { rematchEvent: '', loading: false }
}

export default useUserSubscription
