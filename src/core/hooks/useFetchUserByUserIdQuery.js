import {gql, useLazyQuery} from '@apollo/client';
import {useCallback} from 'react';

import {USER_FRAGMENT} from '../graphql/fragments/user';

const FETCH_USER_BY_ID_QUERY = gql`
    ${USER_FRAGMENT}
    query GetUserById($userId: ID!) {
        user: getUserById(userId: $userId) {
            ...CoreUserFields
        }
    }
`;

const useFetchUserByUserIdQuery = () => {
    const [fetchUser, {data, loading, error}] = useLazyQuery(
        FETCH_USER_BY_ID_QUERY,
    );

    const fetchUserById = useCallback(
        ({userId, queryOptions = EMPTY_OBJECT}) =>
            fetchUser({
                variables: {userId},
                fetchPolicy: 'cache-and-network',
                ...queryOptions,
            }),
        [fetchUser],
    );

    return {
        fetchUserById,
        user: data?.user,
        loading,
        error,
    };
};

export default useFetchUserByUserIdQuery;
