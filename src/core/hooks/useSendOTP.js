import { gql, useMutation } from '@apollo/client';
import { useCallback, useState } from 'react';

const SEND_OTP_MUTATION = gql`
  mutation SendOTP($email: String!) {
    sendOTP(email: $email)
  }
`;

const useSendOTP = () => {
  const [sendOTPMutationQuery] = useMutation(SEND_OTP_MUTATION);

  const [isSendingOTP, setIsSendingOTP] = useState(false);

  const sendOTPToEmail = useCallback(
    async ({ email }) => {
      if (isSendingOTP) {
        return false;
      }
      try {
        setIsSendingOTP(true);
        const responseObj = await sendOTPMutationQuery({
          variables: {
            email,
          },
        });

        const { data } = responseObj ?? EMPTY_OBJECT;
        const { sendOTP: optSent } = data ?? EMPTY_OBJECT;
        return optSent;
      } catch (e) {
        setIsSendingOTP(false);
        return false;
      } finally {
        setIsSendingOTP(false);
      }
    },
    [sendOTPMutationQuery, isSendingOTP],
  );

  return {
    sendOTPToEmail,
    isSendingOTP,
  };
};

export default useSendOTP;
