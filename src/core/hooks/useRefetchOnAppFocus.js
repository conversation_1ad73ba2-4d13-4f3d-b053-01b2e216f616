import { useEffect, useRef } from 'react';
import { AppState } from 'react-native';

const useRefetchOnAppFocus = (refetch) => {
  const appState = useRef(AppState.currentState);

  useEffect(() => {
    const handleAppStateChange = (nextAppState) => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        refetch?.();
      }
      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      subscription.remove();
    };
  }, [refetch]);
};

export default useRefetchOnAppFocus;
