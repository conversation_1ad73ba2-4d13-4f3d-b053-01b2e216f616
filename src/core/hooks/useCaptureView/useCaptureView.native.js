import { captureRef } from 'react-native-view-shot';
import {useCallback} from "react";
import { PixelRatio, Share as NativeShare } from 'react-native';

import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import * as Clipboard from "expo-clipboard";

import { showToast, TOAST_TYPE } from 'molecules/Toast'

const useCaptureView = () => {
    const targetPixelCount = 1080; // If you want full HD pictures
    const pixelRatio = PixelRatio.get(); // The pixel ratio of the device

    const pixels = targetPixelCount / pixelRatio;

    const copyDescriptionToClipboard = useCallback(async(text) => {
        await Clipboard.setStringAsync(text)
        showToast({
            type: TOAST_TYPE.SUCCESS,
            description: 'Copied content to clipboard, paste it on the social channel you are sharing',
        })
    }, []);

    const captureView = useCallback(async ({ viewRef }) => {
        const uri = await captureRef(viewRef, {
            result: 'matiksGameResult',
            height: pixels,
            width: pixels,
            quality: 1,
            format: 'png',
        });

        const fileUri = FileSystem.cacheDirectory + 'matiksGameResult.png';

        await FileSystem.moveAsync({
            from: uri,
            to: fileUri,
        });

        copyDescriptionToClipboard('WON! the Mental Aptitude game on Matiks 😇, Challenge me on https://www.matiks.in')

        if (await Sharing.isAvailableAsync()) {
            await Sharing.shareAsync(fileUri);
        } else {
            NativeShare.share({
                title: 'I WON!',
                message: 'I won the game on Matiks, Have a look 😇',
                url: uri,
            });
        }
    }, [pixels, copyDescriptionToClipboard]);

    return {
        captureView
    }
}

export default useCaptureView;