import useIsPlaying from '@/src/overlays/hooks/useIsPlaying';
import useTriggerPointsStore from '@/src/store/useTriggerPointsStore';
import {TriggerPoint, TriggerPointEvent} from '@/src/store/useTriggerPointsStore/types';
import {useCallback, useEffect, useRef} from 'react';
import useAppInitialize from 'core/hooks/appInitialization/useAppInitialize';
import useTriggerHandlers from './useTriggerHandlers';

const useTriggerEventsHandler = () => {
    const isPlayingGame = useIsPlaying();
    const {handleNotificationRequest} = useTriggerHandlers();
    const {isAppReady: isAppRunning} = useAppInitialize();

    const {
        getCurrentTriggerPoint,
        triggerPointsQueue,
    } = useTriggerPointsStore((state) => ({
        getCurrentTriggerPoint: state.getCurrentTriggerPoint,
        triggerPointsQueue: state.triggerPointsQueue,
    }));

    const handleTriggerEvent = useCallback(
        (event?: TriggerPoint) => {
            if (!isAppRunning || isPlayingGame) return;
            const _event = event || getCurrentTriggerPoint();
            if (!_event) return;
            switch (_event.eventTriggered) {
                case TriggerPointEvent.NOTIFICATION_REQUEST:
                    handleNotificationRequest();
                    break;
                default:
                    break;
            }
        },
        [isAppRunning, isPlayingGame, getCurrentTriggerPoint, handleNotificationRequest],
    );

    const triggerEventsRef = useRef(handleTriggerEvent);
    triggerEventsRef.current = handleTriggerEvent;
    useEffect(() => {
        if (triggerPointsQueue.length > 0) {
            triggerEventsRef.current();
        }
    }, [triggerPointsQueue]);
};

export default useTriggerEventsHandler;
