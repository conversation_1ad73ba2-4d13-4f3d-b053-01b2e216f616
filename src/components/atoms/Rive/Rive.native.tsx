import React, { useCallback, useEffect, useRef, useState } from 'react';
import Rive, { Fit, RiveRef } from 'rive-react-native';
import {
  AppState,
  AppStateStatus,
  NativeSyntheticEvent,
  Platform,
  Text,
  View,
  ViewStyle,
} from 'react-native';
import dark from 'core/constants/themes/dark';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { useRiveCachedUrl } from 'core/hooks/useRiveCache';
import { isValidRiveUrl } from 'atoms/Rive/utils/riveUrlValidation';

interface RiveComponentProps {
  url: string;
  resourceName?: string;
  artboardName?: string;
  stateMachineName?: string;
  style?: ViewStyle;
  autoPlay?: boolean;
  loop?: boolean;
  fit: Fit;
  onLoopEnd?: () => void;
  fallbackText?: string;
  maxRetries?: number;
  retryDelay?: number;
  enableCrashPrevention?: boolean;
  riveRef?: React.Ref<RiveRef>;
  onStateChanged?: (
    event: NativeSyntheticEvent<{
      stateMachineName: string;
      stateName: string;
    }>,
  ) => void;
}

interface RiveState {
  hasError: boolean;
  isLoading: boolean;
  retryCount: number;
  errorType: 'network' | 'file' | 'runtime' | 'cache' | 'unknown';
  lastError?: Error;
  forceBypassCache?: boolean;
}

const DEFAULT_FALLBACK_TEXT = 'Animation unavailable';
const DEFAULT_MAX_RETRIES = 3; // Increased from 2
const DEFAULT_RETRY_DELAY = 1000;

const isNetworkError = (error: any): boolean => {
  const errorMessage = error?.message?.toLowerCase() || '';
  return (
    errorMessage.includes('network') ||
    errorMessage.includes('timeout') ||
    errorMessage.includes('404') ||
    errorMessage.includes('fetch') ||
    errorMessage.includes('connection') ||
    errorMessage.includes('unreachable')
  );
};

const isFileCorruptionError = (error: any): boolean => {
  const errorMessage = error?.message?.toLowerCase() || '';
  return (
    errorMessage.includes('corrupt') ||
    errorMessage.includes('invalid') ||
    errorMessage.includes('malformed') ||
    errorMessage.includes('parse') ||
    errorMessage.includes('format') ||
    errorMessage.includes('decode') ||
    errorMessage.includes('header') ||
    errorMessage.includes('signature')
  );
};

const isCacheError = (error: any): boolean => {
  const errorMessage = error?.message?.toLowerCase() || '';
  return (
    errorMessage.includes('cache') ||
    errorMessage.includes('storage') ||
    errorMessage.includes('filesystem') ||
    errorMessage.includes('file system')
  );
};

// Add cache-busting parameter to URL
const addCacheBuster = (url: string): string => {
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}cb=${Date.now()}&v=${Math.random().toString(36).substr(2, 9)}`;
};

// Validate file by checking headers/signature
const validateRiveFile = async (url: string): Promise<boolean> => {
  try {
    const response = await fetch(url, {
      method: 'HEAD',
      headers: {
        'Cache-Control': 'no-cache',
        Pragma: 'no-cache',
      },
    });

    if (!response.ok) return false;

    const contentType = response.headers.get('content-type');
    const contentLength = response.headers.get('content-length');

    // Basic validation
    if (!contentLength || parseInt(contentLength) === 0) {
      return false;
    }

    // Check if it's a reasonable file size (not too small)
    if (parseInt(contentLength) < 100) {
      return false;
    }

    return true;
  } catch (error) {
    console.warn('File validation failed:', error);
    return false;
  }
};

const RiveComponent: React.FC<RiveComponentProps> = (props) => {
  const {
    url,
    resourceName,
    artboardName,
    stateMachineName,
    style,
    autoPlay = true,
    fit = Fit.Contain,
    loop = true,
    onLoopEnd,
    fallbackText = DEFAULT_FALLBACK_TEXT,
    maxRetries = DEFAULT_MAX_RETRIES,
    retryDelay = DEFAULT_RETRY_DELAY,
    enableCrashPrevention = true,
    riveRef,
    onStateChanged,
  } = props;

  // Use cached URL with option to bypass cache
  const {
    url: cachedUrl,
    isLoading: isCacheLoading,
    isCached,
    clearCache, // Assume this function exists in your cache hook
  } = useRiveCachedUrl(url);

  const [state, setState] = useState<RiveState>({
    hasError: false,
    isLoading: true,
    retryCount: 0,
    errorType: 'unknown',
    forceBypassCache: false,
  });

  const shouldUseCachedUrl =
    Platform.OS !== 'android' && !state.forceBypassCache;
  const urlToUse = shouldUseCachedUrl ? cachedUrl : url;

  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const appStateRef = useRef(AppState.currentState);
  const isComponentMountedRef = useRef(true);
  const validationAttemptRef = useRef(false);

  const memoizedStyle = React.useMemo(() => style, [style]);

  useEffect(() => {
    setState({
      hasError: false,
      isLoading: true,
      retryCount: 0,
      errorType: 'unknown',
      forceBypassCache: false,
    });
    validationAttemptRef.current = false;
  }, [url]);

  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (
        appStateRef.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        if (
          state.hasError &&
          (state.errorType === 'runtime' || state.errorType === 'file')
        ) {
          setState((prev) => ({
            ...prev,
            hasError: false,
            isLoading: true,
            retryCount: 0,
            errorType: 'unknown',
            forceBypassCache: prev.errorType === 'file', // Force bypass cache for file errors
          }));
        }
      }
      appStateRef.current = nextAppState;
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );
    return () => subscription?.remove();
  }, [state.hasError, state.errorType, url]);

  useEffect(
    () => () => {
      isComponentMountedRef.current = false;
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }

      // Clean up Rive ref to prevent memory leaks
      try {
        if (riveRef && typeof riveRef === 'object' && 'current' in riveRef) {
          const ref = riveRef.current;
          if (ref) {
            if (typeof ref.stop === 'function') {
              ref.stop();
            }
            riveRef.current = null;
          }
        }
      } catch (error) {
        console.warn('Error cleaning up Rive ref:', error);
      }
    },
    [riveRef],
  );

  const clearCacheAndRetry = useCallback(async () => {
    if (!isComponentMountedRef.current) return;

    try {
      // Clear cache if available
      if (clearCache) {
        await clearCache();
      }

      setState((prev) => ({
        ...prev,
        hasError: false,
        isLoading: true,
        retryCount: prev.retryCount + 1,
        forceBypassCache: true,
      }));
    } catch (error) {
      console.warn('Cache clearing failed:', error);
    }
  }, [clearCache]);

  const retryLoad = useCallback(async () => {
    if (!isComponentMountedRef.current) return;

    if (state.retryCount < maxRetries) {
      // For file corruption errors, try to validate and clear cache
      if (state.errorType === 'file' && !validationAttemptRef.current) {
        validationAttemptRef.current = true;

        // Try to validate the file first
        const isValid = await validateRiveFile(urlToUse || url);

        if (!isValid) {
          // File is corrupted, clear cache and use direct URL
          await clearCacheAndRetry();
          return;
        }
      }

      setState((prev) => ({
        ...prev,
        hasError: false,
        isLoading: true,
        retryCount: prev.retryCount + 1,
        // Force bypass cache for file errors after first retry
        forceBypassCache: prev.forceBypassCache || prev.errorType === 'file',
      }));

      Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_RETRY, {
        url,
        retryCount: state.retryCount + 1,
        errorType: state.errorType,
        maxRetries,
        forceBypassCache: state.forceBypassCache,
      });
    }
  }, [
    state.retryCount,
    state.errorType,
    state.forceBypassCache,
    maxRetries,
    url,
    urlToUse,
    clearCacheAndRetry,
  ]);

  const handleError = useCallback(
    (error: any) => {
      if (!isComponentMountedRef.current) return;

      let errorType: RiveState['errorType'] = 'unknown';

      if (isNetworkError(error)) {
        errorType = 'network';
      } else if (isFileCorruptionError(error)) {
        errorType = 'file';
      } else if (isCacheError(error)) {
        errorType = 'cache';
      } else {
        errorType = 'runtime';
      }

      setState((prev) => ({
        ...prev,
        hasError: true,
        isLoading: false,
        errorType,
        lastError: error,
      }));

      Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_ERROR, {
        url,
        errorType,
        errorMessage: error?.message || 'Unknown error',
        retryCount: state.retryCount,
        artboardName,
        stateMachineName,
        platform: Platform.OS,
        forceBypassCache: state.forceBypassCache,
      });

      // Retry logic based on error type
      if (state.retryCount < maxRetries) {
        const shouldRetry =
          errorType === 'network' ||
          errorType === 'file' ||
          errorType === 'cache';

        if (shouldRetry) {
          const delay = errorType === 'file' ? retryDelay * 2 : retryDelay;
          retryTimeoutRef.current = setTimeout(
            () => {
              retryLoad();
            },
            delay * (state.retryCount + 1),
          );
        }
      }
    },
    [
      state.retryCount,
      state.forceBypassCache,
      maxRetries,
      retryDelay,
      url,
      artboardName,
      stateMachineName,
      retryLoad,
    ],
  );

  const handleLoad = useCallback(() => {
    if (!isComponentMountedRef.current) return;

    setState((prev) => ({
      ...prev,
      hasError: false,
      isLoading: false,
    }));

    Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_LOADED, {
      url,
      cachedUrl,
      isCached,
      retryCount: state.retryCount,
      artboardName,
      stateMachineName,
      platform: Platform.OS,
      forceBypassCache: state.forceBypassCache,
    });
  }, [
    url,
    cachedUrl,
    isCached,
    state.retryCount,
    state.forceBypassCache,
    artboardName,
    stateMachineName,
  ]);

  const handleLoopEnd = useCallback(() => {
    try {
      onLoopEnd?.();
    } catch (error) {
      Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_CALLBACK_ERROR, {
        url,
        errorMessage: error?.message || 'Loop end callback error',
      });
    }
  }, [onLoopEnd, url]);

  // Prepare final URL with cache busting if needed
  const finalUrl = React.useMemo(() => {
    if (!urlToUse) return undefined;

    // Add cache busting for Android or when bypassing cache
    if (Platform.OS === 'android' || state.forceBypassCache) {
      return addCacheBuster(urlToUse);
    }

    return urlToUse;
  }, [urlToUse, state.forceBypassCache]);

  const onRiveEventReceived = (event) => {
    const eventProperties = event.properties;
    console.info(eventProperties, event, 'infooooooo');
  };

  const riveProps = {
    stateMachineName,
    artboardName,
    style: memoizedStyle,
    onLoopEnd: handleLoopEnd,
    onError: handleError,
    onPlay: handleLoad,
    onStateChanged,
    autoplay: autoPlay,
    fit,
    ...(finalUrl ? { url: finalUrl } : {}),
    ...(resourceName ? { resourceName } : {}),
  };

  // Check if we have either a valid URL or a resourceName
  const hasValidSource = (url && isValidRiveUrl(url)) || resourceName;

  if ((isCacheLoading && shouldUseCachedUrl) || !hasValidSource) {
    return (
      <View style={memoizedStyle}>
        <Text style={{ color: dark.colors.textDark, textAlign: 'center' }}>
          {isCacheLoading ? 'Loading...' : fallbackText}
        </Text>
      </View>
    );
  }

  if (state.hasError) {
    return (
      <View style={memoizedStyle}>
        <Text style={{ color: dark.colors.textDark, textAlign: 'center' }}>
          {fallbackText}
        </Text>
      </View>
    );
  }

  if (enableCrashPrevention) {
    try {
      return <Rive {...riveProps} ref={riveRef} />;
    } catch (error) {
      handleError(error);
      return (
        <View style={memoizedStyle}>
          <Text style={{ color: dark.colors.textDark, textAlign: 'center' }}>
            {fallbackText}
          </Text>
        </View>
      );
    }
  }

  return <Rive {...riveProps} ref={riveRef} />;
};

export default React.memo(RiveComponent);
