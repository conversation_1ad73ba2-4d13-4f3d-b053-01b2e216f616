import React from 'react';
import { fireEvent, render } from '@testing-library/react-native';
import InteractivePrimaryButton from '../index';

describe('InteractivePrimaryButton', () => {
  it('renders without crashing', () => {
    render(
      <InteractivePrimaryButton
        label="Test Button"
        onPress={() => {}}
        buttonStyle={{}}
        buttonContentStyles={{}}
        buttonBorderBackgroundStyle={{}}
      />,
    );
  });

  it('calls onPress when pressed', () => {
    const onPress = jest.fn();
    const { getByText } = render(
      <InteractivePrimaryButton
        label="Test Button"
        onPress={onPress}
        buttonStyle={{}}
        buttonContentStyles={{}}
        buttonBorderBackgroundStyle={{}}
      />,
    );
    const button = getByText('Test Button');
    fireEvent.press(button);
    expect(onPress).toHaveBeenCalled();
  });
});
