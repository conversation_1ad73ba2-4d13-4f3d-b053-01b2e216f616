import React from 'react';
import { Text, StyleSheet } from 'react-native';

interface NumberToSvgProps {
  number: string | number;
  fontSize?: number;
  color?: string;
}

const styles = StyleSheet.create({
  text: {
    fontFamily: 'Montserrat-600', // Using the Montserrat-SemiBold font
  },
});

const NumberToSvg: React.FC<NumberToSvgProps> = ({
  number,
  fontSize = 100,
  color = 'white',
}) => (
  <Text
    style={[styles.text, { fontSize, color }]}
    allowFontScaling={false}
  >
    {number}
  </Text>
);

export default React.memo(NumberToSvg);
