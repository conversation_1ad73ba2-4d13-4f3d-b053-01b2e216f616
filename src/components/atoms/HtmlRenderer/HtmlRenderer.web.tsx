import React from 'react';

interface HtmlRendererProps {
  html: string;
  customStyles?: React.CSSProperties;
}

const HtmlRenderer = ({ html, customStyles = {} }: HtmlRendererProps) => {
  const defaultStyles: React.CSSProperties = {
    fontFamily: 'Montserrat-500',
    fontSize: '16px',
    lineHeight: '1.5',
    color: 'white',
  };

  const mergedStyles: React.CSSProperties = {
    ...defaultStyles,
    ...customStyles,
  };

  return (
    <div style={mergedStyles} dangerouslySetInnerHTML={{ __html: html }} />
  );
};

export default React.memo(HtmlRenderer);
