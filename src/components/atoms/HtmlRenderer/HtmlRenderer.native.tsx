import React from 'react';
import { useWindowDimensions } from 'react-native';
import RenderHtml, { defaultSystemFonts } from 'react-native-render-html';

const EMPTY_OBJECT = {};

interface HtmlRendererProps {
  html: string;
  customStyles?: any;
}

const HtmlRenderer = ({
  html,
  customStyles = EMPTY_OBJECT,
}: HtmlRendererProps) => {
  const { width } = useWindowDimensions();

  const source = { html };
  const systemFonts = [
    ...defaultSystemFonts,
    'Montserrat-500',
    'Montserrat-600',
    'Montserrat-700',
    'Montserrat-800',
  ];

  const defaultStyles = {
    body: { fontSize: 14, color: 'white', fontFamily: 'Montserrat-500' },
    a: { color: 'white', fontFamily: 'Montserrat-600' },
    h1: {
      fontSize: 22,
      fontFamily: 'Montserrat-700',
      marginVertical: 10,
      color: 'white',
    },
    h2: {
      fontSize: 18,
      fontFamily: 'Montserrat-800',
      marginVertical: 8,
      color: 'white',
    },
    p: {
      marginVertical: 5,
      fontSize: 14,
      color: 'white',
      fontFamily: 'Montserrat-600',
    },
    ul: { marginLeft: 20, color: 'white', fontFamily: 'Montserrat-500' },
    li: { marginBottom: 5, color: 'white', fontFamily: 'Montserrat-500' },
  };

  const mergedStyles = { ...defaultStyles, ...customStyles };

  return (
    <RenderHtml
      contentWidth={width}
      source={source}
      tagsStyles={mergedStyles}
      systemFonts={systemFonts}
    />
  );
};

export default React.memo(HtmlRenderer);
