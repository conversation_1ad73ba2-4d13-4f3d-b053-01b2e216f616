import React from 'react';
import { fireEvent, render } from '@testing-library/react-native';
import PrimaryButton from '../index';

describe('Button Component', () => {
  test('renders correctly with label', () => {
    const { getByText } = render(
      <PrimaryButton label="Press Me" onPress={() => {}} />,
    );
    expect(getByText('Press Me')).toBeTruthy();
  });

  test('calls onPress when pressed', () => {
    const onPressMock = jest.fn();
    const { getByText } = render(
      <PrimaryButton label="Press Me" onPress={onPressMock} />,
    );

    fireEvent.press(getByText('Press Me'));
    expect(onPressMock).toHaveBeenCalledTimes(1);
  });

  test('is disabled when disabled prop is true', () => {
    const onPressMock = jest.fn();
    const { getByText } = render(
      <PrimaryButton label="Press Me" onPress={onPressMock} disabled />,
    );

    fireEvent.press(getByText('Press Me'));

    expect(onPressMock).not.toHaveBeenCalled();
  });
});
