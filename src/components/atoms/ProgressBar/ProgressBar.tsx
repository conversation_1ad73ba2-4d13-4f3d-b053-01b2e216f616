import React from 'react';
import { View } from 'react-native';
import LinearGradient from '../LinearGradient';
import { styles } from './ProgressBar.style';

interface ProgerssBarProps {
  currentStep: number;
  totalSteps: number;
}

const ProgressBar = (props: ProgerssBarProps) => {
  return (
    <View style={styles.progressContainer}>
      {Array.from({ length: props.totalSteps }, (_, index) => {
        const isCompleted = index < props.currentStep - 1; // Previous steps
        const isActive = index === props.currentStep - 1; // Current step

        return (
          <View
            key={index}
            style={[
              styles.progressStep,
              isActive
                ? styles.progressStepActive // Partially filled
                : styles.progressStepInactive, // Not filled
            ]}
          >
            {/* Gradient background for completed steps */}
            {isCompleted && (
              <LinearGradient
                colors={['rgba(169, 249, 158, 1)', 'rgba(0, 217, 255, 1)']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.progressStepCompletedBackground}
              />
            )}
            {/* Partial fill for active step */}
            {isActive && (
              <LinearGradient
                colors={['rgba(169, 249, 158, 1)', 'rgba(0, 217, 255, 1)']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.progressStepActiveFill}
              />
            )}
          </View>
        );
      })}
    </View>
  );
};

export default ProgressBar;
