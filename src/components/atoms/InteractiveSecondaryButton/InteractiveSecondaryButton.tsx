import Icon from 'atoms/Icon';
import Haptics from 'core/container/Haptics';
import _size from 'lodash/size';
import React, { forwardRef, useCallback } from 'react';
import { StyleProp, Text, View, ViewStyle } from 'react-native';
import styles from './InteractiveSecondaryButton.style';
import Pressable from '../Pressable';

interface InteractiveSecondaryButtonProps {
  testID?: string;
  label?: string;
  onPress: () => void;
  buttonStyle?: StyleProp<ViewStyle>;
  iconConfig?: any;
  labelStyle?: any;
  buttonContentStyle?: any;
  type?: string;
  borderColor?: string;
  borderComponentStyle?: any;
  buttonContainerStyle?: StyleProp<ViewStyle>;
  buttonBackgroundStyle?: any;
  disabled?: boolean;
}

const InteractiveSecondaryButton = forwardRef<
  View,
  InteractiveSecondaryButtonProps
>(
  (
    {
      testID,
      label,
      onPress,
      buttonStyle,
      borderComponentStyle,
      labelStyle,
      iconConfig,
      borderColor,
      buttonContentStyle,
      buttonContainerStyle,
      buttonBackgroundStyle,
      disabled = false,
    },
    ref,
  ) => {
    const onPressButton = useCallback(() => {
      if (disabled) return;
      onPress?.();
    }, [onPress, disabled]);

    return (
      <View
        testID={testID}
        ref={ref}
        style={[
          { height: 40 },
          buttonContainerStyle,
          disabled && styles.disabledStyle,
        ]}
      >
        <View
          style={[
            styles.buttonBorderBackground,
            borderColor && { backgroundColor: borderColor },
            buttonBackgroundStyle,
            borderComponentStyle && borderComponentStyle,
            disabled && { opacity: 0 },
          ]}
        />
        <Pressable
          style={({ pressed }) => [
            styles.button,
            pressed && !disabled && styles.pressedButton,
            borderColor && { borderColor },
            buttonStyle,
          ]}
          onPress={onPressButton}
          impactFeedbackStyle={Haptics.ImpactFeedbackStyle.Soft}
          disabled={disabled}
        >
          <View style={[styles.buttonContent, buttonContentStyle]}>
            {iconConfig && <Icon size={18} color="white" {...iconConfig} />}
            {_size(label) > 0 && (
              <Text style={[styles.text, labelStyle]}>{label}</Text>
            )}
          </View>
        </Pressable>
      </View>
    );
  },
);

InteractiveSecondaryButton.displayName = 'InteractiveSecondaryButton';

export default React.memo(InteractiveSecondaryButton);
