import NavBar from '@/src/components/shared/Navbar';
import dark from '@/src/core/constants/themes/dark';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import React from 'react';
import {
  Dimensions,
  Image,
  SafeAreaView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import noFriendsPookie from '@/assets/images/pookie/no_friends_pookie.png';
import AntDesign from '@expo/vector-icons/AntDesign';
import { useRouter } from 'expo-router';
import styles from './styles';

const DefaultErrorView = () => {
  const { isMobile } = useMediaQuery();

  const router = useRouter();
  const routeToHome = () => {
    router.replace('/home');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View
        style={[
          styles.emptyStateContainer,
        ]}
      >
        <Image source={noFriendsPookie} style={styles.pookieImage} />

        <Text style={styles.title}>Oops! Something went wrong</Text>

        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryActionButton]}
            onPress={routeToHome}
          >
            <Text style={styles.secondaryActionButtonText}>Go Home</Text>
            <AntDesign name="right" size={12} color={dark.colors.secondary} />
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default React.memo(DefaultErrorView);
