import LinearGradient from 'atoms/LinearGradient';
import React from 'react';
import { View } from 'react-native';
import { useProgressDotsStyles } from './ProgressDots.style';

const ProgressDots = ({
  currentStep,
  totalSteps,
  dotSize,
  spacing,
  activeColor,
  inactiveColor,
}) => {
  const styles = useProgressDotsStyles({
    dotSize,
    spacing,
    activeColor,
    inactiveColor,
  });

  const renderDots = () => {
    const elements = [];

    // If currentStep > 1, render a pill for active dots
    if (currentStep > 1) {
      // Calculate pill width: (number of dots * dotSize) + (gaps between dots * spacing)
      const pillWidth = currentStep * dotSize + (currentStep - 1) * spacing;

      elements.push(
        <LinearGradient
          key="pill"
          colors={['#A9F99E', '#00D9FF']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={[
            styles.pill,
            {
              width: pillWidth,
            },
          ]}
        />,
      );

      // Add gap after pill if there are remaining dots
      if (currentStep < totalSteps) {
        elements.push(<View key="gap-after-pill" style={styles.dotGap} />);
      }
    } else {
      // For first step, render first dot as active
      elements.push(
        <LinearGradient
          key={1}
          colors={['#A9F99E', '#00D9FF']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={[styles.dot]}
        />,
      );

      // Add gap after first dot if there are more dots
      if (totalSteps > 1) {
        elements.push(<View key="gap-1" style={styles.dotGap} />);
      }
    }

    // Render remaining inactive dots
    for (let i = currentStep + 1; i <= totalSteps; i++) {
      elements.push(<View key={i} style={[styles.dot, styles.inactiveDot]} />);

      // Add gap between dots (except after the last dot)
      if (i < totalSteps) {
        elements.push(<View key={`gap-${i}`} style={styles.dotGap} />);
      }
    }

    return elements;
  };

  return <View style={styles.container}>{renderDots()}</View>;
};

export default React.memo(ProgressDots);
