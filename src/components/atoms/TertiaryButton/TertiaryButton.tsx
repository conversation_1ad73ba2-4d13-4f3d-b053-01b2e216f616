import { StyleProp, StyleSheet, TextStyle, ViewStyle } from 'react-native'; // Import ViewStyle
import React from 'react';
import { Button } from '@rneui/themed';
import dark from 'core/constants/themes/dark';

interface TertiaryButtonProps {
  title: string;
  label?: string;
  onPress: () => void;
  titleStyle?: StyleProp<TextStyle>;
  containerStyle?: StyleProp<ViewStyle>;
}

const styles = StyleSheet.create({
  titleStyle: {
    color: dark.colors.secondary,
    fontFamily: 'Montserrat-500',
  },
});

const TertiaryButton: React.FC<TertiaryButtonProps> = ({
  title,
  label,
  onPress,
  titleStyle,
  containerStyle,
  ...restProps
}) => (
  <Button
    type="clear"
    title={title ?? label}
    titleStyle={[styles.titleStyle, titleStyle]}
    onPress={onPress}
    containerStyle={containerStyle}
    {...restProps}
  />
);

export default TertiaryButton;
