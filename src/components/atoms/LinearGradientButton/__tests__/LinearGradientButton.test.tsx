import React from 'react';
import { fireEvent, render } from '@testing-library/react-native';
import LinearGradientButton from '../index';

describe('LinearGradientButton', () => {
  it('renders without crashing', () => {
    render(
      <LinearGradientButton
        label="Test Button"
        onPress={() => {}}
        buttonStyle={{}}
        labelStyle={{}}
      />,
    );
  });

  it('calls onPress when pressed', () => {
    const onPress = jest.fn();
    const { getByText } = render(
      <LinearGradientButton
        label="Test Button"
        onPress={onPress}
        buttonStyle={{}}
        labelStyle={{}}
      />,
    );
    const button = getByText('Test Button');
    fireEvent.press(button);
    expect(onPress).toHaveBeenCalled();
  });
});
