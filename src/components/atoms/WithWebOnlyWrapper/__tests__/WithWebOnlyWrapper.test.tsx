import React from 'react';
import { render } from '@testing-library/react-native';
import { Text } from 'react-native';
import WithWebOnlyWrapper from '../WithWebOnlyWrapper';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';

const mockedUseMediaQuery = useMediaQuery as jest.Mock;

beforeEach(() => {
  mockedUseMediaQuery.mockReturnValue({
    isMobile: false,
  })
})

describe('WithWebOnlyWrapper', () => {
  it('renders without crashing', () => {
    const MockComponent = () => <Text>Test Component</Text>;
    const WrappedComponent = WithWebOnlyWrapper(MockComponent);
    render(<WrappedComponent />);
  });
});
