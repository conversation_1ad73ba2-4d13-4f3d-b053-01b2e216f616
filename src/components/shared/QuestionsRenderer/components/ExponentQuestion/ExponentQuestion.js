import React from 'react';
import { Platform, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import KatexView from 'shared/KatexView';
import _isEmpty from 'lodash/isEmpty';

const ExponentQuestion = (props) => {
  const { question } = props;

  const { isMobile: isCompactMode } = useMediaQuery();

  if (_isEmpty(question)) {
    return null;
  }

  const { base, exponent } = question;
  const fontSize = Platform.OS !== 'web' ? 64 : isCompactMode ? 28 : 40;

  return (
    <View style={{ width: '100%' }}>
      <View
        style={{
          width: '100%',
          flexDirection: 'row',
          justifyContent: 'center',
        }}
      >
        <KatexView expression={`${base}^{${exponent}}`} fontSize={fontSize} />
      </View>
    </View>
  );
};

export default React.memo(ExponentQuestion);
