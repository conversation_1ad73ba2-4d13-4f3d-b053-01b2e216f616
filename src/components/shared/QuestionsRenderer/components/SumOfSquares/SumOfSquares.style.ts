import { StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import dark from 'core/constants/themes/dark';
import { useMemo } from 'react';

const createStyles = (isCompactMode : boolean) =>
  StyleSheet.create({
    tagContainer: {
      paddingHorizontal: 16,
      paddingVertical: 10,
      borderRadius: 20,
      backgroundColor: dark.colors.gradientBackground,
      flexShrink: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    tagText: {
      color: dark.colors.textDark,
      fontFamily: 'Montserrat-500',
      fontSize: 12,
      textAlign: 'center'
    },
    expressionContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    expressionText: {
      fontSize: isCompactMode ? 20 : 24,
      fontFamily: 'Montserrat-500',
      color: 'white',
    },
    primeText: {
      fontSize: isCompactMode ? 20 : 24,
      fontFamily: 'Montserrat-500',
      color: 'white',
      marginRight: 8,
    },
  });

const useStyles = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(() => createStyles(isMobile), [isMobile]);
  return styles;
};

export default useStyles;
