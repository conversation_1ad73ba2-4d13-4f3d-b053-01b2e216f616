import React, { useCallback } from 'react';
import { Text, View } from 'react-native';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import GoogleLoginButton from 'core/oauth/components/GoogleLoginButton';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';
import styles from './GoogleLoginButton.style';
import dark from '../../../../../core/constants/themes/dark';
import { withOpacity } from '../../../../../core/utils/colorUtils';

const GoogleLoginForGuest = () => {
  const { user } = useSession();

  const isGuestUser = userReader.isGuest(user);

  const renderButtonComponent = useCallback(
    ({ hovered }: { hovered: boolean }) => (
      <View
        style={[
          styles.buttonContainer,
          hovered && {
            backgroundColor: withOpacity(dark.colors.secondary, 0.05),
            transform: [{ scale: 1.05 }],
          },
        ]}
      >
        <FontAwesome6
          name="google"
          size={16}
          color={hovered ? dark.colors.secondary : 'white'}
        />
        <Text
          style={[
            styles.signInText,
            hovered && { color: dark.colors.secondary },
          ]}
        >
          Sign In
        </Text>
      </View>
    ),
    [],
  );

  if (!isGuestUser) {
    return null;
  }

  return <GoogleLoginButton renderButtonComponent={renderButtonComponent} />;
};

export default React.memo(GoogleLoginForGuest);
