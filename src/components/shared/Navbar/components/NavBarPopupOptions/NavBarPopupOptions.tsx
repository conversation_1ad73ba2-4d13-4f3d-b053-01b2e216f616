import { useRouter } from 'expo-router';
import React, { useCallback } from 'react';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import userReader from 'core/readers/userReader';
import { Pressable, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Text } from '@rneui/themed';
import styles from './NavBarPopupOptions.style';
import { useSession } from '../../../../../modules/auth/containers/AuthProvider';

interface OptionRowProps {
  iconConfig: Pick<React.ComponentProps<typeof Ionicons>, 'name'> &
    Partial<React.ComponentProps<typeof Ionicons>>;
  onPress: () => void;
  label: string;
}

const OptionRow: React.FC<OptionRowProps> = ({
  iconConfig,
  onPress,
  label,
}) => (
  <Pressable
    style={({ hovered }) => [
      styles.optionRowContainer,
      hovered && styles.hoveredOptionRowContainer,
    ]}
    onPress={onPress}
  >
    <View style={styles.optionRowContent}>
      <Ionicons size={16} color="white" {...iconConfig} />
      <Text style={styles.optionLabel}>{label}</Text>
    </View>
  </Pressable>
);

const NavBarPopupOptions: React.FC = () => {
  const { user, signOut } = useSession();

  const router = useRouter();

  const navigateToLogin = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.CLICK_ON_PAGE_HEADER_LOGOUT);
    signOut();
  }, [signOut]);

  const navigateToUserProfile = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.CLICK_ON_PAGE_HEADER_PROFILE);
    router.push(`/profile/${userReader.username(user)}`);
  }, [router, user]);

  const navigateToUserSettings = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.CLICK_ON_PAGE_HEADER_SETTINGS);
    router.push(`/settings`);
  }, [router]);

  return (
    <View style={styles.modalContent}>
      <OptionRow
        onPress={navigateToUserProfile}
        iconConfig={{ name: 'person' }}
        label="My Profile"
      />
      <OptionRow
        onPress={navigateToUserSettings}
        iconConfig={{ name: 'settings-sharp' }}
        label="Settings"
      />
      <OptionRow
        onPress={navigateToLogin}
        iconConfig={{ name: 'exit-outline' }}
        label="Logout"
      />
    </View>
  );
};

export default React.memo(NavBarPopupOptions);
