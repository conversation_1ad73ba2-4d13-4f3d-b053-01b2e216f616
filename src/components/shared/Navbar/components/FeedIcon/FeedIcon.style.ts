import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    padding: 0,
  },
  iconWrapper: {
    position: 'relative',
    width: 36,
    height: 36,
    borderRadius: 20,
    borderWidth: 1,
    backgroundColor: dark.colors.primary,
    borderColor: dark.colors.tertiary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badge: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: dark.colors.background,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: dark.colors.errorDark,
  },
});

export default styles;
