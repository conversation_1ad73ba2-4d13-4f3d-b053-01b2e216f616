import React from 'react';
import { Linking, Pressable, StyleSheet, View } from 'react-native';
import Icon, { ICON_TYPES } from 'atoms/Icon';
import _map from 'lodash/map';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';
import styles from './SocialChannelsFooter.style';

const SOCIAL_ICONS = [
  {
    iconConfig: {
      name: 'instagram',
      type: ICON_TYPES.FONT_AWESOME_5,
    },
    link: 'https://www.instagram.com/matiks.play',
  },
  {
    iconConfig: {
      name: 'twitter',
      type: ICON_TYPES.FONT_AWESOME_5,
    },
    link: 'https://x.com/matiks_play',
  },
  {
    iconConfig: {
      name: 'linkedin',
      type: ICON_TYPES.FONT_AWESOME_5,
    },
    link: 'https://www.linkedin.com/company/playonmatiks',
  },
  {
    iconConfig: {
      name: 'youtube',
      type: ICON_TYPES.FONT_AWESOME_5,
    },
    link: 'https://www.youtube.com/@matiks.official',
  },
];

const SocialFooter = () => {
  const handlePress = (url: string) => {
    Linking.openURL(url).catch((err) =>
      console.error('Error opening URL', err),
    );
  };

  return (
    <View style={styles.footerContainer}>
      {_map(SOCIAL_ICONS, ({ iconConfig, link }) => (
        <Pressable
          onPress={() => handlePress(link)}
          style={({ hovered }) => [
            {
              borderRadius: 20,
              height: 40,
              width: 40,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: hovered
                ? withOpacity(dark.colors.secondary, 0.1)
                : 'transparent',
            },
          ]}
        >
          {({ hovered }) => (
            <Icon
              {...iconConfig}
              size={hovered ? 20 : 18}
              color={hovered ? dark.colors.secondary : dark.colors.textDark}
            />
          )}
        </Pressable>
      ))}
    </View>
  );
};

export default SocialFooter;
