import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 10,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 120,
  },
  prevButton: {
    backgroundColor: dark.colors.background,
    borderColor: dark.colors.tertiary,
  },
  nextButton: {
    backgroundColor: dark.colors.background,
    borderColor: dark.colors.tertiary,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Montserrat-600',
  },
  disabledButton: {
    backgroundColor: dark.colors.background,
    borderColor: dark.colors.tertiary,
    opacity: 0.6,
  },
  disabledButtonText: {
    color: dark.colors.textDark,
  },
});

export default styles;
