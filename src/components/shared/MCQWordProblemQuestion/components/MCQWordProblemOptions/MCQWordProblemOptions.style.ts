import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    marginTop: 20,
  },
  optionBase: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 15,
    borderWidth: 1,
    marginBottom: 10,
    backgroundColor: dark.colors.background,
    borderColor: dark.colors.tertiary,
  },
  optionContainer: {},
  selectedOption: {
    backgroundColor: dark.colors.background,
    borderColor: dark.colors.tertiary,
  },
  correctOption: {
    backgroundColor: dark.colors.secondary,
    borderColor: dark.colors.secondary,
  },
  incorrectOption: {
    backgroundColor: dark.colors.defeatColor,
    borderColor: dark.colors.defeatColor,
  },
  optionText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Montserrat-500',
    marginLeft: 15,
  },
  selectedText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Montserrat-500',
    marginLeft: 15,
  },
  correctText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Montserrat-600',
    marginLeft: 15,
  },
  incorrectText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Montserrat-600',
    marginLeft: 15,
  },
  indicatorBase: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicator: {
    borderColor: dark.colors.tertiary,
  },
  selectedIndicator: {
    borderColor: 'white',
  },
  correctIndicator: {
    backgroundColor: 'white',
    borderColor: dark.colors.secondary,
  },
  incorrectIndicator: {
    backgroundColor: 'white',
    borderColor: dark.colors.defeatColor,
  },
  indicatorInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'white',
  },
  indicatorText: {
    color: dark.colors.primary,
    fontSize: 14,
    fontFamily: 'Montserrat-600',
  },
});

export default styles;
