import { withOpacity } from '@/src/core/utils/colorUtils';
import { StyleSheet } from 'react-native';
import Dark from '@/src/core/constants/themes/dark';

export const BORDER_RADIUS = 10;

const styles = StyleSheet.create({
  gradientContainer: {
    flex: 1,
    borderRadius: BORDER_RADIUS,
  },
  darkBackgroundContainer: {
    padding: 1,
    flex: 1,
    borderRadius: BORDER_RADIUS,
    backgroundColor: withOpacity(Dark.colors.gradientBackground, 0.5),
  },
  gradientContentContainer: {
    flex: 1,
    borderRadius: BORDER_RADIUS,
  },
  container: {
    flex: 1,
    borderRadius: BORDER_RADIUS,
    backgroundColor: withOpacity(Dark.colors.gradientBackground, 0.95),
  },
});

export default styles;
