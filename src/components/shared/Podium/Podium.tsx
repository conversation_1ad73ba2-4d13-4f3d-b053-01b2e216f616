import React from 'react';
import { View, Image } from 'react-native';
import podium from '@/assets/images/podium.png';
import PodiumUserCard from './PodiumUserCard';
import _isEmpty from 'lodash/isEmpty';
import styles from './Podium.style';

const Podium = ({ podiumUsers = EMPTY_ARRAY }) => {
  const [user1, user2, user3] = podiumUsers;

  if (_isEmpty(podiumUsers)) {
    return;
  }

  return (
    <View style={styles.container}>
      <View style={styles.podium}>
        <Image source={podium} style={styles.image} />

        {user1 && (
          <PodiumUserCard user={user1} style={styles.user1} position={1} />
        )}
        {user2 && <PodiumUserCard user={user2} style={styles.user2} />}
        {user3 && <PodiumUserCard user={user3} style={styles.user3} />}
      </View>
    </View>
  );
};

export default React.memo(Podium);
