import Dark from "@/src/core/constants/themes/dark";
import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
  cardContainer: {
    position: 'absolute',
    alignItems: 'center',
    width: 116,
  },
  userName: {
    marginTop: 2,
    fontSize: 14,
    fontFamily: 'Montserrat-500',
    color: Dark.colors.textDark,
  },
  topRating: {
    marginTop: 2,
    fontSize: 14,
    color: Dark.colors.secondary,
  },
  timeContainer: {
    height: 24,
    width: 64,
    backgroundColor: Dark.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    marginTop: 4,
  },
  userScore: {
    fontSize: 12,
    color: 'white',
    fontFamily: 'Montserrat-500',
    marginBottom: 2,
  },
  userImage: {
    height: 40,
    width: 40,
    borderRadius: 5,
    overflow: 'hidden',
  },
});

export default styles