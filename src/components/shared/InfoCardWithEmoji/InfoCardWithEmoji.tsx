import { View } from 'react-native';
import { Text } from 'react-native';
import styles from './InfoCardWithEmoji.style';
import React from 'react';
import { InfoCardWithEmojiProps } from './types';

const InfoCardWithEmoji: React.FC<InfoCardWithEmojiProps> = (props) => {
  const { iconEmoji, title, description } = props;

  return (
    <View style={styles.card}>
      <Text style={styles.cardIcon}>{iconEmoji}</Text>
      <View style={styles.cardText}>
        <Text style={styles.cardTitle}>{title}</Text>
        <Text style={styles.cardDescription} numberOfLines={2}>
          {description}
        </Text>
      </View>
    </View>
  );
};

export default React.memo(InfoCardWithEmoji);
