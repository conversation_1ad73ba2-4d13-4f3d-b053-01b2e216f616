import { StyleSheet } from 'react-native';
import dark from '../../../core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    width: '100%',
    height: 50,
    paddingHorizontal: 16,
    paddingVertical: 8,
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: dark.colors.secondary,
  },
  infoContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  infoText: {
    fontSize: 14,
    color: 'black',
    fontFamily: 'Montserrat-500',
  },
  button: {
    height: 28,
    borderRadius: 18,
    paddingHorizontal: 16,
    paddingVertical: 0,
    backgroundColor: '#7eca74',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonLabel: {
    color: 'black',
    fontFamily: 'Montserrat-500',
    fontSize: 14,
  },
});

export default styles;
