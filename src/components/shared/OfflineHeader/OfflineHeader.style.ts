import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const style = StyleSheet.create({
  container: {
    // height: 40,
    paddingVertical: 16,
    backgroundColor: dark.colors.gradientBackground,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    maxWidth: '100%',
  },
  buttonContainer: {
    height: 24,
  },
  buttonInnerContainer: {
    backgroundColor: dark.colors.secondary,
    paddingHorizontal: 12,
    height: 24,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: dark.colors.tertiary,
    fontSize: 12,
    lineHeight: 15,
    fontFamily: 'Montserrat-600',
  },
  title: {
    color: dark.colors.textLight,
    flex: 1,
    fontFamily: 'Montserrat-500',
    paddingRight: 8,
    fontSize: 13,
    lineHeight: 16,
  },
});

export default style;
