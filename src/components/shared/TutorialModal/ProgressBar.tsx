import React, { useEffect, useState } from 'react';
import { Animated, View } from 'react-native';
import styles from './ProgressBar.style';
import { ProgressBarProps } from './types';

const ProgressBar: React.FC<ProgressBarProps> = ({ duration, startTime }) => {
  const [fillAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    const timeout = setTimeout(() => {
      Animated.timing(fillAnimation, {
        toValue: 1,
        duration,
        useNativeDriver: false,
      }).start();
    }, startTime);

    return () => clearTimeout(timeout);
  }, [fillAnimation, duration, startTime]);

  const widthInterpolation = fillAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 80],
  });

  return (
    <View style={styles.progressBarContainer}>
      <Animated.View
        style={[styles.progressBar, { width: widthInterpolation }]}
      />
    </View>
  );
};

export default ProgressBar;
