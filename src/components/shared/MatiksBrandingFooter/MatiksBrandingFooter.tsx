import React from 'react';
import { View, Image } from 'react-native';
import { Text } from '@rneui/themed';
import matiksLogo from 'assets/images/LinearGradientIcons/matiksBolt.png';
import styles from './MatiksBrandingFooter.style';

const MatiksBrandingFooter = () => {
  return (
    <View style={styles.brandingContainer}>
      <View style={styles.matikLogoContainer}>
        <Text style={styles.matiksLogoLabel}>MATIK</Text>
        <Image source={matiksLogo} style={styles.matikLogo} />
      </View>
      <Text style={styles.brandDescription}>GAMIFYING MENTAL APTITUDE</Text>
      <View style={styles.brandingLinkContainer}>
        <Text style={styles.brandDescriptionLink}>www.matiks.in</Text>
      </View>
    </View>
  );
};

export default React.memo(MatiksBrandingFooter);
