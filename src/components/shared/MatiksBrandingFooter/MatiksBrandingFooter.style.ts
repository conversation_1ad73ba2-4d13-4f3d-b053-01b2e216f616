import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  brandDescription: {
    color: 'white',
    fontSize: 14,
  },
  brandingContainer: {
    gap: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  brandingLinkContainer: {
    flexDirection: 'row',
    gap: 4,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  brandDescriptionLink: {
    color: 'white',
    fontSize: 12,
  },

  matikLogoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  matikLogo: {
    width: 24,
    height: 24,
  },
  matiksLogoLabel: {
    fontSize: 32,
    fontFamily: 'Montserrat-800',
    color: 'white',
  },
});

export default styles;