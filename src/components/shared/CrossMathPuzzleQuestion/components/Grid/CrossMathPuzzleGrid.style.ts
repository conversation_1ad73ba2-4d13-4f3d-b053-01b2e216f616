import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  gridContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  row: {
    flexDirection: 'row',
  },

  // cell
  cellContainer: {
    // flex: 1,
    width: 44,
    height: 44,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    margin: 0,
    borderColor: dark.colors.background,
  },
  selectedContainer: {
    borderColor: dark.colors.puzzle.secondary,
  },
  borderView: {
    width: 40,
    height: 40,
    backgroundColor: 'transparent',
    padding: 2,
    borderRadius: 4,
  },
  selectedBorderView: {
    backgroundColor: dark.colors.puzzle.secondary,
  },
  text: {
    fontSize: 16,
    color: dark.colors.background,
    fontFamily: 'Montserrat-700',
  },
});

export default styles;
