import React, { useCallback } from 'react';
import { View } from 'react-native';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from 'atoms/Icon';
import { CROSS_MATH_PUZZLE_ACTIONS } from 'shared/CrossMathPuzzleQuestion/constants/puzzleConstants';
import dark from 'core/constants/themes/dark';
import { useCrossMathPuzzleQuestion } from 'shared/CrossMathPuzzleQuestion/context';

const ACTION_CONFIGS = {
  UNDO: {
    iconConfig: {
      name: 'undo-variant',
      type: ICON_TYPES.MATERIAL_COMMUNITY_ICONS,
      color: 'white',
      size: 16,
    },
  },
  REDO: {
    iconConfig: {
      name: 'redo-variant',
      type: ICON_TYPES.MATERIAL_COMMUNITY_ICONS,
      color: 'white',
      size: 16,
    },
  },
  HINT: {
    iconConfig: {
      name: 'lightbulb',
      type: ICON_TYPES.MATERIAL_ICONS,
      color: 'white',
      size: 16,
    },
    label: 'HINT',
  },
  CLEAR: {
    iconConfig: {
      name: 'playlist-remove',
      type: ICON_TYPES.MATERIAL_ICONS,
      color: 'white',
      size: 16,
    },
    label: 'CLEAR',
  },
};

const CrossMathPuzzleActions = () => {
  const { state, onAction } = useCrossMathPuzzleQuestion();

  const { isIncorrectSolution } = state;

  const undo = useCallback(() => {
    onAction({ type: CROSS_MATH_PUZZLE_ACTIONS.UNDO });
  }, [onAction]);

  const redo = useCallback(() => {
    onAction({ type: CROSS_MATH_PUZZLE_ACTIONS.REDO });
  }, [onAction]);

  const clear = useCallback(() => {
    onAction({ type: CROSS_MATH_PUZZLE_ACTIONS.CLEAR });
  }, [onAction]);

  const borderStyle = isIncorrectSolution
    ? {
        borderColor: dark.colors.secondary,
      }
    : EMPTY_OBJECT;

  return (
    <View
      style={{
        height: 42,
        flexDirection: 'row',
        gap: 12,
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <InteractiveSecondaryButton
        onPress={undo}
        iconConfig={ACTION_CONFIGS.UNDO.iconConfig}
        buttonContainerStyle={{ width: 40, height: 40 }}
      />
      <InteractiveSecondaryButton
        {...borderStyle}
        label={ACTION_CONFIGS.CLEAR.label}
        labelStyle={{ fontSize: 12 }}
        onPress={clear}
        iconConfig={ACTION_CONFIGS.CLEAR.iconConfig}
        buttonContainerStyle={{ width: 120, height: 40 }}
      />
      <InteractiveSecondaryButton
        onPress={redo}
        iconConfig={ACTION_CONFIGS.REDO.iconConfig}
        buttonContainerStyle={{ width: 40, height: 40 }}
      />
    </View>
  );
};

export default React.memo(CrossMathPuzzleActions);
