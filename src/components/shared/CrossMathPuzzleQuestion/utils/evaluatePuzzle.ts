import _toNumber from 'lodash/toNumber';
import { cellType } from '../types/crossMathCellType';

export function evaluateExpression(arr) {
  // First handle multiplication and division (BODMAS Rule)
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] === '×' || arr[i] === '÷') {
      const num1 = _toNumber(arr[i - 1]);
      const operator = arr[i];
      const num2 = _toNumber(arr[i + 1]);

      let result;
      if (operator === '×') {
        result = num1 * num2;
      } else if (operator === '÷') {
        result = num1 / num2;
      }

      // Replace the operator and its operands with the result
      arr.splice(i - 1, 3, result);

      // Adjust the loop index since we modified the array
      i -= 2;
    }
  }

  // Now handle addition and subtraction
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] === '+' || arr[i] === '-') {
      const num1 = _toNumber(arr[i - 1]);
      const operator = arr[i];
      const num2 = _toNumber(arr[i + 1]);

      let result;
      if (operator === '+') {
        result = num1 + num2;
      } else if (operator === '-') {
        result = num1 - num2;
      }

      // Replace the operator and its operands with the result
      arr.splice(i - 1, 3, result);

      // Adjust the loop index since we modified the array
      i -= 2;
    }
  }
  // The final result should be the only value left in the array
  return arr[0];
}

export function evaluateCrossMathPuzzle(
  gridItems: cellType[],
  gridDimension: { rows: number; columns: number },
): boolean {
  const grid: cellType[][] = Array.from({ length: gridDimension.rows }, () =>
    Array.from({ length: gridDimension.columns }, () => ({}) as cellType),
  );

  gridItems.forEach((item: cellType, index) => {
    const row = Math.floor(index / gridDimension.columns);
    const column = index % gridDimension.columns;
    grid[row][column] = item;
  });

  let actualRows = 0;
  let actualColumns = 0;

  for (let i = 0; i < gridDimension.rows; i++) {
    for (let j = 0; j < gridDimension.columns; j++) {
      if (grid[i][j].type && grid[i][j].type !== 'EmptyBlock') {
        actualRows = Math.max(actualRows, i + 1);
        actualColumns = Math.max(actualColumns, j + 1);
      }
    }
  }

  const GRID_SIZE = Math.max(Math.min(actualRows, actualColumns), 3);

  for (let i = 0; i < GRID_SIZE; i++) {
    for (let j = 0; j < GRID_SIZE; j++) {
      if (grid[i][j].editable && grid[i][j]?.value === '') {
        return false;
      }
    }
  }

  let isValid: boolean = true;

  for (let row = 0; row < GRID_SIZE - 2; row += 2) {
    const arr = [];
    for (let col = 0; col < GRID_SIZE - 2; col += 1) {
      if (grid[row][col].type !== 'EmptyBlock') {
        arr.push(grid[row][col].value);
      }
    }
    if (arr.length > 0 && grid[row][GRID_SIZE - 1].type !== 'EmptyBlock') {
      const evaluatedExpression = evaluateExpression(arr);
      isValid =
        isValid &&
        _toNumber(grid[row][GRID_SIZE - 1].value) === evaluatedExpression;
    }
  }

  for (let col = 0; col < GRID_SIZE - 2; col += 2) {
    const arr = [];
    for (let row = 0; row < GRID_SIZE - 2; row += 1) {
      if (grid[row][col].type !== 'EmptyBlock') {
        arr.push(grid[row][col].value);
      }
    }
    if (arr.length > 0 && grid[GRID_SIZE - 1][col].type !== 'EmptyBlock') {
      const evaluatedExpression = evaluateExpression(arr);
      isValid =
        isValid &&
        _toNumber(grid[GRID_SIZE - 1][col].value) === evaluatedExpression;
    }
  }

  return isValid;
}
