export interface AppVersion {
  id: string;
  platform: 'iOS' | 'Android';
  version: string;
  features: string[];
  isBlocking: boolean;
  updateType: 'patch' | 'minor' | 'major';
  createdAt: string;
  updatedAt: string;
}

export interface AppUpdateModalProps {
  visible: boolean;
  onClose: () => void;
  onUpdate: () => void;
  currentVersion: string;
  latestVersion: AppVersion;
  canSkip: boolean;
}
