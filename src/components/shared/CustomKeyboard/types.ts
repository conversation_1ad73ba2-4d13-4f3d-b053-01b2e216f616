import { KEYBOARD_TYPES } from './constants';

export type KeyboardType = (typeof KEYBOARD_TYPES)[keyof typeof KEYBOARD_TYPES];
export const UserSettingsKeyboardType = {
  TELEPHONE: 'TELEPHONE',
  CALCULATOR: 'CALCULATOR',
} as const;

export type UserSettingsKeyboardType =
  (typeof UserSettingsKeyboardType)[keyof typeof UserSettingsKeyboardType];

export interface CustomKeyboardProps {
  onKeyPress: (key: string) => void;
  onDelete: () => void;
  customKeyboardType?: KeyboardType;
  keyboardType?: UserSettingsKeyboardType;
}
