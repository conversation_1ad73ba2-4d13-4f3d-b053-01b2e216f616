import React, { useCallback, useEffect, useState } from 'react';
import { View } from 'react-native';
import RangeSlider from 'react-native-range-slider-expo';
import dark from '../../../core/constants/themes/dark';

interface RangeSelectorProps {
  minValue?: number;
  maxValue?: number;
  step?: number;
  initialFromValue?: number;
  initialToValue?: number;
  onValueChange: (value: { from: number; to: number }) => void;
  primaryColor?: string;
}

const RangeSelector: React.FC<RangeSelectorProps> = ({
  minValue = 500,
  maxValue = 3000,
  step = 10,
  initialFromValue = 1000,
  initialToValue = 1500,
  onValueChange,
  primaryColor = dark.colors.secondary,
}) => {
  const [fromValue, setFromValue] = useState(initialFromValue);
  const [toValue, setToValue] = useState(initialToValue);
  const [fromValueOnChange, setFromValueOnChange] = useState(initialFromValue);
  const [toValueOnChange, setToValueOnChange] = useState(initialToValue);

  useEffect(() => {
    onValueChange({ from: fromValueOnChange, to: toValueOnChange });
  }, [fromValueOnChange, toValueOnChange]);

  const handleOnValueChange = useCallback(
    (from: number, to: number) => {
      setFromValue(from);
      setToValue(to);
      onValueChange({ from, to });
    },
    [onValueChange],
  );

  return (
    <View style={{ width: '100%', height: 30 }}>
      <RangeSlider
        min={minValue}
        max={maxValue}
        step={step}
        // fromValue={fromValue}
        // toValue={toValue}
        initialFromValue={initialFromValue}
        initialToValue={initialToValue}
        fromValueOnChange={setFromValueOnChange}
        toValueOnChange={setToValueOnChange}
        // onValueChange={handleOnValueChange}
        styleSize="small"
        showRangeLabels={false}
        showValueLabels={false}
        rangeLabelsTextColor={dark.colors.textDark}
        barHeight={6}
        fromKnobColor={dark.colors.textDark}
        toKnobColor={dark.colors.textDark}
        knobSize={15}
        inRangeBarColor={primaryColor}
        outOfRangeBarColor={dark.colors.tertiary}
        // thumbColor={primaryColor}
        // thumbBorderColor={primaryColor}
        containerStyle={{
          paddingVertical: 0,
          marginBottom: 0,
          height: 30,
          width: '100%',
          paddingHorizontal: 0,
        }}
      />
    </View>
  );
};

export default React.memo(RangeSelector);
