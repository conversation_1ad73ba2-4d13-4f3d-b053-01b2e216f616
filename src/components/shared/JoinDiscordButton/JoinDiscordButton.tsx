import { Text, Linking } from 'react-native';
import React, { useCallback } from 'react';
import { Button } from '@rneui/themed';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Dark from 'core/constants/themes/dark';
import { DISCORD_COMMUNITY_URL } from 'core/constants/appConstants';
import styles from './JoinDiscordButton.style';

const JoinDiscordButton = (/*{ force = false } = EMPTY_OBJECT*/) => {
  // const router = useRouter()

  const onPress = useCallback(() => {
    Linking.openURL(DISCORD_COMMUNITY_URL);
  }, []);

  return (
    <Button
      testID="discord-button"
      onPress={onPress}
      radius={'sm'}
      type="solid"
      buttonStyle={styles.button}
    >
      <MaterialIcons
        testID="icon-discord"
        name="discord"
        size={18}
        color={Dark.colors.textDark}
      />
      <Text style={styles.text}>Join Discord Community</Text>
    </Button>
  );
};

export default React.memo(JoinDiscordButton);
