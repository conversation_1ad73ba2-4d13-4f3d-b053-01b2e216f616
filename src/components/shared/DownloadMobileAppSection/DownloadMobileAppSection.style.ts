import { StyleSheet } from 'react-native';
import Dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  text: {
    fontSize: 10,
    color: Dark.colors.textLight,
    opacity: 0.4,
    fontFamily: 'Montserrat-600',
    lineHeight: 12,
    marginBottom: 0,
    letterSpacing: 0.8,
  },
  innerContainer: {
    gap: 12,
    flexDirection: 'row',
  },
  container: {
    padding: 12,
    width: '100%',
    borderRadius: 12,
    gap: 12,
    borderWidth: 1,
    borderColor: Dark.colors.tertiary,
  },
});

export default styles;
