import React from 'react';
import { Text, View } from 'react-native';
import LinearGradient from '../../atoms/LinearGradient';
import dark from '@/src/core/constants/themes/dark';
import styles from './TextWithDivider.style';
import { TextWithDividerProps } from './types';

const TextWithDivider: React.FC<TextWithDividerProps> = ({
  label,
  labelStyles,
  containerStyles,
}) => {
  return (
    <View style={[styles.container, containerStyles]}>
      <LinearGradient
        colors={[dark.colors.primary, dark.colors.textDark]}
        start={{ x: 0, y: 1 }}
        style={[styles.line, { height: 1 }]}
      />
      <Text style={[styles.text, labelStyles]}>{label}</Text>
      <LinearGradient
        colors={[dark.colors.textDark, dark.colors.primary]}
        start={{ x: 0, y: 1 }}
        style={[styles.line, { height: 1 }]}
      />
    </View>
  );
};

export default React.memo(TextWithDivider);
