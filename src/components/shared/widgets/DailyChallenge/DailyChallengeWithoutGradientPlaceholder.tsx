import React from 'react';
import { View } from 'react-native';
import ShimmerView from '../../../molecules/ShimmerView';
import styles from './DailyChallenge.style';
import dark from '../../../../core/constants/themes/dark';

const DailyChallengeWithoutGradientPlaceholder = () => {
  return (
    <View style={{ width: '100%', marginTop: 20 }}>
      <View
        style={{
          borderColor: dark.colors.tertiary,
          borderWidth: 2,
          borderRadius: 10,
        }}
      >
        <View style={styles.placeholderContainer}>
          <View
            style={{ flexDirection: 'row', justifyContent: 'space-between' }}
          >
            <ShimmerView
              style={{ width: 100, height: 18, borderRadius: 8 }}
              shimmerColors={dark.colors.placeholderShimmerColors}
            />
            <ShimmerView
              style={{ width: 30, height: 18, borderRadius: 3 }}
              shimmerColors={dark.colors.placeholderShimmerColors}
            />
          </View>

          <ShimmerView
            style={{ width: 240, height: 12, borderRadius: 8, marginTop: 8 }}
            shimmerColors={dark.colors.placeholderShimmerColors}
          />
          <ShimmerView
            style={{ width: 140, height: 12, borderRadius: 8, marginTop: 4 }}
            shimmerColors={dark.colors.placeholderShimmerColors}
          />
        </View>
      </View>
    </View>
  );
};

export default React.memo(DailyChallengeWithoutGradientPlaceholder);
