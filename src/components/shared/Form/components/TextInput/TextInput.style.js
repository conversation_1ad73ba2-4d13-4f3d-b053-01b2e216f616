import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  mainContainer: {
    gap: 6,
  },
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  label: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    color: '#FFFFFF',
  },
  inputContainer: {
    borderRadius: 6,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: 12,
    color: 'white',
    borderRadius: 12,
    fontFamily: 'Montserrat-600',
    outlineStyle: 'none',
    borderColor: 'white',
    textAlign: 'center',
    minWidth: 35,
    width: 30,
  },
  labelAndIconRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: dark.colors.error,
  },
  textFormField: {
    borderWidth: 1,
    width: '100%',
    borderColor: dark.colors.tertiary,
    borderRadius: 10,
    outlineStyle: 'none',
    // maxWidth:400,
    fontFamily: 'Montserrat-400',
    textAlign: 'left',
    padding: 12,
    fontSize: 14,
    color: '#fff',
    backgroundColor: '#333',
  },
  focusedInputField: {
    borderWidth: 1,
    outlineColor: dark.colors.secondary,
    outlineStyle: 'none',
    borderColor: dark.colors.secondary,
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
    textAlign: 'left',
    color: '#fff',
    backgroundColor: 'transparent',
  },
});

export default styles;
