export interface RangeSelectorProps {
  field: {
    type: string;
    label?: string;
    rules: {
      minValue: number;
      maxValue: number;
    };
    defaultValue: {
      min: number;
      max: number;
    };
    additional?: any;
    key: string;
    required?: boolean;
    visible?: boolean | (() => boolean);
  };
  value: {
    min: number;
    max: number;
  };
  error: string;
  onChange: (value: { min: number; max: number }) => void;
}

export enum InputType {
  MIN = 'min',
  MAX = 'max',
}

export interface NumInputProps {
  type: InputType;
  inputText: string;
  inputLabel: string;
  handleInputChange: (text: string) => void;
  handleInputBlur: () => void;
  handleIncrease: (type: InputType.MIN | InputType.MAX) => void;
  handleDecrease: (type: InputType.MIN | InputType.MAX) => void;
}
