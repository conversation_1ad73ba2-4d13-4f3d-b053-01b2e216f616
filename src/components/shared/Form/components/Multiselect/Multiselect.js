import React from 'react';
import { View, Text, TouchableOpacity, FlatList } from 'react-native';
import PropTypes from 'prop-types';
import { FORM_INPUT_TYPES } from 'core/constants/forms';
import _values from 'lodash/values';
import styles from './MultiSelect.style';
import FormFieldLabel from '../FormFieldLabel';
import FormFieldError from '../FormFieldError';

const Multiselect = (props) => {
  const { field, value, error, onValueChange } = props;
  const { type, options } = field;

  if (type !== FORM_INPUT_TYPES.MULTISELECT) {
    return null;
  }

  const toggleOption = (option) => {
    const newValue = value.includes(option)
      ? value.filter((item) => item !== option)
      : [...value, option];
    onValueChange(newValue);
  };

  const renderItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.option, value.includes(item) && styles.selectedOption]}
      onPress={() => toggleOption(item)}
    >
      <Text style={styles.optionText}>{item}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.mainContainer}>
      <FormFieldLabel field={field} />
      <FlatList
        data={options}
        renderItem={renderItem}
        keyExtractor={(item) => item}
        extraData={value}
      />
      <FormFieldError error={error} />
    </View>
  );
};

Multiselect.propTypes = {
  field: PropTypes.shape({
    type: PropTypes.oneOf(_values(FORM_INPUT_TYPES)),
    label: PropTypes.string,
    options: PropTypes.arrayOf(PropTypes.string).isRequired,
  }).isRequired,
  value: PropTypes.arrayOf(PropTypes.string).isRequired,
  onValueChange: PropTypes.func.isRequired,
  error: PropTypes.string,
};

export default React.memo(Multiselect);
