import React, { useState } from 'react';
import { TextInput as RNTextInput, View } from 'react-native';
import { TextInputProps } from 'shared/Form/types/formTypes';
import dark from '@/src/core/constants/themes/dark';
import styles from '../TextInput/TextInput.style';
import FormFieldLabel from '../FormFieldLabel';
import FormFieldError from '../FormFieldError';

interface RangeTextInputProps {
  field: TextInputProps;
  minKeyValue: string;
  maxKeyValue: string;
  error: string;
  onMinValueChange: (text: string) => void;
  onMaxValueChange: (text: string) => void;
}

const RangeTextInput = (props: RangeTextInputProps) => {
  const {
    field,
    onMinValueChange,
    minKeyValue,
    maxKeyValue,
    error,
    onMaxValueChange,
  } = props ?? EMPTY_OBJECT;

  const {
    label,
    type,
    innerContainerStyle,
    mainContainerStyle,
    style,
    placeholder,
  } = field ?? EMPTY_OBJECT;

  const [isMinFieldFocused, setIsMinFieldFocused] = useState(false);
  const [isMaxFieldFocused, setIsMaxFieldFocused] = useState(false);

  const onMinFieldFocus = (e: any) => {
    setIsMinFieldFocused(true);
  };

  const onMinFieldBlur = (e: any) => {
    setIsMinFieldFocused(false);
  };

  const onMaxFieldFocus = (e: any) => {
    setIsMaxFieldFocused(true);
  };

  const onMaxFieldBlur = (e: any) => {
    setIsMaxFieldFocused(false);
  };

  return (
    <View style={[styles.mainContainer, mainContainerStyle]}>
      <FormFieldLabel field={field} />
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          width: '100%',
          gap: 12,
        }}
      >
        <RNTextInput
          style={[
            styles.textFormField,
            { flex: 1 },
            isMinFieldFocused && styles.focusedInputField,
          ]}
          value={minKeyValue}
          maxLength={50}
          keyboardType="numeric"
          onFocus={onMinFieldFocus}
          onBlur={onMinFieldBlur}
          placeholder={`Min ${placeholder}`}
          placeholderTextColor={dark.colors.textDark}
          onChangeText={onMinValueChange}
        />
        <RNTextInput
          style={[
            styles.textFormField,
            { flex: 1 },
            isMaxFieldFocused && styles.focusedInputField,
          ]}
          value={maxKeyValue}
          maxLength={50}
          placeholder={`Max ${placeholder}`}
          placeholderTextColor={dark.colors.textDark}
          keyboardType="numeric"
          onFocus={onMaxFieldFocus}
          onBlur={onMaxFieldBlur}
          onChangeText={onMaxValueChange}
        />
      </View>
      <FormFieldError error={error} />
    </View>
  );
};

export default React.memo(RangeTextInput);
