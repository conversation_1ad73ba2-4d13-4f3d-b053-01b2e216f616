import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  labelStyle: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Montserrat-400',
  },
  descStyle: {
    color: dark.colors.textDark,
    fontSize: 12,
    fontFamily: 'Montserrat-400',
  },
  unfilledOptionStyle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: dark.colors.secondary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  filledCircleStyle: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: dark.colors.secondary,
  },
  innerContainerStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 10,
  },
  mainContainerStyle: {
    gap: 6,
  },
});

export default styles;
