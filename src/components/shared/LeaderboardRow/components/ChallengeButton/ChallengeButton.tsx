import Pressable from '@/src/components/atoms/Pressable';
import React from 'react';
import { Text } from 'react-native';
import styles from './ChallengeButton.style';
import FistSVG from '@/src/components/svg/FistSVG';
import { ChallengeButtonProps } from '../../types';

const ChallengeButton: React.FC<ChallengeButtonProps> = (props) => {
  const { onPress, label, renderLeadingComponent } = props;
  return (
    <Pressable style={styles.sendChallenge} onPress={onPress}>
      {renderLeadingComponent ? renderLeadingComponent() : <FistSVG />}
      <Text style={styles.sendChallengeText}>{label}</Text>
    </Pressable>
  );
};

export default React.memo(ChallengeButton);
