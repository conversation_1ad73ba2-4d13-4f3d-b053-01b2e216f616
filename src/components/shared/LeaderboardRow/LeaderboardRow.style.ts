import { StyleSheet } from 'react-native';
import Dark from 'core/constants/themes/dark';
import APP_LAYOUT_CONSTANTS from 'core/constants/appLayout';

const styles = StyleSheet.create({
  container: {
    width: '100%',
    maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
    flex: 1,
    maxHeight: 'auto',
    justifyContent: 'flex-start',
  },
  rankCircle: {
    width: 28,
    height: 28,
    borderRadius: 14,
    borderColor: Dark.colors.leaderboardImageBorder,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  currUserTimeBg: {
    backgroundColor: Dark.colors.GradientCardGreen,
    padding: 8,
    borderRadius: 49,
    gap: 10,
  },
  firstPlace: {
    backgroundColor: Dark.colors.puzzle.primary,
    padding: 8,
    borderRadius: 49,
    gap: 10,
  },
  firstPlaceLabel: {
    color: 'black',
  },
  currUserFirstBg: {
    backgroundColor: Dark.colors.tertiary,
  },
  currUserBg: {
    backgroundColor: Dark.colors.tertiary,
  },
  rankCircleText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Montserrat-600',
  },
  mainText: {
    color: Dark.colors.textDark,
    fontSize: 15,
    marginLeft: 20,
  },
  flatListContainerStyle: {
    width: '100%',
  },
  flatListContentContainerStyle: {
    paddingHorizontal: 24,
    width: '100%',
  },
  userListStyle: {
    paddingHorizontal: 24,
  },
  heading: {
    fontSize: 24,
    fontFamily: 'Montserrat-700',
    marginBottom: 16,
  },
  item: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  loadingIndicator: {
    marginTop: 50,
  },
  textContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  text: {
    color: 'white',
  },

  // header
  headerContainer: {
    marginTop: -8,
    paddingVertical: 12,
  },
  headerLabelStyle: {
    color: Dark.colors.textDark,
    fontSize: 12,
  },

  // columns
  rowContainer: {
    flexDirection: 'row',
    height: 80,
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 14,
    gap: 6,
  },
  userScoreRowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 14,
    width: '100%',
    borderWidth: 1.3,
    borderRadius: 10,
    borderColor: 'grey',
    backgroundColor: Dark.colors.tertiary,
    gap: 6,
  },
  compactRowContainer: {
    paddingVertical: 18,
    // paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  rankColumn: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    maxWidth: 36,
  },
  rankLabel: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Montserrat-600',
  },
  rowLabel: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Montserrat-600',
  },
  rowLabelScore: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Montserrat-700',
  },
  rating: {
    color: 'white',
    opacity: 0.4,
    fontFamily: 'Montserrat-600',
    fontSize: 12,
  },
  profileInfoColumn: {
    flex: 1,
    flexDirection: 'row',
    gap: 15,
    alignItems: 'center',
  },
  profileImageContainer: {
    width: 55,
    height: 55,
    borderRadius: 30,
    borderWidth: 1,
    borderColor: Dark.colors.leaderboardImageBorder,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeLabelContainer: {
    position: 'absolute',
    overflow: 'visible',
    borderWidth: 1,
    borderColor: Dark.colors.leaderboardImageBorder,
    borderRadius: 20,
    paddingHorizontal: 7,
    paddingVertical: 3,
    backgroundColor: Dark.colors.background,
    left: 13,
    bottom: -8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeLabel: {
    color: 'white',
    fontSize: 10,
    fontFamily: 'Montserrat-800',
    textAlign: 'center',
  },
  scoreColumn: {
    width: 100,
    flexShrink: 1,
    alignItems: 'center',
  },
  timeTakenColumn: {
    flexShrink: 2,
    alignItems: 'center',
  },
  correctSubmissionColumn: {
    flexShrink: 1,
    width: 200,
    alignItems: 'center',
  },
  containerWithInfo: {
    flex: 1,
    gap: 10,
    flexDirection: 'column',
    width: '100%',
    maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
  },
  separator: {
    width: '100%',
    height: 1,
    backgroundColor: Dark.colors.tertiary,
    marginVertical: 4,
  },

  // empty leaderboard
  emptyLeaderboardContainer: {
    alignItems: 'center',
    padding: 24,
    gap: 16,
  },
  emptyLeaderboardLabel: {
    color: Dark.colors.textDark,
    fontSize: 14,
    textAlign: 'center',
  },
  playNowButton: {
    width: 100,
    borderRadius: 16,
    backgroundColor: Dark.colors.tertiary,
  },
  playNowLabel: {
    fontSize: 12,
    color: 'white',
  },
  usernameContainer: {
    flex: 1,
    justifyContent: 'center',
    flexWrap: 'wrap',
    gap: 6,
  },
});

export default styles;
