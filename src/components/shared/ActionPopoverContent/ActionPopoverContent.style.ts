import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from '@/src/core/utils/colorUtils';

export default StyleSheet.create({
  popoverCard: {
    backgroundColor: dark.colors.background,
    padding: 25,
    borderRadius: 12,
    width: '100%',
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    maxWidth: 320,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 6,
  },
  popoverImagePlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: dark.colors.tertiary,
    marginBottom: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  popoverTitle: {
    fontSize: 16,
    fontFamily: 'Montserrat-800',
    color: dark.colors.streak,
    textAlign: 'center',
    marginBottom: 12,
    textTransform: 'uppercase',
  },
  popoverMessage: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textLight,
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 20,
  },
  popoverPrimaryButton: {
    backgroundColor: dark.colors.primary,
    borderColor: dark.colors.streak,
    borderWidth: 1,
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
  },
  popoverPrimaryButtonText: {
    color: dark.colors.textLight,
    fontSize: 12,
    textAlign: 'center',
    fontFamily: 'Montserrat-800',
  },
  popoverSecondaryActionText: {
    color: withOpacity(dark.colors.textLight, 0.7),
    fontSize: 12,
    fontFamily: 'Montserrat-800',
    paddingVertical: 10,
    marginTop: 20,
    textAlign: 'center',
  },
});
