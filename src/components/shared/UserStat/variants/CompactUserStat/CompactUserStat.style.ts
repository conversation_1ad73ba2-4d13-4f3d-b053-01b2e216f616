import { StyleSheet } from 'react-native';
import { withOpacity } from 'core/utils/colorUtils';
import Dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  gradientContainer: {
    padding: 1,
    borderRadius: 8,
    width: '100%',
    maxWidth: 1000,
  },
  darkBackgroundContainer: {
    borderRadius: 8,
    backgroundColor: Dark.colors.gradientBackground,
  },
  gradientContentContainer: {
    borderRadius: 8,
  },
  container: {
    borderRadius: 8,
    padding: 12,
    gap: 12,
    backgroundColor: withOpacity(Dark.colors.gradientBackground, 0.95),
  },
  profileContainer: {
    flexDirection: 'row',
    gap: 12,
    flex: 2.5,
  },
  profileImageContainer: {
    borderWidth: 1,
    borderColor: Dark.colors.textDark,
    borderRadius: 8,
    height: 41,
    width: 41,
    overflow: 'hidden',
  },
  userInfoContainer: {},
  userInfoName: {
    fontSize: 20,
    color: Dark.colors.secondary,
    fontWeight: '600',
  },
  countryName: {
    color: 'white',
    fontSize: 10,
  },
  statsContainer: {
    gap: 12,
    flexDirection: 'row',
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    color: Dark.colors.textDark,
  },
  statValue: {
    fontSize: 16,
    color: 'white',
    fontFamily: 'Montserrat-700',
  },
  statContainer: {
    flex: 1,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Dark.colors.tertiary,
    minWidth: 76,
    minHeight: 47,
    paddingVertical: 8,
    paddingHorizontal: 12,
    gap: 2,
  },
});

export default styles;
