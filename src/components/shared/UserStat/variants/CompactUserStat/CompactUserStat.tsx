import React, { useCallback } from 'react';
import { View, Text } from 'react-native';
import _capitalize from 'lodash/capitalize';
import _toUpper from 'lodash/toUpper';
import styles from './CompactUserStat.style';
import { IRenderStat, UserStatProps } from '../../types';
import userReader from '@/src/core/readers/userReader';

const CompactUserStat = ({ user }: UserStatProps) => {
  const renderStat = useCallback(
    ({ label, value, valuePrefix = '' }: IRenderStat) => {
      const statValue = value ? `${valuePrefix}${value}` : '-';
      return (
        <View style={styles.statContainer}>
          <Text style={styles.statValue} numberOfLines={1}>
            {statValue}
          </Text>
          <Text style={styles.statLabel} numberOfLines={1}>
            {label}
          </Text>
        </View>
      );
    },
    [],
  );

  return (
    <View style={styles.statsContainer}>
      {renderStat({ label: 'Rating', value: userReader.rating(user) })}
      {renderStat({ label: '#Rank', value: userReader.globalRank(user) })}
      {renderStat({
        label: 'Games',
        value: userReader.gamesPlayed(user),
        valuePrefix: '',
      })}
      {/*{renderStat({ label: 'Win', value: '', valuePrefix: '' })}*/}
    </View>
  );
};

export default React.memo(CompactUserStat);
