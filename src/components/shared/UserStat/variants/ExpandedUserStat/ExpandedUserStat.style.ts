import { StyleSheet } from 'react-native';
import { withOpacity } from 'core/utils/colorUtils';
import Dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  gradientContainer: {
    padding: 1,
    borderRadius: 16,
    width: '100%',
    maxWidth: 1000,
  },
  darkBackgroundContainer: {
    borderRadius: 16,
    backgroundColor: Dark.colors.gradientBackground,
  },
  gradientContentContainer: {
    borderRadius: 16,
  },
  container: {
    borderRadius: 16,
    padding: 28,
    backgroundColor: withOpacity(Dark.colors.gradientBackground, 0.95),
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileContainer: {
    flexDirection: 'row',
    gap: 12,
    flex: 2.5,
  },
  profileImageContainer: {
    borderWidth: 1,
    borderColor: Dark.colors.textDark,
    borderRadius: 8,
    height: 41,
    width: 41,
    overflow: 'hidden',
  },
  userInfoContainer: {
    gap: 6,
    marginVertical: 10,
  },
  userInfoName: {
    fontSize: 22,
    color: Dark.colors.secondary,
    fontFamily: 'Montserrat-600',
  },
  countryName: {
    color: 'white',
    fontSize: 12,
  },
  statLabel: {
    fontSize: 12,
    color: Dark.colors.textDark,
  },
  statValue: {
    fontSize: 22,
    color: 'white',
    fontFamily: 'Montserrat-400',
  },
  statContainer: {
    flex: 1,
    gap: 2,
  },
});

export default styles;
