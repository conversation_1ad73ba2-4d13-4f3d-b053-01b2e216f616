import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import dark from '@/src/core/constants/themes/dark';
import useVerifyOTP from 'core/hooks/useVerifyOTP';
import OTPInput from '../OTPInput';
import styles from './EmailVerificationFlow.style';
import { EmailVerificationFlowProps } from './types';

const OTP_LENGTH_LIMIT = 4;
const TIMER_DURATION = 300;

const EmailVerificationFlow: React.FC<EmailVerificationFlowProps> = ({
  onVerificationComplete,
  sendOTPToEmail,
  isSendingOTP,
  isOtpSent,
  onChangeEmail,
}) => {
  const [otp, setOtp] = useState('');
  const [timer, setTimer] = useState(TIMER_DURATION);
  const [canResend, setCanResend] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [isVerified, setIsVerified] = useState(false);
  const [isOTPIncorrect, setIsOTPIncorrect] = useState(false);
  const { verifyOTP, isVerifyingOTP } = useVerifyOTP();

  useEffect(() => {
    if (isOtpSent) {
      setTimer(TIMER_DURATION);
      setCanResend(false);
    }
  }, [isOtpSent]);

  useEffect(() => {
    if (timer > 0) {
      timerRef.current = setInterval(() => {
        setTimer((prev) => {
          if (prev <= 1) {
            if (timerRef.current) clearInterval(timerRef.current);
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [timer]);

  const handleSendOTP = useCallback(async () => {
    const optSent = await sendOTPToEmail();
    if (optSent) {
      setTimer(TIMER_DURATION);
      setCanResend(false);
      setOtp('');
    }
  }, [sendOTPToEmail]);

  const handleOTPChange = useCallback(
    async (value: string) => {
      setOtp(value);
      if (value.length === OTP_LENGTH_LIMIT) {
        const isValid = await verifyOTP(value);
        if (isValid) {
          onVerificationComplete(true);
          setIsVerified(true);
          setIsOTPIncorrect(false);
        } else {
          setIsOTPIncorrect(true);
        }
      } else {
        setIsOTPIncorrect(false);
      }
    },
    [verifyOTP, onVerificationComplete],
  );

  const handleChangeEmail = useCallback(() => {
    setIsVerified(false);
    setOtp('');
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    onChangeEmail?.();
  }, [onChangeEmail]);

  return (
    <View style={styles.container}>
      {!isVerified && (
        <>
          <OTPInput
            value={otp}
            onChange={handleOTPChange}
            length={OTP_LENGTH_LIMIT}
            disabled={isVerifyingOTP}
            isError={isOTPIncorrect}
          />
          <View style={styles.actionsContainer}>
            <TouchableOpacity onPress={handleChangeEmail}>
              <Text style={styles.changeEmailText}>Change Email</Text>
            </TouchableOpacity>
            {timer > 0 ? (
              <Text style={styles.timerText}>Resend OTP in {timer}s</Text>
            ) : (
              <TouchableOpacity
                onPress={handleSendOTP}
                disabled={isSendingOTP}
                style={styles.resendButton}
              >
                <Text
                  style={[
                    styles.resendText,
                    isSendingOTP && { color: dark.colors.textLight },
                  ]}
                >
                  {isSendingOTP ? 'Sending...' : 'Resend OTP'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </>
      )}
      {isVerified && (
        <TouchableOpacity onPress={handleChangeEmail}>
          <Text style={[styles.changeEmailText, { marginTop: 0 }]}>
            Edit Email
          </Text>
        </TouchableOpacity>
      )}
      {isVerified && (
        <View>
          <View style={styles.verifiedContainer}>
            <Text style={styles.verifiedText}>✅ Email Verified</Text>
          </View>
        </View>
      )}
    </View>
  );
};

export default React.memo(EmailVerificationFlow);
