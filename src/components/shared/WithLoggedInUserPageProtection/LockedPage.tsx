import React from 'react';
import { Text, View } from 'react-native';

import { FontAwesome } from '@expo/vector-icons';
import Dark from '@/src/core/constants/themes/dark';
import GoogleLoginButton from '../../../core/oauth/components/GoogleLoginButton';
import styles from './LockedPage.style';

const LockedPage = () => {
  return (
    <View style={styles.container}>
      <FontAwesome name="lock" size={48} color={Dark.colors.secondary} />
      <Text style={styles.label}>
        This page is locked for signed in users only. Please Sign In to view
        this page
      </Text>
      <View style={styles.googleSignInButton}>
        <GoogleLoginButton />
      </View>
    </View>
  );
};

export default LockedPage;
