import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import Dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderWidth: 1,
    gap: 12,
    borderColor: dark.colors.tertiary,
    borderRadius: 8,
  },
  ratingsTitleText: {
    fontSize: 10,
    color: Dark.colors.textLight,
    opacity: 0.4,
    fontFamily: 'Montserrat-600',
    lineHeight: 12,
    marginBottom: 0,
    letterSpacing: 0.8,
  },
  innerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
    flexWrap: 'wrap',
  },
  icon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  title: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textDark,
  },
  rating: {
    fontSize: 20,
    color: 'white',
    lineHeight: 24,
    letterSpacing: 1,
    fontFamily: 'Montserrat-900',
  },
  individualRatingInfoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    width: 120,
    borderRadius: 8,
    padding: 12,
    backgroundColor: dark.colors.gradientBackground,
  },
});

export default styles;
