import React from 'react';
import { View } from 'react-native';
import ShimmerView from 'molecules/ShimmerView';
import Dark from 'core/constants/themes/dark';
import styles from './PlaceholderRow.style';

const PlaceholderRow = () => {
  return (
    <View style={styles.rowContainer}>
      <View style={styles.rankColumn}>
        <ShimmerView
          style={{ width: 20, height: 20 }}
          shimmerColors={Dark.colors.placeholderShimmerColors}
        />
      </View>
      <View style={styles.profileInfoColumn}>
        <ShimmerView
          style={{ width: '100%', height: 20 }}
          shimmerColors={Dark.colors.placeholderShimmerColors}
        />
      </View>
      <View style={styles.ratingColumn}>
        <ShimmerView
          style={{ width: 40, height: 20 }}
          shimmerColors={Dark.colors.placeholderShimmerColors}
        />
      </View>
    </View>
  );
};

export default React.memo(PlaceholderRow);
