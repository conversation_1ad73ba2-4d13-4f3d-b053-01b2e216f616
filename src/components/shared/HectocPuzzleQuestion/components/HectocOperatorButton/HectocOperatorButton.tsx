import React from 'react';
import { Pressable, StyleProp, Text, ViewStyle } from 'react-native';
import { useHectocPuzzleQuestion } from '../../context';
import styles from './HectocOperatorButton.style';

interface HectocOperatorButtonProps {
  char: string;
  disabled?: boolean;
  style?: StyleProp<ViewStyle>;
  opacity?: number;
}

const HectocOperatorButton: React.FC<HectocOperatorButtonProps> = ({
  char,
  disabled = false,
  style,
  opacity = 1,
}) => {
  const { appendCharacter } = useHectocPuzzleQuestion();

  const handlePress = () => {
    if (!disabled) {
      appendCharacter(char);
    }
  };

  return (
    <Pressable
      onPress={handlePress}
      disabled={disabled}
      style={({ pressed }) => [
        styles.optionContainer,
        {
          opacity: disabled ? 0.5 : pressed ? 0.7 : opacity,
        },
        style,
      ]}
    >
      <Text style={styles.text}>{char === '*' ? '×' : char}</Text>
    </Pressable>
  );
};

export default React.memo(HectocOperatorButton);
