import React, { useEffect } from 'react';
import { Text, View } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSequence,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import dark from '@/src/core/constants/themes/dark';
import { useMathMazePuzzle } from '../../context';
import { styles } from './MathMazePuzzleQuestionTargetDisplay.style';

const MathMazePuzzleQuestionTargetDisplay: React.FC = () => {
  const { state } = useMathMazePuzzle();
  const { target, result, reachedEnd } = state;

  const targetScale = useSharedValue(1);

  useEffect(() => {
    targetScale.value = withSequence(
      withTiming(1.2, { duration: 200 }),
      withSpring(1, { damping: 8, stiffness: 100 }),
    );
  }, [target, targetScale]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: targetScale.value }],
  }));

  const targetText = !reachedEnd ? '' : result;

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <View style={styles.labelContainer}>
        <MaterialIcons name="cancel" size={24} style={styles.cancelIcon} />
        <Text style={styles.labelText}>TARGET GOAL</Text>
      </View>
      <View style={styles.targetContainer}>
        {reachedEnd && (
          <Text
            style={[
              styles.targetText,
              result !== target
                ? { color: dark.colors.wrong }
                : { color: dark.colors.secondary },
            ]}
          >
            {targetText}
          </Text>
        )}
        <Text style={[styles.targetText]}>
          {reachedEnd && '/'}
          {target}
        </Text>
      </View>
    </Animated.View>
  );
};

export default React.memo(MathMazePuzzleQuestionTargetDisplay);
