import { Platform, StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { TILE_CONFIG } from '../../../constants/puzzleConstants';

const getStyles = (tileSize: number, isStart: boolean) =>
  StyleSheet.create({
    container: {
      position: 'absolute',
      top: isStart ? -40 : 58,
      left: TILE_CONFIG.MARGIN + tileSize / 2 - 30,
      width: 60,
      height: 30,
      overflow: 'visible',
    },
    triangle: {
      position: 'absolute',
      top: isStart ? 31 : -10,
      left: 19,
      width: 0,
      height: 0,
      zIndex: 200,
      borderLeftWidth: 10,
      borderRightWidth: 10,
      borderBottomWidth: !isStart ? 10 : 0,
      borderTopWidth: isStart ? 10 : 0,
      borderLeftColor: 'transparent',
      borderRightColor: 'transparent',
      borderTopColor: dark.colors.mathsMazeSelectedGridColor,
      borderBottomColor: dark.colors.mathsMazeSelectedGridColor,
    },
    triangleBorderBackground: {
      position: 'absolute',
      top: isStart ? 28 : -8,
      left: 19,
      width: 0,
      height: 0,
      zIndex: 250,
      borderLeftWidth: 10,
      borderRightWidth: 10,
      borderBottomWidth: !isStart ? 10 : 0,
      borderTopWidth: isStart ? 10 : 0,
      borderLeftColor: 'transparent',
      borderRightColor: 'transparent',
      borderTopColor: dark.colors.darkBg,
      borderBottomColor: dark.colors.darkBg,
    },
    button: {
      borderRadius: 10,
      overflow: 'hidden',
      width: '100%',
      height: '100%',
      borderColor: dark.colors.mathsMazeSelectedGridColor,
      borderWidth: 1,
      backgroundColor: dark.colors.darkBg,
    },
    buttonContent: {
      width: '100%',
      height: '100%',
      elevation: 5, // For Android 3D effect
      shadowColor: '#000', // iOS shadow
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      transform: [{ translateY: 0 }], // Default position
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 8,
      ...Platform.select({
        web: {
          transition: 'transform 0.1s',
        },
      }),
    },
    buttonBorderBackground: {
      backgroundColor: dark.colors.mathsMazeSelectedGridColor,
      width: '100%',
      height: 20,
      borderRadius: 10,
      position: 'absolute',
      borderWidth: 10,
      borderColor: dark.colors.mathsMazeSelectedGridColor,
      bottom: -2,
    },
    pressedButton: {
      transform: [{ translateY: 4 }],
      borderBottomWidth: 0,
    },
    text: {
      color: 'white',
      fontSize: 10,
      fontFamily: 'Montserrat-800',
      textAlign: 'center',
    },
    hoveredButton: {
      backgroundColor: 'black',
    },
    hoveredButtonSecondary: {
      backgroundColor: dark.colors.background,
    },
    disabledStyle: {
      opacity: 0.5,
    },
  });

export default getStyles;
