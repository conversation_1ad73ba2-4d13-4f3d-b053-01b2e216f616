import React from 'react';
import { View, Text, StyleSheet, SafeAreaView, Platform } from 'react-native';
import { LineGraph, TooltipData } from 'shared/LineGraph';
import type {
  DataSet,
  DataPoint as LineGraphDataPoint,
  ComparisonPointInfo,
} from 'shared/LineGraph';
import dark from '@/src/core/constants/themes/dark';
import _findIndex from 'lodash/findIndex';

interface TestDataPoint extends LineGraphDataPoint {
  time?: string;
  prevTime?: string;
  nextTime?: string;
  isHighlighted?: boolean;
}

interface TestDataSet extends Omit<DataSet, 'data'> {
  data: TestDataPoint[];
}

const GraphTestScreen = () => {
  const imageBasedDataSets: TestDataSet[] = [
    {
      name: 'Metric 1',
      data: [
        { x: 'Q1', y: 20, time: '00:18:10' },
        { x: 'Q2', y: 40, time: '00:19:00' },
        { x: 'Q3', y: 30, time: '00:19:50' },
        { x: 'Q4', y: 60, time: '00:20:32' },
        { x: 'Q5', y: 90, time: '00:21:15' },
        { x: 'Q6', y: 55, time: '00:22:00' },
        { x: 'Q7', y: 25, time: '00:22:40' },
        { x: 'Q8', y: 80, time: '00:23:10' },
        { x: 'Q9', y: 70, time: '00:23:55' },
        { x: 'Q10', y: 50, time: '00:24:32' },
        { x: 'Q11', y: 60, time: '00:25:00' },
        { x: 'Q12', y: 75, time: '00:25:30' },
        { x: 'Q13', y: 80, time: '00:26:00' },
        { x: 'Q14', y: 65, time: '00:26:30' },
      ],
      color: dark.colors.secondary,
      strokeWidth: 1,
      showPoints: true,
      pointRadius: 6,
      pointColor: dark.colors.secondary,
    },
    {
      name: 'Metric 2',
      data: [
        { x: 'Q1', y: 50 },
        { x: 'Q2', y: 25 },
        { x: 'Q3', y: 95 },
        { x: 'Q4', y: 35 },
        { x: 'Q5', y: 75 },
        { x: 'Q6', y: 80 },
        { x: 'Q7', y: 65 },
        { x: 'Q8', y: 45 },
        { x: 'Q9', y: 75 },
        { x: 'Q10', y: 40 },
      ],
      color: dark.colors.tertiary,
      strokeWidth: 1,
      showPoints: true,
      pointRadius: 6,
      pointColor: dark.colors.tertiary,
    },
  ];

  const renderCustomTooltip = (tooltipData: TooltipData | null) => {
    if (!tooltipData) return null;

    const primaryPoint = tooltipData;
    const comparisonPoints = tooltipData.comparisonPoints || [];

    let displayTimesSource: TestDataPoint | ComparisonPointInfo | undefined =
      primaryPoint.dataSet.name === 'Metric 1'
        ? (primaryPoint as TestDataPoint)
        : undefined;

    if (!displayTimesSource) {
      const metric1Comparison = comparisonPoints.find(
        (cp) => cp.dataSetName === 'Metric 1',
      );
      if (metric1Comparison) {
        displayTimesSource = metric1Comparison.originalPoint as TestDataPoint;
      }
    }

    const metric1TimeIndex = _findIndex(
      comparisonPoints,
      (cp) => cp.dataSetName === 'Metric 1',
    );
    const metric1Time =
      metric1TimeIndex != -1 ? comparisonPoints[metric1TimeIndex]?.y : '';
    const metric2TimeIndex = _findIndex(
      comparisonPoints,
      (cp) => cp.dataSetName === 'Metric 2',
    );
    const metric2Time =
      metric2TimeIndex != -1 ? comparisonPoints[metric2TimeIndex]?.y : '';

    const currentLabel = primaryPoint.x;
    const isMetric1TimeLesser = metric1Time < metric2Time;

    return (
      <View style={styles.tooltipCustom}>
        <Text style={styles.timeText}>{metric1Time}</Text>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          {isMetric1TimeLesser && (
            <Text style={styles.tooltipSeparator}>{'<'}</Text>
          )}
          <View style={styles.tooltipTimeSegment}>
            <Text style={styles.tooltipMainLabel}>{currentLabel}</Text>
          </View>
          {!isMetric1TimeLesser && (
            <Text style={styles.tooltipSeparator}>{'>'}</Text>
          )}
        </View>
        <Text style={styles.timeText}>{metric2Time}</Text>
      </View>
    );
  };

  const q5DataPointIndex = imageBasedDataSets[0].data.findIndex(
    (p) => p.x === 'Q5',
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <LineGraph
          dataSets={imageBasedDataSets}
          width={Platform.OS === 'web' ? 600 : 380}
          height={450}
          backgroundColor="#1E1E1E"
          padding={{ top: 90, right: 20, bottom: 40, left: 40 }}
          xAxis={{
            show: true,
            color: '#555555',
            tickLabelColor: '#BBBBBB',
            tickLabelFontSize: 12,
            showGridLines: false,
          }}
          yAxis={{
            show: true,
            color: '#555555',
            tickLabelColor: '#BBBBBB',
            tickLabelFontSize: 10,
            showGridLines: true,
            gridLineColor: '#444444',
            gridLineStrokeWidth: 1,
            numberOfTicks: 5,
            gridLineStrokeDasharray: '',
            labelFormatter: (value) => String(Math.round(value)),
            min: 0,
            max: 100,
          }}
          tooltip={{
            enabled: true,
            renderContent: renderCustomTooltip,
            showConnectorLine: true,
            connectorLineColor: '#777777',
            connectorLineWidth: 1,
            fixedTopOffset: 15,
            connectorLineStrokeDasharray: '',
          }}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#121212',
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    backgroundColor: '#121212',
  },
  header: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 20,
  },
  tooltipCustom: {
    backgroundColor: '#343434',
    borderRadius: 20,
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: 12,
    paddingVertical: 5,
    gap: 10,
  },
  timeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontFamily: 'Montserrat-800',
  },
  tooltipTopBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 15,
    marginBottom: 5,
  },
  tooltipTimeSegment: {
    alignItems: 'center',
    backgroundColor: dark.colors.primary,
    paddingHorizontal: 6.5,
    paddingVertical: 2,
    borderRadius: 4,
  },
  tooltipTimeText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  tooltipMainLabel: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginHorizontal: 10,
  },
  tooltipSeparator: {
    color: dark.colors.victoryColor,
    fontSize: 16,
    fontWeight: 'bold',
    marginHorizontal: 5,
  },
  tooltipCircle: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginHorizontal: 8,
  },
  comparisonContainer: {
    marginTop: 8,
    paddingHorizontal: 15,
    alignSelf: 'stretch',
  },
  comparisonItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  legendColorBox: {
    width: 10,
    height: 10,
    marginRight: 6,
  },
  comparisonText: {
    color: '#FFFFFF',
    fontSize: 12,
  },
});

export default GraphTestScreen;
