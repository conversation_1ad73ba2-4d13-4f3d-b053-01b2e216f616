import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    height: 42,
    maxHeight: 42,
    flex: 1,
  },
  primaryButton: {
    backgroundColor: dark.colors.secondary,
  },
  primaryButtonText: {
    color: dark.colors.background,
    fontSize: 14,
    fontFamily: 'Montserrat-600',
  },
  secondaryButton: {
    borderColor: dark.colors.secondary,
    borderWidth: 1,
    backgroundColor: 'transparent',
  },
  secondaryButtonText: {
    color: dark.colors.secondary,
    fontSize: 14,
    fontFamily: 'Montserrat-600',
  },
  defaultButton: {
    backgroundColor: dark.colors.tertiary,
    paddingHorizontal: 16,
  },
  defaultButtonText: {
    color: dark.colors.text,
    fontSize: 14,
    fontFamily: 'Montserrat-600',
  },
});

export default styles;
