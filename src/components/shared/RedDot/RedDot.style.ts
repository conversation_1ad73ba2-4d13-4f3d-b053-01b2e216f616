import { Platform, StyleSheet } from 'react-native';
import Dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  redDotContainer: {
    width: 9,
    height: 9,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    backgroundColor: withOpacity(Dark.colors.red, 0.4),
    shadowColor: Dark.colors.red,
    shadowOpacity: 1.0,
    shadowOffset: { width: 0, height: 0 },
    shadowRadius: 8,
    elevation: 20,
    ...Platform.select({
      web: {
        shadowRadius: 20,
      },
    }),
  },
  redDot: {
    width: 6,
    height: 6,
    borderRadius: 4,
    backgroundColor: Dark.colors.red,
  },
});

export default styles;
