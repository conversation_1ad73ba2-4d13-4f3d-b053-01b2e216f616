import React, { useMemo } from 'react';
import { Text, View } from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import _isNumber from 'lodash/isNumber';
import _isEmpty from 'lodash/isEmpty';
import _size from 'lodash/size';
import _slice from 'lodash/slice';
import _reduce from 'lodash/reduce';
import _map from 'lodash/map';
import _round from 'lodash/round';
import dark from '../../../core/constants/themes/dark';
import styles from './WavyGraphComponent.style';
import { WavyGraphComponentProps } from './types';

const MAX_GRAPH_SIZE = 50;

const WavyGraphComponent: React.FC<WavyGraphComponentProps> = (props) => {
  const { dataPoints: dataPointsInMs, height = 220, width = 300 } = props;

  const dataPoints = useMemo(
    () =>
      _map(dataPointsInMs, (time) => {
        if (_isNumber(time)) {
          return _round(time / 1000, 2);
        }
        return time;
      }),
    [dataPointsInMs],
  );

  const adaptedDataPoints = useMemo(() => {
    if (_size(dataPoints) <= MAX_GRAPH_SIZE) return dataPoints;

    if (!_isNumber(dataPoints[0])) return dataPoints;

    const originalSize = _size(dataPoints);

    const movingAverage = Math.ceil(originalSize / MAX_GRAPH_SIZE);

    const reducedArray = [];
    for (let i = 0; i < originalSize; i += movingAverage) {
      const chunk = _slice(dataPoints, i, i + movingAverage);
      if (_size(chunk) === 0) {
        return reducedArray;
      }
      const average = _reduce(chunk, (sum, val) => sum + val, 0) / _size(chunk);
      reducedArray.push(_round(average, 2));
    }

    return reducedArray;
  }, [dataPoints]);

  const chartData = {
    labels: [],
    datasets: [
      {
        data: adaptedDataPoints,
        color: () => `rgba(255, 255, 255, 1)`,
        strokeWidth: 1,
      },
    ],
  };

  if (_isEmpty(dataPoints)) {
    return null;
  }

  return (
    <View style={styles.container}>
      {dataPoints.length > 2 && (
        <LineChart
          data={chartData}
          width={width}
          height={height}
          yAxisSuffix="s"
          yAxisInterval={1}
          withDots
          withInnerLines={false}
          withOuterLines={false}
          bezier
          chartConfig={{
            backgroundGradientFrom: dark.colors.primary,
            backgroundGradientTo: dark.colors.primary,
            fillShadowGradientTo: '#FFA79B',
            fillShadowGradientFrom: '#FFA79B',
            fillShadowGradientToOpacity: 0.2,
            fillShadowGradientFromOpacity: 0.2,
            color: () => `red`,
            labelColor: () => dark.colors.textDark,
            propsForDots: {
              r: '3',
              strokeWidth: '1',
              stroke: dark.colors.secondary,
            },
            propsForBackgroundLines: {
              strokeDasharray: '',
            },
          }}
          style={styles.chartStyle}
        />
      )}
      {dataPoints.length <= 2 && (
        <View
          style={{
            height,
            width,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Text style={styles.notSufficientData}>
            Not Sufficient Data to Show
          </Text>
        </View>
      )}
    </View>
  );
};

export default WavyGraphComponent;
