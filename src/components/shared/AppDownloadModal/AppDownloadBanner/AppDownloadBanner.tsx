import { Image, Linking, Platform, Text, TouchableOpacity } from 'react-native';
import LinearGradient from 'atoms/LinearGradient';
import useAppDownloadBannerStyle from './AppDownloadBanner.style';
import { View } from 'react-native-animatable';
import { useCallback, useState } from 'react';
import useMediaQuery from 'core/hooks/useMediaQuery';
import AppDownloadImage from 'assets/images/app_download_image.png';
import AppDownloadModal from '../AppDownloadModal';
import Analytics from '../../../../core/analytics';
import { ANALYTICS_EVENTS } from '../../../../core/analytics/const';
import dark from '../../../../core/constants/themes/dark';
import { AppDownloadBannerProps } from './types';

const AppDownloadBanner : React.FC<AppDownloadBannerProps> = (props) => {
  const { trackingProperties } = props;

  const styles = useAppDownloadBannerStyle();
//   const router = useRouter();
  const [isModalVisible, setIsModalVisible] = useState(false);

  const { isMobile: isCompactMode } = useMediaQuery();

  const onDownloadNowPressed = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.CLICKED_ON_DOWNLOAD_APP_NOW_BUTTON,
      trackingProperties,
    );
    if (!isCompactMode) {
      setIsModalVisible(true);
    } else {
      Linking.openURL(
        'https://play.google.com/store/apps/details?id=com.matiks.app',
      );
    }
  }, [isCompactMode, trackingProperties, setIsModalVisible]);

  const closeModal = useCallback(() => {
    setIsModalVisible(false);
  }, [setIsModalVisible]);

  if (Platform.OS !== 'web') {
    return null;
  }

  return (
    <>
      <LinearGradient
        colors={['#233422', '#39390A']}
        style={styles.gradientContainer}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <View style={{ gap: 10, width: isCompactMode ? '80%' : '90%' }}>
          <Text style={styles.downloadText}>
            Don't just compete, play with your friends. Engange in 1 min duels
            and increase your rating —
            <Text style={{ fontFamily: 'Montserrat-900' }}>{' Download '}</Text>
            <Text
              style={{
                color: dark.colors.secondary,
                fontFamily: 'Montserrat-900',
              }}
            >
              {'Matiks '}
            </Text>
            <Text style={{ fontFamily: 'Montserrat-900' }}>{'App Now!'}</Text>
          </Text>
          <TouchableOpacity
            style={styles.downloadButton}
            onPress={onDownloadNowPressed}
          >
            <Text style={[styles.downloadText, styles.downloadButtonText]}>
              Download App
            </Text>
          </TouchableOpacity>
        </View>
        <Image
          source={AppDownloadImage}
          style={{
            width: isCompactMode ? '20%' : '10%',
            height: isCompactMode ? 90 : 120,
            resizeMode: 'contain',
          }}
        />
      </LinearGradient>
      <AppDownloadModal
        isModalVisible={isModalVisible}
        onClose={closeModal}
        key={'App-Download-Modal'}
      />
    </>
  );
};

export default AppDownloadBanner;
