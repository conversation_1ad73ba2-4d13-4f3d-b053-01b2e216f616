import React from 'react';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import CompactCarouselImageBanner from './CompactCarouselImageBanner';
import ExpandedCarouselImageBanner from './ExpandedCarouselImageBanner';
import { CarouselImageBannerProps } from './types';

const CarouselImageBanner : React.FC<CarouselImageBannerProps> = (props) => {
  const { isMobile: isCompactMode } = useMediaQuery();

  if (isCompactMode) {
    return <CompactCarouselImageBanner {...props} />;
  }
  return <ExpandedCarouselImageBanner {...props} />;
};

export default React.memo(CarouselImageBanner);
