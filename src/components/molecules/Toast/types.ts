import { ActivityIndicatorProps, TextStyle, ViewStyle } from "react-native";
import { TOAST_TYPE } from "./Toast";

interface IconProps {
  name: string;
  color?: string;
  size?: number;
  iconSet?: string;
  iconStyle?: TextStyle;
}

interface CtaProps {
  label?: string;
  onPress?: () => void;
  buttonData?: any;
}

export interface ToastProps {
  toastContainerStyle?: ViewStyle;
  loadingToastContainerStyle?: ViewStyle;
  renderDismissIcon?: () => React.ReactNode;
  loaderProps?: Partial<ActivityIndicatorProps>;
  loadingTextStyle?: TextStyle;
  contentContainerStyle?: ViewStyle;
  containerStyle?: ViewStyle;
  iconContainerStyle?: ViewStyle;
  messageContainerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  descriptionStyle?: TextStyle;
  iconProps?: IconProps;
}

export interface ToastState {
  show: boolean;
  type?: ToastType;
  timeInterval?: number;
  showCTA?: boolean;
  cta?: CtaProps;
  title?: string;
  description?: string;
  getToastBackgroundColor?: (type: ToastType) => string;
  renderStatusContent?: (state: ToastState) => React.ReactNode;
  iconProps?: IconProps;
  loaderProps?: Partial<ActivityIndicatorProps>;
  renderDismissIcon?: () => React.ReactNode;
}

export type ToastType = (typeof TOAST_TYPE)[keyof typeof TOAST_TYPE];

export interface ToastPropDetails {
  iconProps: IconProps;
  backgroundColor: string;
}