import React from 'react';
import { View } from 'react-native';
import { Sheet, SnapPointsMode, YStack } from 'tamagui';
import AntDesign from '@expo/vector-icons/AntDesign';
import _isNil from 'lodash/isNil';
import _isFunction from 'lodash/isFunction';

import dark from '@/src/core/constants/themes/dark';
import defaultStyles from './BottomSheet.style';
import { BottomSheetCompactProps } from './types';
import useBottomSheet from './useBottomSheet';

const BottomSheetCompact: React.FC<BottomSheetCompactProps> = ({
  isOpen,
  config,
  onClose,
}) => {
  const { handleClose, dismissOnOverlayPress, animation } = useBottomSheet({
    isOpen,
    config,
    onClose,
  });

  if (!isOpen || !config?.content) {
    return null;
  }

  const renderCloseButton = () => {
    if (
      !_isNil(config.renderCloseButton) &&
      _isFunction(config.renderCloseButton)
    ) {
      return config.renderCloseButton();
    }

    return (
      <View
        style={{
          top: -45,
          width: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}
        pointerEvents="box-none"
      >
        <View
          style={{
            ...defaultStyles.closeButton,
            ...config.styles?.closeButton,
          }}
          pointerEvents="auto"
        >
          <AntDesign
            name="close"
            color={dark.colors.textDark}
            size={22}
            onPress={handleClose}
          />
        </View>
      </View>
    );
  };

  const DefaultFrameComponent = ({
    children,
  }: {
    children: React.ReactNode;
  }) => (
    <Sheet.Frame
      backgroundColor={dark.colors.background}
      borderTopColor={dark.colors.secondary}
      borderTopWidth={4}
      pointerEvents="auto"
      {...config.styles?.frame}
    >
      <YStack gap="$4" pointerEvents="auto">
        <YStack {...config.styles?.contentContainer} pointerEvents="auto">
          {children}
        </YStack>
      </YStack>
    </Sheet.Frame>
  );

  const FrameComponent = config.renderFrameComponent || DefaultFrameComponent;

  const extraProps = _isNil(config.snapPoints)
    ? {
        snapPointsMode: 'fit' as SnapPointsMode,
      }
    : {
        snapPoints: config.snapPoints,
        snapPointsMode: 'percent' as SnapPointsMode,
      };

  return (
    <Sheet
      disableDrag
      moveOnKeyboardChange
      modal
      zIndex={200000}
      open={isOpen}
      dismissOnSnapToBottom
      dismissOnOverlayPress={dismissOnOverlayPress}
      animation={animation}
      {...extraProps}
    >
      <Sheet.Overlay
        animation={animation}
        enterStyle={{ opacity: 0 }}
        exitStyle={{ opacity: 0 }}
        pointerEvents={dismissOnOverlayPress ? 'auto' : 'none'}
        onPress={(e) => {
          if (!dismissOnOverlayPress) {
            e.preventDefault();
          }
        }}
      />
      <Sheet.Handle />
      {dismissOnOverlayPress && renderCloseButton()}
      <FrameComponent>
        {_isFunction(config.content)
          ? config.content({ closeBottomSheet: handleClose })
          : config.content}
      </FrameComponent>
    </Sheet>
  );
};

export default BottomSheetCompact;
