import { useCallback, useEffect, useRef } from 'react';
import { BackHandler } from 'react-native';
import { BottomSheetConfig } from './types';

interface UseBottomSheetProps {
  isOpen: boolean;
  config: BottomSheetConfig | null;
  onClose: () => void;
}

export const useBottomSheet = ({ isOpen, config, onClose }: UseBottomSheetProps) => {
  const backHandlerRef = useRef<any>(null);

  const handleBackPress = useCallback((): boolean => {
    if (isOpen) {
      onClose();
      return true;
    }
    return false;
  }, [isOpen, onClose]);

  const addBackHandler = useCallback(() => {
    removeBackHandler();
    if (isOpen) {
      backHandlerRef.current = BackHandler.addEventListener(
        'hardwareBackPress',
        handleBackPress,
      );
    }
  }, [isOpen, handleBackPress]);

  const removeBackHandler = useCallback(() => {
    if (backHandlerRef.current) {
      backHandlerRef.current.remove();
      backHandlerRef.current = null;
    }
  }, []);

  const handleClose = useCallback(() => {
    if (config?.onCloseBottomSheet) {
      config.onCloseBottomSheet();
    }
    onClose();
  }, [config, onClose]);

  useEffect(() => {
    if (isOpen) {
      addBackHandler();
    } else {
      removeBackHandler();
    }

    return () => {
      removeBackHandler();
    };
  }, [isOpen, addBackHandler, removeBackHandler]);

  return {
    handleClose,
    dismissOnOverlayPress: config?.dismissOnOverlayPress !== false,
    animation: config?.animation || '200ms',
  };
};

export default useBottomSheet;
