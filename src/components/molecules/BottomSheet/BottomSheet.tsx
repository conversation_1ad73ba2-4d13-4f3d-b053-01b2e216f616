import React from 'react';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import BottomSheetCompact from './BottomSheetCompact';
import BottomSheetDialog from './BottomSheetDialog';
import { BottomSheetContainer, useBottomSheetContext, getBottomSheetInstance } from './BottomSheetContainer';
import { BottomSheetConfig, OpenBottomSheetProps } from './types';

// Main BottomSheet component that renders based on screen size
const BottomSheet: React.FC = () => {
  const { isMobile } = useMediaQuery();
  const { isOpen, config, closeBottomSheet } = useBottomSheetContext();

  const isCompactUI = isMobile;

  if (isCompactUI) {
    return (
      <BottomSheetCompact
        isOpen={isOpen}
        config={config}
        onClose={closeBottomSheet}
      />
    );
  }

  return (
    <BottomSheetDialog
      isOpen={isOpen}
      config={config}
      onClose={closeBottomSheet}
    />
  );
};

// Wrapper component that provides the container
const BottomSheetWithContainer: React.FC = () => {
  return (
    <BottomSheetContainer>
      <BottomSheet />
    </BottomSheetContainer>
  );
};

// Utility functions for backward compatibility
export function openBottomSheet(props: OpenBottomSheetProps = {}) {
  const instance = getBottomSheetInstance();
  if (instance) {
    const config: BottomSheetConfig = {
      content: props.content || null,
      renderFrameComponent: props.renderFrameComponent,
      renderCloseButton: props.renderCloseButton,
      snapPoints: props.snapPoints,
      styles: props.styles,
      animation: props.animation || '200ms',
      dismissOnOverlayPress: props.dismissOnOverlayPress !== false,
      containerPadding: props.containerPadding,
      onCloseBottomSheet: props.onCloseBottomSheet,
    };
    instance.openBottomSheet(config);
  }
}

export function closeBottomSheet() {
  const instance = getBottomSheetInstance();
  if (instance) {
    instance.closeBottomSheet();
  }
}

// Export the main component
export default BottomSheetWithContainer;

export {
  BottomSheetWithContainer as Component,
  BottomSheetContainer,
  useBottomSheetContext,
  BottomSheet as PureBottomSheet,
};
