import { ComponentType, ReactNode } from 'react';
import { ViewStyle } from 'react-native';
import { SheetProps, YStackProps } from 'tamagui';

export interface BottomSheetContent {
  closeBottomSheet: () => void;
}

export interface BottomSheetStyles {
  frame?: object;
  contentContainer?: YStackProps;
  closeButton?: ViewStyle;
}

export interface BottomSheetConfig {
  content: ReactNode | ((props: BottomSheetContent) => ReactNode);
  renderFrameComponent?: ComponentType<{ children: ReactNode }>;
  renderCloseButton?: () => ReactNode;
  snapPoints?: number[];
  styles?: Partial<BottomSheetStyles>;
  animation?: SheetProps['animation'];
  dismissOnOverlayPress?: boolean;
  containerPadding?: number;
  onCloseBottomSheet?: () => void;
}

export interface BottomSheetState extends BottomSheetConfig {
  isOpen: boolean;
}

export interface BottomSheetContextValue {
  isOpen: boolean;
  config: BottomSheetConfig | null;
  openBottomSheet: (config: BottomSheetConfig) => void;
  closeBottomSheet: () => void;
}

export interface BottomSheetCompactProps {
  isOpen: boolean;
  config: BottomSheetConfig | null;
  onClose: () => void;
}

export interface BottomSheetDialogProps {
  isOpen: boolean;
  config: BottomSheetConfig | null;
  onClose: () => void;
}

// Legacy interfaces for backward compatibility
export interface BottomSheetProps {
  isOpen?: boolean;
  content?: ReactNode;
  containerPadding?: number;
  renderFrameComponent?: ReactNode;
  renderCloseButton?: () => ReactNode;
  onCloseBottomSheet?: () => void;
  snapPoints?: number[];
  styles?: any;
}

export interface OpenBottomSheetProps extends Partial<BottomSheetConfig> {}
