import React, { createContext, useContext, useState, useCallback, useRef } from 'react';
import { BottomSheetConfig, BottomSheetContextValue } from './types';

const BottomSheetContext = createContext<BottomSheetContextValue | null>(null);

interface BottomSheetProviderProps {
  children: React.ReactNode;
}

export const BottomSheetProvider: React.FC<BottomSheetProviderProps> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [config, setConfig] = useState<BottomSheetConfig | null>(null);
  const eventQueueRef = useRef<BottomSheetConfig[]>([]);

  const processNextInQueue = useCallback(() => {
    if (eventQueueRef.current.length > 0) {
      const nextConfig = eventQueueRef.current.shift();
      if (nextConfig) {
        setConfig(nextConfig);
        setIsOpen(true);
      }
    }
  }, []);

  const openBottomSheet = useCallback((newConfig: BottomSheetConfig) => {
    if (isOpen) {
      // If already open, queue the event
      eventQueueRef.current.push(newConfig);
      return;
    }
    
    setConfig(newConfig);
    setIsOpen(true);
  }, [isOpen]);

  const closeBottomSheet = useCallback(() => {
    setIsOpen(false);
    setConfig(null);
    
    // Process next item in queue after a brief delay
    setTimeout(() => {
      processNextInQueue();
    }, 100);
  }, [processNextInQueue]);

  const contextValue: BottomSheetContextValue = {
    isOpen,
    config,
    openBottomSheet,
    closeBottomSheet,
  };

  return (
    <BottomSheetContext.Provider value={contextValue}>
      {children}
    </BottomSheetContext.Provider>
  );
};

export const useBottomSheetContext = (): BottomSheetContextValue => {
  const context = useContext(BottomSheetContext);
  if (!context) {
    throw new Error('useBottomSheetContext must be used within a BottomSheetProvider');
  }
  return context;
};

// Singleton instance for backward compatibility
let containerInstance: BottomSheetContextValue | null = null;

export const setBottomSheetInstance = (instance: BottomSheetContextValue) => {
  containerInstance = instance;
};

export const getBottomSheetInstance = (): BottomSheetContextValue | null => {
  return containerInstance;
};

// Container component that manages the singleton pattern
interface BottomSheetContainerProps {
  children: React.ReactNode;
}

export const BottomSheetContainer: React.FC<BottomSheetContainerProps> = ({ children }) => {
  return (
    <BottomSheetProvider>
      <BottomSheetInstanceManager>
        {children}
      </BottomSheetInstanceManager>
    </BottomSheetProvider>
  );
};

// Component to manage the singleton instance
const BottomSheetInstanceManager: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const context = useBottomSheetContext();
  
  React.useEffect(() => {
    setBottomSheetInstance(context);
    return () => {
      setBottomSheetInstance(null);
    };
  }, [context]);

  return <>{children}</>;
};

export default BottomSheetContainer;
