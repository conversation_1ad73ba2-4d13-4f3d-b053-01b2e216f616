// Main exports
export { default } from './BottomSheet';
export { 
  Component,
  BottomSheetContainer,
  useBottomSheetContext,
  PureBottomSheet,
  openBottomSheet,
  closeBottomSheet,
} from './BottomSheet';

// Individual component exports
export { default as BottomSheetCompact } from './BottomSheetCompact';
export { default as BottomSheetDialog } from './BottomSheetDialog';
export { default as useBottomSheet } from './useBottomSheet';

// Type exports
export type {
  BottomSheetConfig,
  BottomSheetContextValue,
  BottomSheetCompactProps,
  BottomSheetDialogProps,
  BottomSheetContent,
  BottomSheetStyles,
  OpenBottomSheetProps,
  BottomSheetProps, // Legacy
} from './types';
