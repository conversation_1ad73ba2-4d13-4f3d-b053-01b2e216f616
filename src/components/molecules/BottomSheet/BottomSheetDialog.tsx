import React from 'react';
import { <PERSON><PERSON>, Dialog, Unspaced } from 'tamagui';
import AntDesign from '@expo/vector-icons/AntDesign';
import _isFunction from 'lodash/isFunction';

import dark from '@/src/core/constants/themes/dark';
import { BottomSheetDialogProps } from './types';
import useBottomSheet from './useBottomSheet';

const BottomSheetDialog: React.FC<BottomSheetDialogProps> = ({
  isOpen,
  config,
  onClose,
}) => {
  const { handleClose, dismissOnOverlayPress } = useBottomSheet({
    isOpen,
    config,
    onClose,
  });

  if (!isOpen || !config?.content) {
    return null;
  }

  return (
    <Dialog
      modal
      open={isOpen}
      onOpenChange={(open) => {
        if (!open && dismissOnOverlayPress) {
          handleClose();
        }
      }}
    >
      <Dialog.Portal>
        <Dialog.Overlay
          key="overlay"
          animation="slow"
          opacity={0.9}
          enterStyle={{ opacity: 0 }}
          exitStyle={{ opacity: 0 }}
          pointerEvents="none"
        />

        <Dialog.Content
          bordered
          elevate
          borderRadius={4}
          maxWidth={500}
          backgroundColor={dark.colors.background}
          key="content"
          animateOnly={['transform', 'opacity']}
          animation={[
            'quicker',
            {
              opacity: {
                overshootClamping: true,
              },
            },
          ]}
          enterStyle={{ x: 0, y: -20, opacity: 0, scale: 0.9 }}
          exitStyle={{ x: 0, y: 10, opacity: 0, scale: 0.95 }}
          gap="$4"
          pointerEvents="auto"
        >
          {_isFunction(config.content)
            ? config.content({ closeBottomSheet: handleClose })
            : config.content}
          
          {dismissOnOverlayPress && (
            <Unspaced>
              <Dialog.Close asChild>
                <Button
                  position="absolute"
                  top="$3"
                  right="$3"
                  size="$2"
                  circular
                  backgroundColor="transparent"
                  icon={<AntDesign name="close" size={18} color="white" />}
                  onPress={handleClose}
                />
              </Dialog.Close>
            </Unspaced>
          )}
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog>
  );
};

export default BottomSheetDialog;
