import React from 'react';
import { Text, View } from 'react-native';
import InteractiveSecondaryButton from '../../atoms/InteractiveSecondaryButton';
import styles from './NewNumberInput.style';

interface NewNumberInputProps {
  label: string;
  value: number;
  onValueChange: (value: number) => void;
  minValue?: number;
  maxValue?: number;
  incrementBy?: number;
  valueSuffix?: string;
}

const NewNumberInput = ({
  label,
  value,
  onValueChange,
  minValue = 0,
  maxValue = 10,
  incrementBy = 1,
  valueSuffix = '',
}: NewNumberInputProps) => {
  const handleIncrement = () => {
    if (value < maxValue) {
      onValueChange(value + incrementBy);
    }
  };

  const handleDecrement = () => {
    if (value > minValue) {
      onValueChange(value - incrementBy);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.inputContainer}>
        <InteractiveSecondaryButton
          onPress={handleDecrement}
          label="-"
          buttonStyle={styles.button}
          labelStyle={styles.buttonText}
        />
        <Text style={styles.value}>
          {value}
          {valueSuffix}
        </Text>
        <InteractiveSecondaryButton
          onPress={handleIncrement}
          label="+"
          buttonStyle={styles.button}
          labelStyle={styles.buttonText}
        />
      </View>
    </View>
  );
};

export default React.memo(NewNumberInput);
