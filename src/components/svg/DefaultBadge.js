import * as React from 'react'
import Svg, { Path } from 'react-native-svg'
import dark from '../../core/constants/themes/dark'
const SvgComponent = (props) => (
    <Svg
        xmlns="http://www.w3.org/2000/svg"
        width={22}
        height={28}
        fill="none"
        {...props}
    >
        <Path
            // Linear gradient to be added
            fill={dark.colors.secondary}
            d="m10.999 16 2.833 2.167c.133.111.267.117.4.017s.178-.228.133-.384l-1.1-3.533 2.967-2.333a.359.359 0 0 0 .1-.383c-.044-.145-.144-.217-.3-.217h-3.567l-1.133-3.567c-.044-.155-.155-.233-.333-.233-.178 0-.29.078-.334.233l-1.133 3.567H5.965a.312.312 0 0 0-.316.216.334.334 0 0 0 .083.384l2.933 2.333-1.1 3.567c-.044.155 0 .283.134.383.133.1.266.095.4-.016l2.9-2.2Zm0 11.2c-.156 0-.3-.01-.434-.033a2.632 2.632 0 0 1-.4-.1c-3-1-5.389-2.85-7.166-5.55-1.778-2.7-2.667-5.605-2.667-8.716V6.5c0-.556.161-1.056.483-1.5a2.752 2.752 0 0 1 1.25-.967l8-3A2.76 2.76 0 0 1 11 .867c.31 0 .622.056.933.167l8 3c.511.2.928.522 1.25.967.322.444.483.944.483 1.5v6.3c0 3.11-.889 6.016-2.666 8.716-1.778 2.7-4.167 4.55-7.167 5.55a2.632 2.632 0 0 1-.4.1 2.641 2.641 0 0 1-.433.034Z"
        />
    </Svg>
)
export default SvgComponent
