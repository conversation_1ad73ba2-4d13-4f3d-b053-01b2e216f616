import * as React from 'react';
import Svg, { Path } from 'react-native-svg';
import PropTypes from 'prop-types';
import dark from '../../../core/constants/themes/dark';
import { TouchableOpacity } from 'react-native';
import { useCallback } from 'react';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';

const AnalyticsIcon = (props) => {
  const {
    width = 14,
    height = 14,
    fillColor = dark.colors.secondary,
    onPress,
  } = props;

  const onPressAnalyticsIcon = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.PRACTICE_MODULE.CLICKED_ON_ANALYTICS_ICON);
    onPress();
  }, [onPress]);

  return (
    <TouchableOpacity onPress={onPressAnalyticsIcon}>
      <Svg
        xmlns="http://www.w3.org/2000/svg"
        width={width}
        height={height}
        fill="none"
        {...props}
      >
        <Path
          fill={fillColor}
          d="M5.722 10.013c1.192 0 2.205-.417 3.04-1.252.834-.834 1.25-1.847 1.25-3.04 0-1.191-.416-2.204-1.25-3.039-.835-.834-1.848-1.251-3.04-1.251s-2.206.417-3.04 1.251c-.834.835-1.252 1.848-1.252 3.04s.418 2.205 1.252 3.04c.834.834 1.848 1.251 3.04 1.251Zm0-2.146a.692.692 0 0 1-.51-.205.692.692 0 0 1-.206-.51V3.576c0-.202.069-.372.206-.51a.692.692 0 0 1 .51-.205c.202 0 .372.069.51.206a.692.692 0 0 1 .205.51v3.575a.692.692 0 0 1-.206.51.692.692 0 0 1-.51.205Zm-2.504 0a.692.692 0 0 1-.51-.205.692.692 0 0 1-.205-.51V5.007c0-.203.069-.373.206-.51a.692.692 0 0 1 .51-.206c.202 0 .372.069.509.206a.692.692 0 0 1 .206.51v2.145a.692.692 0 0 1-.206.51.692.692 0 0 1-.51.205Zm5.007 0a.692.692 0 0 1-.51-.205.692.692 0 0 1-.205-.51v-1.43c0-.203.068-.373.205-.51a.692.692 0 0 1 .51-.205c.202 0 .372.068.51.205a.692.692 0 0 1 .205.51v1.43a.692.692 0 0 1-.206.51.692.692 0 0 1-.51.205Zm-2.503 3.576c-1.598 0-2.95-.554-4.06-1.662C.555 8.672 0 7.319 0 5.72c0-1.596.554-2.95 1.663-4.058C2.77.555 4.124 0 5.722 0c1.597 0 2.95.555 4.058 1.663 1.109 1.109 1.663 2.462 1.663 4.059a5.67 5.67 0 0 1-1.198 3.522l3.558 3.558a.678.678 0 0 1 .197.5c0 .204-.066.37-.197.502a.678.678 0 0 1-.5.196.678.678 0 0 1-.501-.196l-3.558-3.559a5.67 5.67 0 0 1-3.522 1.198Z"
        />
      </Svg>
    </TouchableOpacity>
  );
};

AnalyticsIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  fillColor: PropTypes.string,
  onPress: PropTypes.func,
};

export default AnalyticsIcon;
