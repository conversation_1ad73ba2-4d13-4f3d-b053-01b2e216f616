import React from 'react';
import Svg, {
  <PERSON><PERSON>Path,
  Defs,
  FeBlend,
  FeColorMatrix,
  FeFlood,
  FeOffset,
  Filter,
  G,
  Mask,
  Path,
  Rect,
} from 'react-native-svg';

const SecondRankSvg = () => (
  <Svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Rect y="3.05176e-05" width="28" height="28" rx="4" fill="#C5D0DC" />
    <Rect
      x="0.5"
      y="0.500031"
      width="27"
      height="27"
      rx="3.5"
      stroke="white"
      stroke-opacity="0.5"
    />
    <G clip-path="url(#clip0_356_353)">
      <Rect x="4" y="4.00003" width="20" height="20" rx="2" fill="#D3DBE4" />
      <G filter="url(#filter0_d_356_353)">
        <Mask
          id="path-4-outside-1_356_353"
          maskUnits="userSpaceOnUse"
          x="8"
          y="8.00003"
          width="11"
          height="13"
          fill="black"
        >
          <Rect fill="white" x="8" y="8.00003" width="11" height="13" />
          <Path d="M9.69094 20V18.016L13.8829 14.096C14.1816 13.8187 14.4003 13.5734 14.5389 13.36C14.6883 13.1467 14.7843 12.9547 14.8269 12.784C14.8803 12.6027 14.9069 12.4374 14.9069 12.288C14.9069 11.9254 14.7843 11.6427 14.5389 11.44C14.3043 11.2374 13.9469 11.136 13.4669 11.136C13.0616 11.136 12.6829 11.2267 12.3309 11.408C11.9789 11.5894 11.6909 11.8614 11.4669 12.224L9.11494 10.912C9.53094 10.208 10.1336 9.6427 10.9229 9.21603C11.7123 8.78936 12.6563 8.57603 13.7549 8.57603C14.6189 8.57603 15.3763 8.7147 16.0269 8.99203C16.6776 9.26936 17.1843 9.66403 17.5469 10.176C17.9203 10.688 18.1069 11.2907 18.1069 11.984C18.1069 12.3467 18.0589 12.7094 17.9629 13.072C17.8669 13.424 17.6803 13.8027 17.4029 14.208C17.1363 14.6027 16.7363 15.0454 16.2029 15.536L12.8749 18.608L12.3469 17.488H18.4109V20H9.69094Z" />
        </Mask>
        <Path
          d="M9.69094 20V18.016L13.8829 14.096C14.1816 13.8187 14.4003 13.5734 14.5389 13.36C14.6883 13.1467 14.7843 12.9547 14.8269 12.784C14.8803 12.6027 14.9069 12.4374 14.9069 12.288C14.9069 11.9254 14.7843 11.6427 14.5389 11.44C14.3043 11.2374 13.9469 11.136 13.4669 11.136C13.0616 11.136 12.6829 11.2267 12.3309 11.408C11.9789 11.5894 11.6909 11.8614 11.4669 12.224L9.11494 10.912C9.53094 10.208 10.1336 9.6427 10.9229 9.21603C11.7123 8.78936 12.6563 8.57603 13.7549 8.57603C14.6189 8.57603 15.3763 8.7147 16.0269 8.99203C16.6776 9.26936 17.1843 9.66403 17.5469 10.176C17.9203 10.688 18.1069 11.2907 18.1069 11.984C18.1069 12.3467 18.0589 12.7094 17.9629 13.072C17.8669 13.424 17.6803 13.8027 17.4029 14.208C17.1363 14.6027 16.7363 15.0454 16.2029 15.536L12.8749 18.608L12.3469 17.488H18.4109V20H9.69094Z"
          fill="white"
        />
        <Path
          d="M9.69094 20H9.19094V20.5H9.69094V20ZM9.69094 18.016L9.34943 17.6508L9.19094 17.799V18.016H9.69094ZM13.8829 14.096L13.5427 13.7296L13.5414 13.7308L13.8829 14.096ZM14.5389 13.36L14.1293 13.0733L14.1244 13.0803L14.1197 13.0875L14.5389 13.36ZM14.8269 12.784L14.3473 12.6429L14.3444 12.6528L14.3419 12.6628L14.8269 12.784ZM14.5389 11.44L14.212 11.8185L14.2205 11.8255L14.5389 11.44ZM12.3309 11.408L12.5599 11.8525L12.5599 11.8525L12.3309 11.408ZM11.4669 12.224L11.2234 12.6607L11.641 12.8937L11.8923 12.4868L11.4669 12.224ZM9.11494 10.912L8.68447 10.6577L8.4237 11.099L8.87136 11.3487L9.11494 10.912ZM10.9229 9.21603L11.1607 9.65588L11.1607 9.65588L10.9229 9.21603ZM16.0269 8.99203L15.8309 9.45199L15.8309 9.45199L16.0269 8.99203ZM17.5469 10.176L17.1389 10.4651L17.1429 10.4706L17.5469 10.176ZM17.9629 13.072L18.4453 13.2036L18.4463 13.2L17.9629 13.072ZM17.4029 14.208L16.9903 13.9257L16.9886 13.9281L17.4029 14.208ZM16.2029 15.536L15.8644 15.1681L15.8638 15.1686L16.2029 15.536ZM12.8749 18.608L12.4227 18.8212L12.7133 19.4377L13.2141 18.9754L12.8749 18.608ZM12.3469 17.488V16.988H11.5584L11.8947 17.7012L12.3469 17.488ZM18.4109 17.488H18.9109V16.988H18.4109V17.488ZM18.4109 20V20.5H18.9109V20H18.4109ZM10.1909 20V18.016H9.19094V20H10.1909ZM10.0324 18.3812L14.2244 14.4612L13.5414 13.7308L9.34943 17.6508L10.0324 18.3812ZM14.2232 14.4624C14.5362 14.1717 14.7879 13.8945 14.9582 13.6325L14.1197 13.0875C14.0127 13.2522 13.827 13.4657 13.5427 13.7296L14.2232 14.4624ZM14.9486 13.6468C15.1177 13.4052 15.2492 13.1565 15.312 12.9053L14.3419 12.6628C14.3193 12.7529 14.2589 12.8882 14.1293 13.0733L14.9486 13.6468ZM15.3066 12.9251C15.37 12.7095 15.4069 12.4958 15.4069 12.288H14.4069C14.4069 12.3789 14.3905 12.4959 14.3473 12.6429L15.3066 12.9251ZM15.4069 12.288C15.4069 11.7996 15.2346 11.3662 14.8574 11.0545L14.2205 11.8255C14.334 11.9192 14.4069 12.0511 14.4069 12.288H15.4069ZM14.8657 11.0616C14.5019 10.7474 14.0041 10.636 13.4669 10.636V11.636C13.8898 11.636 14.1066 11.7273 14.2121 11.8184L14.8657 11.0616ZM13.4669 10.636C12.9839 10.636 12.5265 10.7448 12.102 10.9635L12.5599 11.8525C12.8394 11.7085 13.1393 11.636 13.4669 11.636V10.636ZM12.102 10.9635C11.663 11.1897 11.3095 11.5275 11.0415 11.9613L11.8923 12.4868C12.0724 12.1953 12.2949 11.9891 12.5599 11.8525L12.102 10.9635ZM11.7105 11.7874L9.35852 10.4754L8.87136 11.3487L11.2234 12.6607L11.7105 11.7874ZM9.5454 11.1664C9.91078 10.5481 10.4432 10.0437 11.1607 9.65588L10.6852 8.77618C9.82398 9.24169 9.1511 9.86799 8.68447 10.6577L9.5454 11.1664ZM11.1607 9.65588C11.8623 9.27662 12.7209 9.07603 13.7549 9.07603V8.07603C12.5917 8.07603 11.5622 8.30211 10.6852 8.77618L11.1607 9.65588ZM13.7549 9.07603C14.5661 9.07603 15.2543 9.20624 15.8309 9.45199L16.223 8.53207C15.4982 8.22315 14.6718 8.07603 13.7549 8.07603V9.07603ZM15.8309 9.45199C16.4076 9.69779 16.8363 10.0378 17.1389 10.465L17.955 9.88702C17.5322 9.29024 16.9476 8.84094 16.223 8.53207L15.8309 9.45199ZM17.1429 10.4706C17.448 10.8889 17.6069 11.3861 17.6069 11.984H18.6069C18.6069 11.1953 18.3926 10.4871 17.9509 9.88144L17.1429 10.4706ZM17.6069 11.984C17.6069 12.302 17.5649 12.6217 17.4796 12.9441L18.4463 13.2C18.553 12.797 18.6069 12.3914 18.6069 11.984H17.6069ZM17.4806 12.9405C17.4033 13.2237 17.2463 13.5515 16.9903 13.9257L17.8156 14.4904C18.1142 14.0539 18.3306 13.6244 18.4453 13.2036L17.4806 12.9405ZM16.9886 13.9281C16.7519 14.2785 16.3823 14.6916 15.8644 15.1681L16.5415 15.904C17.0902 15.3992 17.5207 14.9269 17.8172 14.488L16.9886 13.9281ZM15.8638 15.1686L12.5358 18.2406L13.2141 18.9754L16.5421 15.9034L15.8638 15.1686ZM13.3272 18.3948L12.7992 17.2748L11.8947 17.7012L12.4227 18.8212L13.3272 18.3948ZM12.3469 17.988H18.4109V16.988H12.3469V17.988ZM17.9109 17.488V20H18.9109V17.488H17.9109ZM18.4109 19.5H9.69094V20.5H18.4109V19.5Z"
          fill="black"
          mask="url(#path-4-outside-1_356_353)"
        />
      </G>
    </G>
    <Defs>
      <Filter
        id="filter0_d_356_353"
        x="8.42371"
        y="8.07602"
        width="10.4872"
        height="13.224"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <FeFlood flood-opacity="0" result="BackgroundImageFix" />
        <FeColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <FeOffset dy="0.8" />
        <FeColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"
        />
        <FeBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_356_353"
        />
        <FeBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_356_353"
          result="shape"
        />
      </Filter>
      <ClipPath id="clip0_356_353">
        <Rect x="4" y="4.00003" width="20" height="20" rx="2" fill="white" />
      </ClipPath>
    </Defs>
  </Svg>
);

export default React.memo(SecondRankSvg);
