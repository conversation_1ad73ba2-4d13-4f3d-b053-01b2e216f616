import React from 'react';
import Svg, {
  <PERSON><PERSON><PERSON>ath,
  Defs,
  FeBlend,
  FeColorMatrix,
  FeFlood,
  FeOffset,
  Filter,
  G,
  Mask,
  Path,
  Rect,
} from 'react-native-svg';

const FirstRankSvg = () => (
  <Svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Rect y="3.05176e-05" width="28" height="28" rx="4" fill="#F5C944" />
    <Rect
      x="0.5"
      y="0.500031"
      width="27"
      height="27"
      rx="3.5"
      stroke="white"
      stroke-opacity="0.5"
    />
    <G clip-path="url(#clip0_356_350)">
      <Rect x="4" y="4.00003" width="20" height="20" rx="2" fill="#F7D75E" />
      <G filter="url(#filter0_d_356_350)">
        <Mask
          id="path-4-outside-1_356_350"
          maskUnits="userSpaceOnUse"
          x="10"
          y="8.00003"
          width="7"
          height="13"
          fill="black"
        >
          <Rect fill="white" x="10" y="8.00003" width="7" height="13" />
          <Path d="M12.9338 20V9.93603L14.2938 11.248H10.8538V8.80003H16.1018V20H12.9338Z" />
        </Mask>
        <Path
          d="M12.9338 20V9.93603L14.2938 11.248H10.8538V8.80003H16.1018V20H12.9338Z"
          fill="white"
        />
        <Path
          d="M12.9338 20H12.4338V20.5H12.9338V20ZM12.9338 9.93603L13.281 9.57618L12.4338 8.75894V9.93603H12.9338ZM14.2938 11.248V11.748H15.5323L14.641 10.8882L14.2938 11.248ZM10.8538 11.248H10.3538V11.748H10.8538V11.248ZM10.8538 8.80003V8.30003H10.3538V8.80003H10.8538ZM16.1018 8.80003H16.6018V8.30003H16.1018V8.80003ZM16.1018 20V20.5H16.6018V20H16.1018ZM13.4338 20V9.93603H12.4338V20H13.4338ZM12.5867 10.2959L13.9467 11.6079L14.641 10.8882L13.281 9.57618L12.5867 10.2959ZM14.2938 10.748H10.8538V11.748H14.2938V10.748ZM11.3538 11.248V8.80003H10.3538V11.248H11.3538ZM10.8538 9.30003H16.1018V8.30003H10.8538V9.30003ZM15.6018 8.80003V20H16.6018V8.80003H15.6018ZM16.1018 19.5H12.9338V20.5H16.1018V19.5Z"
          fill="black"
          mask="url(#path-4-outside-1_356_350)"
        />
      </G>
    </G>
    <Defs>
      <Filter
        id="filter0_d_356_350"
        x="10.3538"
        y="8.30002"
        width="6.24805"
        height="13"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <FeFlood flood-opacity="0" result="BackgroundImageFix" />
        <FeColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <FeOffset dy="0.8" />
        <FeColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"
        />
        <FeBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_356_350"
        />
        <FeBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_356_350"
          result="shape"
        />
      </Filter>
      <ClipPath id="clip0_356_350">
        <Rect x="4" y="4.00003" width="20" height="20" rx="2" fill="white" />
      </ClipPath>
    </Defs>
  </Svg>
);

export default React.memo(FirstRankSvg);
