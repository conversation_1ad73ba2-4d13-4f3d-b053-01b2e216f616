import * as React from "react"
import Svg, { <PERSON>, <PERSON>, G } from "react-native-svg"

const FriendRequestSentSvg = (props) =>{
    return (
      <Svg
        width={18}
        height={18}
        viewBox="0 0 17 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <Mask
          id="a"
          style={{
            maskType: "alpha"
          }}
          maskUnits="userSpaceOnUse"
          x={0}
          y={0}
          width={18}
          height={18}
        >
          <Path fill="#D9D9D9" d="M0.5 0H16.5V16H0.5z" />
        </Mask>
        <G mask="url(#a)">
          <Path
            d="M12.2 6.117l2.35-2.367a.65.65 0 01.475-.2c.183 0 .341.067.475.2.133.133.2.292.2.475a.649.649 0 01-.2.475l-2.834 2.833a.64.64 0 01-.466.2.64.64 0 01-.467-.2l-1.417-1.416a.65.65 0 01-.2-.475c0-.184.067-.342.2-.475a.64.64 0 01.467-.2.64.64 0 01.467.2l.95.95zM6.5 8a2.568 2.568 0 01-1.883-.783 2.568 2.568 0 01-.784-1.884c0-.733.261-1.36.784-1.883A2.568 2.568 0 016.5 2.667c.733 0 1.36.26 1.883.783.522.522.784 1.15.784 1.883 0 .734-.262 1.362-.784 1.884A2.568 2.568 0 016.5 8zm-5.333 4v-.533c0-.378.097-.725.291-1.042.195-.317.453-.558.775-.725a9.898 9.898 0 012.1-.775 9.172 9.172 0 014.333 0c.712.172 1.412.43 2.1.775.323.167.581.408.775.725.195.317.292.664.292 1.042V12c0 .367-.13.68-.392.942a1.284 1.284 0 01-.941.391h-8c-.367 0-.68-.13-.942-.391A1.284 1.284 0 011.167 12zM2.5 12h8v-.533a.648.648 0 00-.333-.567 8.69 8.69 0 00-1.817-.675 7.734 7.734 0 00-3.7 0 8.69 8.69 0 00-1.817.675.648.648 0 00-.333.567V12zm4-5.333c.367 0 .68-.13.941-.392.262-.261.392-.575.392-.942 0-.366-.13-.68-.392-.941A1.284 1.284 0 006.5 4c-.367 0-.68.13-.942.392a1.284 1.284 0 00-.391.941c0 .367.13.681.391.942.261.261.575.392.942.392z"
            fill="#A9F99E"
          />
        </G>
      </Svg>
    )
  }

export default React.memo(FriendRequestSentSvg)
