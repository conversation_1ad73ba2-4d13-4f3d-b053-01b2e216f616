/* eslint-disable camelcase */
/* eslint-disable no-unused-vars */
/* eslint-disable max-classes-per-file */
/* eslint-disable import/no-unused-modules */
/* eslint-disable no-empty-function */
export function save_questions(encrypted_questions) {}

export function check_answer(question_id, answer) {}
export function draw_question(question_id, canvas, ctx) {}

export class QuestionOutput {}

export class QuestionOutputWrapper {}

export class QuestionResult {}

export class SubmissionOutput {}

function initSync(module) {}

async function init() {}

export { initSync };

export default init;
