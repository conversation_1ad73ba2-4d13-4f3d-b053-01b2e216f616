/* eslint-disable class-methods-use-this */
import _get from 'lodash/get';
import _toNumber from 'lodash/toNumber';
import { produce } from 'immer';

export interface FeedHelperInterface {
  changeFeedLikeStatus: (_feeds: any[], _feedId: string) => any[];
  deleteFeed: (_feeds: any[], _feedId: string) => any[];
  updateApplyStreakShield: (_feeds: any[], _feedId: string) => any[];
}

export class FeedHelper implements FeedHelperInterface {
  changeFeedLikeStatus = (_feeds: any[], _feedId: string): any[] =>
    _feeds.map((feed: any) => {
      if (feed._id === _feedId) {
        return {
          ...feed,
          isLiked: !feed.isLiked,
          feedData: {
            ...feed?.feedData,
            likesCount: Math.max(
              0,
              feed.isLiked
                ? Math.max(
                    _toNumber(_get(feed, ['feedData', 'likesCount'], 0)) - 1,
                    0,
                  )
                : _toNumber(_get(feed, ['feedData', 'likesCount'], 0)) + 1,
            ),
          },
        };
      }
      return feed;
    });

  deleteFeed = (_feeds: any[], _feedId: string): any[] =>
    _feeds.filter((feed: any) => feed._id !== _feedId);

  updateApplyStreakShield = produce((_feeds: any[], _feedId: string) => {
    const feed = _feeds.find((feed) => feed?.feedData?._id === _feedId);
    if (feed) {
      feed.feedData.additionalInfo.streakShieldApplied =
        !feed.feedData.additionalInfo.streakShieldApplied;
    }
  });
}
