import { gql } from '@apollo/client';
import { FEED_RESPONSE_FRAGMENT } from 'core/graphql/fragments/feed';

export const GET_USER_FEED = gql`
  query GetUserFeeds($lastId: ID, $pageSize: Int) {
    getUserFeeds(lastId: $lastId, pageSize: $pageSize) {
      ...FeedResponseFields
    }
  }
  ${FEED_RESPONSE_FRAGMENT}
`;

export const UPDATE_LIKE_STATUS = gql`
  mutation UpdateLikeStatus($feedId: ID!) {
    updateLikeStatus(feedId: $feedId)
  }
`;

export const UPDATE_LAST_READ_FEED_ID = gql`
  mutation UpdateLastReadFeedId($lastReadFeedId: ID!) {
    updateLastReadFeedId(lastReadFeedId: $lastReadFeedId)
  }
`;

export const DELETE_FEED = gql`
  mutation DeleteFeed($feedId: ID!) {
    deleteFeedById(feedId: $feedId)
  }
`;

export const UPDATE_APPLY_STREAK_SHIELD = gql`
  mutation UpdateApplyStreakShield($feedId: ID!) {
    updateApplyStreakShield(feedId: $feedId)
  }
`;
