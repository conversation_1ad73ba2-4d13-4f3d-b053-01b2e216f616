import { EventEmitter } from 'events';
import { GroupPlayEvent, GroupPlayEventHandlers } from './types';

// Event Types Constants
export const GROUP_PLAY_EVENTS = {
  // Game Lifecycle
  GAME_INITIALIZED: 'GAME_INITIALIZED',
  GAME_STARTED: 'GAME_STARTED',
  GAME_ENDED: 'GAME_ENDED',
  GAME_ABORTED: 'GAME_ABORTED',
  
  // Question Lifecycle
  QUESTION_STARTED: 'QUESTION_STARTED',
  QUESTION_ENDED: 'QUESTION_ENDED',
  QUESTION_PRELOADED: 'QUESTION_PRELOADED',
  
  // Answer Events
  ANSWER_SUBMITTED: 'ANSWER_SUBMITTED',
  ANSWER_CORRECT: 'ANSWER_CORRECT',
  ANSWER_INCORRECT: 'ANSWER_INCORRECT',
  ANSWER_TIMEOUT: 'ANSWER_TIMEOUT',
  
  // Player Events
  PLAYER_JOINED: 'PLAYER_JOINED',
  PLAYER_LEFT: 'PLAYER_LEFT',
  PLAYER_SCORE_UPDATED: 'PLAYER_SCORE_UPDATED',
  PLAYER_STATUS_CHANGED: 'PLAYER_STATUS_CHANGED',
  
  // Phase Events
  PHASE_CHANGED: 'PHASE_CHANGED',
  WAITING_PHASE_STARTED: 'WAITING_PHASE_STARTED',
  QUESTION_PHASE_STARTED: 'QUESTION_PHASE_STARTED',
  RESULTS_PHASE_STARTED: 'RESULTS_PHASE_STARTED',
  
  // Leaderboard Events
  LEADERBOARD_UPDATED: 'LEADERBOARD_UPDATED',
  LEADERBOARD_SHOWN: 'LEADERBOARD_SHOWN',
  LEADERBOARD_HIDDEN: 'LEADERBOARD_HIDDEN',
  
  // Timing Events
  TIME_SYNC: 'TIME_SYNC',
  TIMER_TICK: 'TIMER_TICK',
  CYCLE_UPDATED: 'CYCLE_UPDATED',
  
  // Error Events
  CONNECTION_ERROR: 'CONNECTION_ERROR',
  SYNC_ERROR: 'SYNC_ERROR',
  SUBMISSION_ERROR: 'SUBMISSION_ERROR',
  
  // WebSocket Events
  WS_MESSAGE_RECEIVED: 'WS_MESSAGE_RECEIVED',
  WS_RECONNECTED: 'WS_RECONNECTED',
  WS_DISCONNECTED: 'WS_DISCONNECTED',
} as const;

export type GroupPlayEventType = typeof GROUP_PLAY_EVENTS[keyof typeof GROUP_PLAY_EVENTS];

class GroupPlayEventManager {
  private eventEmitter: EventEmitter;
  private static instance: GroupPlayEventManager;
  private eventHistory: GroupPlayEvent[] = [];
  private maxHistorySize = 100;

  constructor() {
    if (GroupPlayEventManager.instance) {
      return GroupPlayEventManager.instance;
    }
    
    this.eventEmitter = new EventEmitter();
    this.eventEmitter.setMaxListeners(50); // Increase for multiple listeners
    GroupPlayEventManager.instance = this;
  }

  // Event Emission
  emit(eventType: GroupPlayEventType, data: any, gameId?: string, userId?: string): void {
    const event: GroupPlayEvent = {
      id: this.generateEventId(),
      type: eventType,
      gameId: gameId || '',
      userId,
      timestamp: Date.now(),
      data,
    };

    // Add to history
    this.addToHistory(event);

    // Emit the event
    this.eventEmitter.emit(eventType, event);
    this.eventEmitter.emit('*', event); // Global listener
  }

  // Event Listening
  on(eventType: GroupPlayEventType | '*', handler: (event: GroupPlayEvent) => void): void {
    this.eventEmitter.on(eventType, handler);
  }

  off(eventType: GroupPlayEventType | '*', handler: (event: GroupPlayEvent) => void): void {
    this.eventEmitter.off(eventType, handler);
  }

  once(eventType: GroupPlayEventType, handler: (event: GroupPlayEvent) => void): void {
    this.eventEmitter.once(eventType, handler);
  }

  // Batch Event Processing
  emitBatch(events: Array<{ type: GroupPlayEventType; data: any; gameId?: string; userId?: string }>): void {
    events.forEach(({ type, data, gameId, userId }) => {
      this.emit(type, data, gameId, userId);
    });
  }

  // Event History Management
  private addToHistory(event: GroupPlayEvent): void {
    this.eventHistory.push(event);
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }
  }

  getEventHistory(gameId?: string, eventType?: GroupPlayEventType): GroupPlayEvent[] {
    let filtered = this.eventHistory;
    
    if (gameId) {
      filtered = filtered.filter(event => event.gameId === gameId);
    }
    
    if (eventType) {
      filtered = filtered.filter(event => event.type === eventType);
    }
    
    return filtered;
  }

  clearHistory(gameId?: string): void {
    if (gameId) {
      this.eventHistory = this.eventHistory.filter(event => event.gameId !== gameId);
    } else {
      this.eventHistory = [];
    }
  }

  // Utility Methods
  private generateEventId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Remove all listeners for cleanup
  removeAllListeners(eventType?: GroupPlayEventType): void {
    if (eventType) {
      this.eventEmitter.removeAllListeners(eventType);
    } else {
      this.eventEmitter.removeAllListeners();
    }
  }

  // Get listener count for debugging
  getListenerCount(eventType: GroupPlayEventType): number {
    return this.eventEmitter.listenerCount(eventType);
  }

  // Event Debugging
  enableDebugMode(): void {
    this.on('*', (event) => {
      console.log(`[GroupPlay Event] ${event.type}:`, event);
    });
  }

  disableDebugMode(): void {
    this.removeAllListeners('*');
  }
}

// Singleton instance
export const groupPlayEventManager = new GroupPlayEventManager();

// Convenience functions for common event patterns
export const emitQuestionEvent = (
  type: 'STARTED' | 'ENDED' | 'PRELOADED',
  questionId: string,
  gameId: string,
  additionalData?: any
) => {
  const eventType = `QUESTION_${type}` as GroupPlayEventType;
  groupPlayEventManager.emit(eventType, {
    questionId,
    ...additionalData,
  }, gameId);
};

export const emitPlayerEvent = (
  type: 'JOINED' | 'LEFT' | 'SCORE_UPDATED' | 'STATUS_CHANGED',
  userId: string,
  gameId: string,
  additionalData?: any
) => {
  const eventType = `PLAYER_${type}` as GroupPlayEventType;
  groupPlayEventManager.emit(eventType, {
    userId,
    ...additionalData,
  }, gameId, userId);
};

export const emitPhaseEvent = (
  type: 'CHANGED' | 'WAITING_PHASE_STARTED' | 'QUESTION_PHASE_STARTED' | 'RESULTS_PHASE_STARTED',
  gameId: string,
  phaseData: any
) => {
  const eventType = type === 'CHANGED' ? 'PHASE_CHANGED' : type as GroupPlayEventType;
  groupPlayEventManager.emit(eventType, phaseData, gameId);
};

export const emitAnswerEvent = (
  type: 'SUBMITTED' | 'CORRECT' | 'INCORRECT' | 'TIMEOUT',
  questionId: string,
  gameId: string,
  userId: string,
  answerData?: any
) => {
  const eventType = `ANSWER_${type}` as GroupPlayEventType;
  groupPlayEventManager.emit(eventType, {
    questionId,
    ...answerData,
  }, gameId, userId);
};

// Event Handler Registration Helper
export const registerGroupPlayEventHandlers = (
  gameId: string,
  handlers: Partial<GroupPlayEventHandlers>
): (() => void) => {
  const unsubscribeFunctions: (() => void)[] = [];

  Object.entries(handlers).forEach(([eventKey, handler]) => {
    if (typeof handler === 'function') {
      const eventType = eventKey.replace(/^on/, '').replace(/([A-Z])/g, '_$1').toUpperCase().slice(1) as GroupPlayEventType;
      
      const wrappedHandler = (event: GroupPlayEvent) => {
        if (event.gameId === gameId) {
          handler(event);
        }
      };

      groupPlayEventManager.on(eventType, wrappedHandler);
      unsubscribeFunctions.push(() => groupPlayEventManager.off(eventType, wrappedHandler));
    }
  });

  // Return cleanup function
  return () => {
    unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
  };
};

export default GroupPlayEventManager;
