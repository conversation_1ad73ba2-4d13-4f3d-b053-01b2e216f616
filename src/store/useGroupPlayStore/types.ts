/* eslint-disable no-undef */
export interface GroupPlayQuestion {
  id: string;
  question: any;
  answers: string[];
  maxTimeLimit: number;
  hasSolved: boolean;
  incorrectAttempts: number;
  submittedAnswer?: string;
  submissionTime?: number;
  isCorrect?: boolean;
}

export interface GroupPlayPlayer {
  userId: string;
  userName: string;
  score: number;
  correctAnswers: number;
  incorrectAnswers: number;
  isOnline: boolean;
  lastActivity: number;
}

export interface GroupPlayGamePhase {
  type: 'INITIAL_WAITING' | 'QUESTION' | 'WAITING' | 'RESULTS' | 'ENDED';
  startTime: number;
  endTime: number;
  duration: number;
}

export interface GroupPlayGameConfig {
  timeLimit: number;
  maxTimePerQuestion: number;
  waitingTime: number;
  numPlayers: number;
  gameType: string;
  gameMode: string;
}

export interface GroupPlayGameState {
  // Game Core Data
  gameId: string;
  gameStatus: string;
  startTime: number;
  endTime?: number;
  config: GroupPlayGameConfig;
  
  // Questions Management
  questions: Record<string, GroupPlayQuestion>;
  allQuestions: any[];
  currentQuestionId: string | null;
  currentQuestionIndex: number;
  totalQuestions: number;
  questionStartTime: number;
  
  // Players Management
  players: Record<string, GroupPlayPlayer>;
  currentUserId: string;
  leaderboard: GroupPlayPlayer[];
  
  // Game Phase Management
  currentPhase: GroupPlayGamePhase;
  isGameReady: boolean;
  isGameActive: boolean;
  
  // Timing & Synchronization
  serverTimeOffset: number;
  lastSyncTime: number;
  cycleStartTime: number;
  
  // UI State
  loading: boolean;
  error: string | null;
  isSubmitting: boolean;
  showLeaderboard: boolean;
  
  // Performance Optimizations
  preloadedQuestions: Record<string, any>;
  optimisticUpdates: Record<string, any>;
  
  // Event Management
  eventQueue: any[];
  lastEventId: string | null;
}

export interface GroupPlayActions {
  // Game Initialization
  initializeGame: (gameData: any) => void;
  resetGame: () => void;
  
  // Question Management
  loadQuestion: (questionId: string) => void;
  preloadNextQuestion: () => void;
  updateCurrentQuestion: () => void;
  
  // Answer Submission
  submitAnswer: (answer: string) => Promise<boolean>;
  handleAnswerResult: (result: any) => void;
  
  // Player Management
  updatePlayer: (userId: string, updates: Partial<GroupPlayPlayer>) => void;
  updateLeaderboard: (leaderboard: GroupPlayPlayer[]) => void;
  
  // Phase Management
  updateGamePhase: (phase: Partial<GroupPlayGamePhase>) => void;
  startQuestionPhase: () => void;
  startWaitingPhase: () => void;
  
  // Timing & Synchronization
  syncServerTime: (serverTime: number) => void;
  updateCycleTime: () => void;
  
  // Event Handling
  processEvent: (event: any) => void;
  addToEventQueue: (event: any) => void;
  processEventQueue: () => void;
  
  // Optimistic Updates
  addOptimisticUpdate: (key: string, data: any) => void;
  removeOptimisticUpdate: (key: string) => void;
  rollbackOptimisticUpdate: (key: string) => void;
  
  // Utility
  clearStore: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export type GroupPlayStore = GroupPlayGameState & GroupPlayActions;

export type GetGroupPlayState = () => GroupPlayStore;
export type SetGroupPlayState = (
  update: GroupPlayStore | ((state: GroupPlayStore) => void)
) => void;

// Event Types
export interface GroupPlayEvent {
  id: string;
  type: string;
  gameId: string;
  userId?: string;
  timestamp: number;
  data: any;
}

export interface GroupPlayEventHandlers {
  onQuestionStart: (event: GroupPlayEvent) => void;
  onQuestionEnd: (event: GroupPlayEvent) => void;
  onAnswerSubmitted: (event: GroupPlayEvent) => void;
  onPlayerJoined: (event: GroupPlayEvent) => void;
  onPlayerLeft: (event: GroupPlayEvent) => void;
  onGamePhaseChanged: (event: GroupPlayEvent) => void;
  onLeaderboardUpdated: (event: GroupPlayEvent) => void;
  onGameEnded: (event: GroupPlayEvent) => void;
}

// Service Interfaces
export interface GroupPlayGameServiceInterface {
  initializeGame: (gameData: any) => GroupPlayGameState;
  calculateCurrentPhase: (gameState: GroupPlayGameState) => GroupPlayGamePhase;
  calculateCurrentQuestionIndex: (gameState: GroupPlayGameState) => number;
  isGameActive: (gameState: GroupPlayGameState) => boolean;
}

export interface GroupPlayQuestionServiceInterface {
  loadQuestion: (questionId: string, gameState: GroupPlayGameState) => GroupPlayQuestion | null;
  preloadQuestion: (questionIndex: number, gameState: GroupPlayGameState) => void;
  validateAnswer: (answer: string, question: GroupPlayQuestion) => boolean;
  calculateScore: (question: GroupPlayQuestion, timeTaken: number) => number;
}

export interface GroupPlayTimingServiceInterface {
  getCurrentServerTime: () => number;
  calculateTimeRemaining: (gameState: GroupPlayGameState) => number;
  calculateQuestionTimeRemaining: (gameState: GroupPlayGameState) => number;
  syncWithServer: (serverTime: number) => void;
}

export interface GroupPlayEventServiceInterface {
  emit: (eventType: string, data: any) => void;
  on: (eventType: string, handler: (data: any) => void) => void;
  off: (eventType: string, handler: (data: any) => void) => void;
  processWebSocketEvent: (event: any) => void;
}
