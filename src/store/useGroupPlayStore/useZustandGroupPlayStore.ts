/* eslint-disable no-param-reassign */
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { clearStore } from '../helpers';
import { GroupPlayStore } from './types';
import GroupPlayHandlers from './handlers';

const defaultConfig = {
  // Game Core Data
  gameId: '',
  gameStatus: '',
  startTime: 0,
  endTime: undefined,
  config: {
    timeLimit: 300,
    maxTimePerQuestion: 10,
    waitingTime: 5,
    numPlayers: 4,
    gameType: '',
    gameMode: '',
  },

  // Questions Management
  questions: {},
  allQuestions: [],
  currentQuestionId: null,
  currentQuestionIndex: 0,
  totalQuestions: 0,
  questionStartTime: 0,

  // Players Management
  players: {},
  currentUserId: '',
  leaderboard: [],

  // Game Phase Management
  currentPhase: {
    type: 'INITIAL_WAITING' as const,
    startTime: 0,
    endTime: 0,
    duration: 0,
  },
  isGameReady: false,
  isGameActive: false,

  // Timing & Synchronization
  serverTimeOffset: 0,
  lastSyncTime: 0,
  cycleStartTime: 0,

  // UI State
  loading: false,
  error: null,
  isSubmitting: false,
  showLeaderboard: false,

  // Performance Optimizations
  preloadedQuestions: {},
  optimisticUpdates: {},

  // Event Management
  eventQueue: [],
  lastEventId: null,
};

const useGroupPlayZustandStore = create<GroupPlayStore>()(
  immer((set, get) => {
    const handlers = new GroupPlayHandlers(set, get);
    
    return {
      ...defaultConfig,
      
      // Game Initialization
      initializeGame: handlers.initializeGame,
      resetGame: handlers.resetGame,
      
      // Question Management
      loadQuestion: handlers.loadQuestion,
      preloadNextQuestion: handlers.preloadNextQuestion,
      updateCurrentQuestion: handlers.updateCurrentQuestion,
      
      // Answer Submission
      submitAnswer: handlers.submitAnswer,
      handleAnswerResult: handlers.handleAnswerResult,
      
      // Player Management
      updatePlayer: handlers.updatePlayer,
      updateLeaderboard: handlers.updateLeaderboard,
      
      // Phase Management
      updateGamePhase: handlers.updateGamePhase,
      startQuestionPhase: handlers.startQuestionPhase,
      startWaitingPhase: handlers.startWaitingPhase,
      
      // Timing & Synchronization
      syncServerTime: handlers.syncServerTime,
      updateCycleTime: handlers.updateCycleTime,
      
      // Event Handling
      processEvent: handlers.processEvent,
      addToEventQueue: handlers.addToEventQueue,
      processEventQueue: handlers.processEventQueue,
      
      // Optimistic Updates
      addOptimisticUpdate: handlers.addOptimisticUpdate,
      removeOptimisticUpdate: handlers.removeOptimisticUpdate,
      rollbackOptimisticUpdate: handlers.rollbackOptimisticUpdate,
      
      // Utility
      setLoading: handlers.setLoading,
      setError: handlers.setError,
      
      // Clear store
      clearStore: () => {
        handlers.resetGame();
        clearStore(set, defaultConfig);
      },
    };
  }),
);

export default useGroupPlayZustandStore;
