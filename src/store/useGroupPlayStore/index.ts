import { useShallow } from 'zustand/shallow';
import { GroupPlayStore } from './types';
import useGroupPlayZustandStore from './useZustandGroupPlayStore';

type GroupPlayStoreSelector = (state: GroupPlayStore) => any;

const useGroupPlayStore = (selector: GroupPlayStoreSelector) =>
  useGroupPlayZustandStore(useShallow(selector));

export default useGroupPlayStore;

// Export types and utilities
export * from './types';
export * from './events';
export { default as GroupPlayGameService } from '@/src/modules/game/services/GroupPlayGameService';
export { default as GroupPlayQuestionService } from '@/src/modules/game/services/GroupPlayQuestionService';
export { default as GroupPlayTimingService } from '@/src/modules/game/services/GroupPlayTimingService';
export { default as GroupPlayEventService } from '@/src/modules/game/services/GroupPlayEventService';
