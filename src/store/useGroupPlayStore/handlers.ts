/* eslint-disable no-param-reassign */
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _cloneDeep from 'lodash/cloneDeep';
import { handleAsync } from 'core/utils/asyncUtils';
import { showToast, hideToast, TOAST_TYPE } from '@/src/components/molecules/Toast';
import {
  GroupPlayStore,
  GroupPlayGameState,
  GroupPlayQuestion,
  GroupPlayPlayer,
  GroupPlayGamePhase,
  GetGroupPlayState,
  SetGroupPlayState,
} from './types';
import GroupPlayGameService from '@/src/modules/game/services/GroupPlayGameService';
import GroupPlayQuestionService from '@/src/modules/game/services/GroupPlayQuestionService';
import GroupPlayTimingService from '@/src/modules/game/services/GroupPlayTimingService';
import GroupPlayEventService from '@/src/modules/game/services/GroupPlayEventService';
import { groupPlayEventManager, GROUP_PLAY_EVENTS } from './events';

const EMPTY_OBJECT = {};

interface GroupPlayHandlersInterface {
  // Game Initialization
  initializeGame: (gameData: any) => void;
  resetGame: () => void;
  
  // Question Management
  loadQuestion: (questionId: string) => void;
  preloadNextQuestion: () => void;
  updateCurrentQuestion: () => void;
  
  // Answer Submission
  submitAnswer: (answer: string) => Promise<boolean>;
  handleAnswerResult: (result: any) => void;
  
  // Player Management
  updatePlayer: (userId: string, updates: Partial<GroupPlayPlayer>) => void;
  updateLeaderboard: (leaderboard: GroupPlayPlayer[]) => void;
  
  // Phase Management
  updateGamePhase: (phase: Partial<GroupPlayGamePhase>) => void;
  startQuestionPhase: () => void;
  startWaitingPhase: () => void;
  
  // Timing & Synchronization
  syncServerTime: (serverTime: number) => void;
  updateCycleTime: () => void;
  
  // Event Handling
  processEvent: (event: any) => void;
  addToEventQueue: (event: any) => void;
  processEventQueue: () => void;
  
  // Optimistic Updates
  addOptimisticUpdate: (key: string, data: any) => void;
  removeOptimisticUpdate: (key: string) => void;
  rollbackOptimisticUpdate: (key: string) => void;
  
  // Utility
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export default class GroupPlayHandlers implements GroupPlayHandlersInterface {
  private setState: SetGroupPlayState;
  private getState: GetGroupPlayState;
  private gameService: GroupPlayGameService;
  private questionService: GroupPlayQuestionService;
  private timingService: GroupPlayTimingService;
  private eventService: GroupPlayEventService;

  constructor(setState: SetGroupPlayState, getState: GetGroupPlayState) {
    this.setState = setState;
    this.getState = getState;
    this.gameService = new GroupPlayGameService();
    this.questionService = new GroupPlayQuestionService();
    this.timingService = new GroupPlayTimingService();
    this.eventService = new GroupPlayEventService();
    
    this.setupEventListeners();
  }

  // Game Initialization
  initializeGame = (gameData: any): void => {
    try {
      this.setLoading(true);
      
      const initialState = this.gameService.initializeGame(gameData);
      
      this.setState((state) => {
        Object.assign(state, initialState);
        state.loading = false;
        state.error = null;
      });

      // Set up timing synchronization
      this.timingService.syncWithServer(Date.now());
      
      // Preload upcoming questions
      this.questionService.preloadUpcomingQuestions(this.getState(), 3);
      
      // Set up phase transitions
      this.schedulePhaseTransitions();
      
    } catch (error) {
      console.error('Error initializing game:', error);
      this.setError(error.message);
      this.setLoading(false);
    }
  };

  resetGame = (): void => {
    this.setState((state) => {
      state.gameId = '';
      state.gameStatus = '';
      state.startTime = 0;
      state.endTime = undefined;
      state.questions = {};
      state.allQuestions = [];
      state.currentQuestionId = null;
      state.currentQuestionIndex = 0;
      state.totalQuestions = 0;
      state.questionStartTime = 0;
      state.players = {};
      state.currentUserId = '';
      state.leaderboard = [];
      state.isGameReady = false;
      state.isGameActive = false;
      state.serverTimeOffset = 0;
      state.lastSyncTime = 0;
      state.cycleStartTime = 0;
      state.loading = false;
      state.error = null;
      state.isSubmitting = false;
      state.showLeaderboard = false;
      state.preloadedQuestions = {};
      state.optimisticUpdates = {};
      state.eventQueue = [];
      state.lastEventId = null;
    });
    
    // Cleanup services
    this.timingService.clearAllTimers();
    this.questionService.clearPreloadCache();
    this.eventService.clearEventQueue();
  };

  // Question Management
  loadQuestion = (questionId: string): void => {
    const state = this.getState();
    const question = this.questionService.loadQuestion(questionId, state);
    
    if (question) {
      this.setState((state) => {
        state.questions[questionId] = question;
      });
    }
  };

  preloadNextQuestion = (): void => {
    const state = this.getState();
    this.questionService.preloadNextQuestion(state);
  };

  updateCurrentQuestion = (): void => {
    const state = this.getState();
    const updatedState = this.gameService.updateCurrentQuestion(state);
    
    if (updatedState !== state) {
      this.setState((state) => {
        state.currentQuestionId = updatedState.currentQuestionId;
        state.currentQuestionIndex = updatedState.currentQuestionIndex;
        state.questionStartTime = updatedState.questionStartTime;
      });
      
      // Preload next question
      this.preloadNextQuestion();
    }
  };

  // Answer Submission
  submitAnswer = async (answer: string): Promise<boolean> => {
    const state = this.getState();
    
    if (!state.currentQuestionId || state.isSubmitting) {
      return false;
    }

    try {
      this.setState((state) => {
        state.isSubmitting = true;
      });

      // Add optimistic update
      this.addOptimisticUpdate(`answer-${state.currentQuestionId}`, {
        questionId: state.currentQuestionId,
        answer,
        submissionTime: Date.now(),
        isOptimistic: true,
      });

      // Process answer locally first
      const result = this.questionService.processAnswerSubmission(
        state.currentQuestionId,
        answer,
        state,
        state.currentUserId
      );

      // Update local state optimistically
      this.setState((state) => {
        state.questions[state.currentQuestionId] = result.updatedQuestion;
        if (result.isCorrect) {
          const player = state.players[state.currentUserId];
          if (player) {
            player.score += result.score;
            player.correctAnswers += 1;
          }
        }
      });

      // TODO: Send to server via WebSocket
      // This would be handled by the WebSocket service
      
      return result.isCorrect;
      
    } catch (error) {
      console.error('Error submitting answer:', error);
      this.rollbackOptimisticUpdate(`answer-${state.currentQuestionId}`);
      this.setError('Failed to submit answer');
      return false;
    } finally {
      this.setState((state) => {
        state.isSubmitting = false;
      });
    }
  };

  handleAnswerResult = (result: any): void => {
    const { questionId, userId, isCorrect, score, submissionTime } = result;
    
    this.setState((state) => {
      // Update question
      if (state.questions[questionId]) {
        state.questions[questionId] = {
          ...state.questions[questionId],
          submittedAnswer: result.answer,
          submissionTime,
          isCorrect,
          hasSolved: isCorrect,
        };
      }
      
      // Update player
      if (state.players[userId]) {
        if (isCorrect) {
          state.players[userId].score += score;
          state.players[userId].correctAnswers += 1;
        } else {
          state.players[userId].incorrectAnswers += 1;
        }
        state.players[userId].lastActivity = Date.now();
      }
    });

    // Remove optimistic update
    this.removeOptimisticUpdate(`answer-${questionId}`);
  };

  // Player Management
  updatePlayer = (userId: string, updates: Partial<GroupPlayPlayer>): void => {
    this.setState((state) => {
      if (state.players[userId]) {
        Object.assign(state.players[userId], updates);
      }
    });
  };

  updateLeaderboard = (leaderboard: GroupPlayPlayer[]): void => {
    this.setState((state) => {
      state.leaderboard = leaderboard;
      
      // Update individual player scores
      leaderboard.forEach((player) => {
        if (state.players[player.userId]) {
          state.players[player.userId].score = player.score;
        }
      });
    });
  };

  // Phase Management
  updateGamePhase = (phase: Partial<GroupPlayGamePhase>): void => {
    this.setState((state) => {
      state.currentPhase = { ...state.currentPhase, ...phase };
    });
  };

  startQuestionPhase = (): void => {
    const state = this.getState();
    
    this.setState((state) => {
      state.currentPhase = {
        type: 'QUESTION',
        startTime: this.timingService.getCurrentServerTime(),
        endTime: this.timingService.getCurrentServerTime() + (state.config.maxTimePerQuestion * 1000),
        duration: state.config.maxTimePerQuestion * 1000,
      };
    });

    // Update current question
    this.updateCurrentQuestion();
    
    // Schedule question timeout
    this.timingService.scheduleQuestionTimeout(this.getState(), () => {
      this.startWaitingPhase();
    });
  };

  startWaitingPhase = (): void => {
    const state = this.getState();
    
    this.setState((state) => {
      state.currentPhase = {
        type: 'WAITING',
        startTime: this.timingService.getCurrentServerTime(),
        endTime: this.timingService.getCurrentServerTime() + (state.config.waitingTime * 1000),
        duration: state.config.waitingTime * 1000,
      };
      state.showLeaderboard = true;
    });

    // Schedule next question phase
    this.timingService.createTimer(
      `waiting-phase-${state.gameId}`,
      state.config.waitingTime * 1000,
      () => {
        this.setState((state) => {
          state.showLeaderboard = false;
        });
        this.startQuestionPhase();
      },
      state.gameId
    );
  };

  // Timing & Synchronization
  syncServerTime = (serverTime: number): void => {
    this.timingService.syncWithServer(serverTime);
    
    this.setState((state) => {
      state.serverTimeOffset = this.timingService.getCurrentServerTime() - Date.now();
      state.lastSyncTime = Date.now();
    });
  };

  updateCycleTime = (): void => {
    const state = this.getState();
    const updatedState = this.gameService.updateGamePhase(state);
    
    if (updatedState !== state) {
      this.setState((state) => {
        state.currentPhase = updatedState.currentPhase;
      });
    }
  };

  // Event Handling
  processEvent = (event: any): void => {
    this.eventService.processWebSocketEvent(event);
  };

  addToEventQueue = (event: any): void => {
    this.setState((state) => {
      state.eventQueue.push(event);
    });
  };

  processEventQueue = (): void => {
    const state = this.getState();
    
    if (state.eventQueue.length > 0) {
      const events = [...state.eventQueue];
      
      this.setState((state) => {
        state.eventQueue = [];
      });
      
      events.forEach((event) => {
        this.processEvent(event);
      });
    }
  };

  // Optimistic Updates
  addOptimisticUpdate = (key: string, data: any): void => {
    this.setState((state) => {
      state.optimisticUpdates[key] = {
        ...data,
        timestamp: Date.now(),
      };
    });
  };

  removeOptimisticUpdate = (key: string): void => {
    this.setState((state) => {
      delete state.optimisticUpdates[key];
    });
  };

  rollbackOptimisticUpdate = (key: string): void => {
    const state = this.getState();
    const update = state.optimisticUpdates[key];
    
    if (update && update.questionId) {
      // Rollback question state
      this.setState((state) => {
        const question = state.questions[update.questionId];
        if (question) {
          question.submittedAnswer = undefined;
          question.submissionTime = undefined;
          question.isCorrect = undefined;
          question.hasSolved = false;
        }
        
        // Rollback player score if needed
        if (update.isCorrect && state.players[state.currentUserId]) {
          state.players[state.currentUserId].score -= update.score || 0;
          state.players[state.currentUserId].correctAnswers -= 1;
        }
      });
    }
    
    this.removeOptimisticUpdate(key);
  };

  // Utility
  setLoading = (loading: boolean): void => {
    this.setState((state) => {
      state.loading = loading;
    });
  };

  setError = (error: string | null): void => {
    this.setState((state) => {
      state.error = error;
    });
    
    if (error) {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: error,
      });
    }
  };

  // Private Methods
  private setupEventListeners(): void {
    // Listen to game events and update state accordingly
    groupPlayEventManager.on(GROUP_PLAY_EVENTS.ANSWER_CORRECT, (event) => {
      this.handleAnswerResult({
        ...event.data,
        isCorrect: true,
      });
    });

    groupPlayEventManager.on(GROUP_PLAY_EVENTS.ANSWER_INCORRECT, (event) => {
      this.handleAnswerResult({
        ...event.data,
        isCorrect: false,
      });
    });

    groupPlayEventManager.on(GROUP_PLAY_EVENTS.LEADERBOARD_UPDATED, (event) => {
      this.updateLeaderboard(event.data.leaderboard);
    });

    groupPlayEventManager.on(GROUP_PLAY_EVENTS.TIME_SYNC, (event) => {
      this.syncServerTime(event.data.serverTime);
    });
  }

  private schedulePhaseTransitions(): void {
    const state = this.getState();
    
    // Schedule initial game start if not started
    if (!state.isGameReady) {
      const timeUntilStart = state.startTime - this.timingService.getCurrentServerTime();
      
      if (timeUntilStart > 0) {
        this.timingService.createTimer(
          `game-start-${state.gameId}`,
          timeUntilStart,
          () => {
            this.setState((state) => {
              state.isGameReady = true;
            });
            this.startQuestionPhase();
          },
          state.gameId
        );
      } else {
        this.setState((state) => {
          state.isGameReady = true;
        });
        this.startQuestionPhase();
      }
    }
  }
}
