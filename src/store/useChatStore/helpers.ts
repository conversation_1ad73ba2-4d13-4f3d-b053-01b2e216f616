/* eslint-disable no-param-reassign */
import { Group } from '@/src/modules/chat/types/groups';
import _isArray from 'lodash/isArray';
import _get from 'lodash/get';
import { ChatState, MessageGroup } from './types';

export const getMessageGroupsList = (newGroups: Record<string, Group>) => {
  const messageGroups = Object.values(newGroups);
  const messageGroupsShown = messageGroups.sort(
    (a: Group, b: Group) =>
      new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
  );
  return messageGroupsShown;
};

export const updateLastMessageReadState = (
  state: ChatState,
  groupId: string,
  userId: string,
  latestMessageChecked: string,
) => {
  const group = state.messageGroups[groupId] ?? EMPTY_OBJECT;
  let lastMessageRead = _get(group, 'lastMessageRead', []);
  if (!_isArray(lastMessageRead)) {
    lastMessageRead = [];
  }
  const lastMessageReadInfo = lastMessageRead.find(
    (user: any) => user.userId === userId,
  );

  if (!lastMessageReadInfo) {
    lastMessageRead.push({
      userId,
      lastMessageRead: latestMessageChecked,
    });
    state.messageGroups[groupId] = group;
    return;
  }
  group.lastMessageRead = lastMessageRead.map((lastMessageReadInfo: any) => {
    if (lastMessageReadInfo.userId === userId) {
      return {
        ...lastMessageReadInfo,
        lastMessageRead: latestMessageChecked,
      };
    }
    return lastMessageReadInfo;
  });
  const newGroups = { ...state.messageGroups };
  state.messageGroups[groupId] = group;
  newGroups[groupId] = group;
  state.messageGroups = newGroups;

  const messageGroupsShown = getMessageGroupsList(newGroups);
  messageGroupsShown.forEach((group: MessageGroup) => {
    let isRead = true;
    if (_isArray(group.lastMessageRead)) {
      group.lastMessageRead.forEach((lastMessageRead: any) => {
        if (
          lastMessageRead?.userId === userId &&
          lastMessageRead?.lastMessageRead !== group?.lastMessage?._id
        ) {
          isRead = false;
        }
      });
    }
    state.isRead = isRead;
  });
  state.messageGroupsShown = messageGroupsShown;
  state.messagesError[groupId] = null;
};
