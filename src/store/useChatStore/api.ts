/* eslint-disable class-methods-use-this */
import { ApolloC<PERSON>, ApolloQueryResult, FetchResult } from '@apollo/client';
import { SORT_DIRECTION } from 'core/constants/chatConstants';
import {
  GET_MESSAGES,
  GET_MESSAGE_GROUP,
  UPDATE_LAST_MESSAGE_READ,
} from './graphql';
import { MessageGroupId } from './types';

export interface ChatApiInterface {
  fetchMessages: (
    _groupId: MessageGroupId,
    _lastMessageId: string | null,
  ) => Promise<ApolloQueryResult<any>>;
  fetchMessageGroups: (_page: number) => Promise<ApolloQueryResult<any>>;
  updateLastMessageRead: (
    _groupId: MessageGroupId,
    _lastMessageId: string,
  ) => Promise<FetchResult<any>>;
}

const PAGE_SIZE = 50;

export default class ChatApi implements ChatApiInterface {
  fetchMessages = async (
    groupId: MessageGroupId,
    lastMessageId: string | null,
  ) => {
    const appoloClient: ApolloClient<object> = getApolloClient();
    const resp = await appoloClient.query({
      query: GET_MESSAGES,
      fetchPolicy: 'network-only',
      variables: {
        groupId,
        pageSize: PAGE_SIZE,
        lastMessageId,
        sortDirection: SORT_DIRECTION.DESC,
      },
    });
    return resp;
  };

  fetchMessageGroups = async (page: number) => {
    const appoloClient: ApolloClient<object> = getApolloClient();
    const resp = await appoloClient.query({
      query: GET_MESSAGE_GROUP,
      fetchPolicy: 'network-only',
      variables: {
        input: {
          page,
          pageSize: PAGE_SIZE,
        },
      },
    });
    return resp;
  };

  updateLastMessageRead = async (
    groupId: MessageGroupId,
    lastMessageId: string,
  ) => {
    const appoloClient: ApolloClient<object> = getApolloClient();
    const resp = await appoloClient.mutate({
      mutation: UPDATE_LAST_MESSAGE_READ,
      variables: {
        groupId,
        lastMessageReadId: lastMessageId,
      },
    });
    return resp;
  };
}
