/* eslint-disable class-methods-use-this */
/* eslint-disable no-param-reassign */
/* eslint-disable import/prefer-default-export */
import { handleAsync } from 'core/utils/asyncUtils';
import _get from 'lodash/get';
import _isArray from 'lodash/isArray';
import _filter from 'lodash/filter';
import { Group } from 'modules/chatV2/types/groups';
import groupReader from 'modules/chatV2/readers/groupReader';
import { WEBSOCKET_CHANNELS } from 'core/constants/websocket';
import _size from 'lodash/size';
import {
  ChatState,
  GetChatStore,
  Message,
  MESSAGE_SOURCE_TYPE,
  MessageGroup,
  MessageGroupId,
  SetChatStore,
} from './types';
import ChatApi, { ChatApiInterface } from './api';
import { getMessageGroupsList, updateLastMessageReadState } from './helpers';
import WebsocketStore from '../useWebSocketStore/useWebsocketZustandStore';
import UserStore from '../useUserStore/useZustandUserStore';

interface ChatHandlersInterface {
  fetchMessages: (_groupId: MessageGroupId) => Promise<void>;
  fetchMessageGroups: () => Promise<void>;
  addMessage: (
    _message: Message,
    _groupId: MessageGroupId,
    _source: MESSAGE_SOURCE_TYPE,
  ) => Promise<void>;
  updateLastMessageRead: (
    _groupId: MessageGroupId,
    _userId: string,
  ) => Promise<void>;
  searchGroups: (_searchQuery: string) => Promise<void>;
  refreshMessageGroups: () => Promise<void>;
  updateLastMessageReadStateWebSocket: (
    _groupId: MessageGroupId,
    _userId: string,
  ) => Promise<void>;
}

export default class ChatHandlers implements ChatHandlersInterface {
  private api: ChatApiInterface;

  private setState: SetChatStore;

  private getState: GetChatStore;

  constructor(setState: SetChatStore, getState: GetChatStore) {
    this.api = new ChatApi();
    this.setState = setState;
    this.getState = getState;
  }

  refreshMessageGroups: () => Promise<void> = async () => {
    this.setState((state: ChatState) => {
      state.isMessageGroupsLoading = true;
      state.messageGroupsError = null;
      state.messageGroupsShown = [];
      state.messageGroups = {};
      state.messageGroupNextPage = 1;
    });
    const nextPage = this.getState().messageGroupNextPage;
    const [resp, error]: [any, Error | null] = await handleAsync(
      this.api.fetchMessageGroups,
      nextPage,
    );
    if (error || resp?.error) {
      this.setState((state: ChatState) => {
        state.messageGroupsError = error ?? resp?.error;
        state.isMessageGroupsLoading = false;
      });
      return;
    }
    const groups = _get(resp, ['data', 'getAllMessageGroups', 'groups'], []);
    const isRead = _get(
      resp,
      ['data', 'getAllMessageGroups', 'isRead'],
      true,
    ) as boolean;

    this.setState((state: ChatState) => {
      const newGroups = { ...state.messageGroups };
      groups.forEach((group: MessageGroup) => {
        state.messageGroups[group._id] = group;
        newGroups[group._id] = group;
      });
      state.messageGroupsShown = getMessageGroupsList(newGroups);
      state.isRead = isRead;
      state.isMessageGroupsLoading = false;
      state.messageGroupsError = null;
    });
  };

  /** Fetch Messages -> also added logic with lastId */
  fetchMessages = async (groupId: MessageGroupId): Promise<void> => {
    const { isMessagesLoading, hasMoreMessages } = this.getState();
    if (
      hasMoreMessages[groupId] === false ||
      isMessagesLoading[groupId]
    ) {
      return;
    }
    this.setState((state: ChatState) => {
      state.isMessagesLoading[groupId] = true;
      state.messagesError[groupId] = null;
    });

    const lastMessageId = _get(this.getState(), 'lastMessageId', {});
    const [resp, error]: [any, Error | null] = await handleAsync(
      this.api.fetchMessages,
      groupId,
      lastMessageId[groupId],
    );
    if (error || resp?.error) {
      this.setState((state: ChatState) => {
        state.messagesError[groupId] = error ?? resp?.error;
        state.isMessagesLoading[groupId] = false;
      });
      return;
    }
    const getMessagesByGroupId = _get(resp, ['data', 'getMessagesByGroupId']);
    const messages = _get(getMessagesByGroupId, ['messages'], []);
    const newLastMessageId = _get(
      getMessagesByGroupId,
      ['lastMessageId'],
      null,
    );
    const newHasMoreMessages = _get(getMessagesByGroupId, ['hasMore'], false);

    this.setState((state: ChatState) => {
      const messagesArray = state.messages[groupId];
      state.isMessagesLoading[groupId] = false;
      state.lastMessageId[groupId] = newLastMessageId;
      state.hasMoreMessages[groupId] = newHasMoreMessages;

      if (!_isArray(messages)) {
        return;
      }
      if (_isArray(messagesArray)) {
        messagesArray.push(...messages);
      } else {
        state.messages[groupId] = messages;
      }
    });
  };

  /** Fetch Message Groups */
  fetchMessageGroups = async (): Promise<void> => {
    this.setState((state: ChatState) => {
      state.isMessageGroupsLoading = true;
      state.messageGroupsError = null;
    });
    const nextPage = this.getState().messageGroupNextPage;
    const [resp, error]: [any, Error | null] = await handleAsync(
      this.api.fetchMessageGroups,
      nextPage,
    );
    if (error || resp?.error) {
      this.setState((state: ChatState) => {
        state.messageGroupsError = error ?? resp?.error;
        state.isMessageGroupsLoading = false;
      });
      return;
    }
    const groups = _get(resp, ['data', 'getAllMessageGroups', 'groups'], []);
    const isRead = _get(
      resp,
      ['data', 'getAllMessageGroups', 'isRead'],
      true,
    ) as boolean;

    this.setState((state: ChatState) => {
      const newGroups = { ...state.messageGroups };
      groups.forEach((group: MessageGroup) => {
        state.messageGroups[group._id] = group;
        newGroups[group._id] = group;
      });
      state.messageGroupsShown = getMessageGroupsList(newGroups);
      state.isRead = isRead;
      state.isMessageGroupsLoading = false;
      state.messageGroupsError = null;
    });
  };

  updateLastMessageRead = async (
    groupId: MessageGroupId,
    userId: string,
  ): Promise<void> => {
    this.setState((state: ChatState) => {
      state.messagesError[groupId] = null;
    });
    const group = this.getState().messageGroups[groupId];
    const lastMessageRead = group?.lastMessageRead;
    const lastMessageReadInfo = lastMessageRead?.find(
      (user: any) => user.userId === userId,
    );
    const latestMessageChecked =
      this.getState().messages?.[groupId]?.[0]?._id;
    if (
      !lastMessageReadInfo ||
      !latestMessageChecked ||
      lastMessageReadInfo?.lastMessageRead === latestMessageChecked
    ) {
      return;
    }
    const [resp, error]: [any, Error | null] = await handleAsync(
      this.api.updateLastMessageRead,
      groupId,
      latestMessageChecked,
    );

    if (error || resp?.error) {
      this.setState((state: ChatState) => {
        state.messagesError[groupId] = error ?? resp?.error;
      });
      return;
    }
    this.setState((state: ChatState) => {
      updateLastMessageReadState(state, groupId, userId, latestMessageChecked);
    });
  };

  searchGroups = async (searchQuery: string): Promise<void> => {
    if (_size(searchQuery) === 0) {
      this.setState((state: ChatState) => {
        const messageGroups = Object.values(this.getState().messageGroups);
        if (_isArray(messageGroups)) {
          state.messageGroupsShown = messageGroups.sort(
            (a: any, b: any) =>
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
          );
        }
        state.isMessageGroupsLoading = false;
      });
      return;
    }
    this.setState((state: ChatState) => {
      state.isMessageGroupsLoading = true;
      state.messageGroupsError = null;
    });
    const messageGroups = Object.values(this.getState().messageGroups);
    const searchedGroups =
      _filter(
        messageGroups,
        (group: Group) =>
          group.groupName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          groupReader
            .individual(group)
            ?.name?.toLowerCase()
            .includes(searchQuery.toLowerCase()),
      ) ?? [];
    this.setState((state: ChatState) => {
      state.messageGroupsShown = searchedGroups;
      state.isMessageGroupsLoading = false;
      state.messageGroupsError = null;
    });
  };

  addMessage = async (
    message: Message,
    groupId: MessageGroupId,
    source: MESSAGE_SOURCE_TYPE,
  ): Promise<void> => {
    this.setState((state: ChatState) => {
      const messagesArray = state.messages[groupId];
      if (_isArray(messagesArray)) {
        messagesArray.unshift(message);
      } else {
        state.messages[groupId] = [message];
      }

      if (source === MESSAGE_SOURCE_TYPE.OTHER) {
        state.isRead = false;
      }

      const group = state.messageGroups[groupId];
      if (group) {
        group.lastMessage = message;
        group.updatedAt = message.createdAt;
        state.messageGroups[groupId] = group;
        const newGroups = { ...state.messageGroups };
        newGroups[groupId] = group;
        const messageGroupsShown = getMessageGroupsList(newGroups);
        state.messageGroupsShown = messageGroupsShown;
        if (source === MESSAGE_SOURCE_TYPE.SELF) {
          updateLastMessageReadState(
            state,
            groupId,
            message.sender,
            message?._id,
          );
        }
      } else {
        this.refreshMessageGroups();
      }
    });
  };

  updateLastMessageReadStateWebSocket = async (
    groupId: MessageGroupId,
    latestMessageChecked: string,
  ): Promise<void> => {
    const { userId } = UserStore.getState();
    if (!userId) {
      return;
    }
    const channel = WEBSOCKET_CHANNELS.UserEvents(userId);
    WebsocketStore.getState().sendMessage({
      type: 'updateLastMessageRead',
      channel,
      data: {
        groupId,
        lastMessageRead: latestMessageChecked,
      },
    });

    this.setState((state) => {
      state.isRead = true;
      updateLastMessageReadState(state, groupId, userId, latestMessageChecked);
    });
  };
}
