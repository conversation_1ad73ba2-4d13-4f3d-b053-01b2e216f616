import { gql } from '@apollo/client';
import SHOWDOWN_DETAIL_FRAGMENT from 'core/graphql/fragments/showdown';

// Query to get showdown by ID
export const GET_SHOWDOWN = gql`
  ${SHOWDOWN_DETAIL_FRAGMENT}
  query GetShowdownByID($showdownId: ID!) {
    getShowdownById(showdownId: $showdownId) {
      ...ShowdownDetailFields
    }
  }
`;
// markAttendance(showdownId: ID!): Boolean! @auth
export const MARK_ATTENDANCE = gql`
  mutation MarkAttendance($showdownId: ID!) {
    markAttendance(showdownId: $showdownId)
  }
`;

// Query to get featured showdown
export const GET_FEATURED_SHOWDOWN = gql`
  query GetFeaturedShowdown {
    getFeaturedShowdown {
      _id
      name
      description
      startTime
      endTime
    }
  }
`;

// Query to get fixtures by showdown ID
export const GET_FIXTURES = gql`
  query GetFicturesByShowdownId($showdownId: ID!) {
    getFicturesByShowdownId(showdownId: $showdownId) {
      currentUserFicture {
        showdownId
        users {
          showdownParticipant {
            _id
            userID
            rounds {
              opponent
              round
              score
              games
            }
            stats {
              currentScore
            }
            rank
            userInfo {
              name
              username
              profileImageUrl
              rating
            }
          }
          currentRound {
            opponent
            round
            score
            wins
          }
        }
        round
      }
    }
  }
`;

// Query to get leaderboard details
export const GET_LEADERBOARD_DETAILS = gql`
  query GetPaginatedLeaderboard($input: PaginatedLeaderboardInput) {
    getPaginatedLeaderboard(input: $input) {
      participants {
        participant {
          _id
          rounds {
            opponent
            round
            score
            games
            totalGamesPlayed
          }
          userInfo {
            name
            username
            profileImageUrl
            rating
          }
        }
        score
        rank
      }
      count
    }
  }
`;

// Mutation to register for showdown
export const REGISTER_FOR_SHOWDOWN = gql`
  mutation RegisterForShowdown($input: ShowdownRegistrationFormValuesInput!) {
    registerForShowdown(input: $input)
  }
`;

// Mutation to unregister from showdown
export const UNREGISTER_FROM_SHOWDOWN = gql`
  mutation UnregisterFromShowdown($showdownId: ID!) {
    unregisterFromShowdown(showdownId: $showdownId)
  }
`;
