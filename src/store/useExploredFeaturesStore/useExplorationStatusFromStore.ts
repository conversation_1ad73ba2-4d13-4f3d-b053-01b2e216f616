import { useCallback, useEffect, useRef } from 'react';
import { ExploredFeaturesData } from 'store/useExploredFeaturesStore/types';
import useExploredFeaturesStore from './index';

interface ExplorationStatus {
  exploredFeaturesData: ExploredFeaturesData | null;
  needToShowExploreFeatures: boolean;
  getModuleExplorationStatus: (moduleId: string) => boolean;
  getFeatureExplorationStatus: (featureId: string) => boolean;
  getModuleProgress: (moduleId: string) => {
    explored: number;
    total: number;
    percentage: number;
  };
  getAllModulesProgress: () => {
    explored: number;
    total: number;
    percentage: number;
  };
  markFeatureAsExplored: (featureType: string) => Promise<boolean>;
  loading: boolean;
  error: any;
  refetch: () => void;
}

/**
 * Hook that provides exploration status functionality using Zustand store
 * This is a drop-in replacement for the existing useExplorationStatus hook
 */
const useExplorationStatusFromStore = (): ExplorationStatus => {
  const {
    exploredFeaturesData,
    needToShowExploreFeatures,
    loading,
    error,
    fetchExploredFeatures,
    markFeatureAsExplored,
    isFeatureExplored,
    getModuleProgress,
    getModuleExplorationStatus,
    getAllModulesProgress,
  } = useExploredFeaturesStore((state) => ({
    needToShowExploreFeatures: state.needToShowExploreFeatures,
    exploredFeaturesData: state.exploredFeaturesData,
    loading: state.loading,
    error: state.error,
    fetchExploredFeatures: state.fetchExploredFeatures,
    markFeatureAsExplored: state.markFeatureAsExplored,
    isFeatureExplored: state.isFeatureExplored,
    getModuleProgress: state.getModuleProgress,
    getModuleExplorationStatus: state.getModuleExplorationStatus,
    getAllModulesProgress: state.getAllModulesProgress,
  }));

  // Use ref to track if we've already attempted to fetch data
  const hasFetchedRef = useRef(false);

  // Stable fetch function to prevent infinite loops
  const stableFetchExploredFeatures = useCallback(() => {
    if (!hasFetchedRef.current && !exploredFeaturesData && !loading && !error) {
      hasFetchedRef.current = true;
      fetchExploredFeatures();
    }
  }, [exploredFeaturesData, loading, error, fetchExploredFeatures]);

  // Fetch data on mount if not already loaded
  useEffect(() => {
    stableFetchExploredFeatures();
  }, [stableFetchExploredFeatures]);

  const getFeatureExplorationStatus = useCallback(
    (featureId: string): boolean => isFeatureExplored(featureId),
    [isFeatureExplored],
  );

  const refetch = useCallback(() => {
    // Reset the fetch flag and force a refetch
    hasFetchedRef.current = false;
    fetchExploredFeatures(true);
  }, [fetchExploredFeatures]);

  return {
    exploredFeaturesData,
    getModuleExplorationStatus,
    getFeatureExplorationStatus,
    getModuleProgress,
    getAllModulesProgress,
    markFeatureAsExplored,
    needToShowExploreFeatures,
    loading,
    error,
    refetch,
  };
};

export default useExplorationStatusFromStore;
