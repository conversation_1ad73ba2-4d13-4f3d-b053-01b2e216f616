/* eslint-disable import/prefer-default-export */
// storage.ts
import { enableMapSet } from 'immer';
import useZustandUserStore from './useUserStore/useZustandUserStore';
import useZustandFeedStore from './useFeedStore/useZustandFeedStore';
import useZustandShowdownStore from './useShowdownStore/useZustandShowdownStore';
import useGameZustandStore from './useGameStore/useZustandGameStore';
import useZustandTriggerPointsStore from './useTriggerPointsStore/zustandTriggerPointsStore';
import useWebsocketZustandStore from './useWebSocketStore/useWebsocketZustandStore';
import useZustandExploredFeaturesStore from './useExploredFeaturesStore/useZustandExploredFeaturesStore';
import useGroupPlayZustandStore from './useGroupPlayStore/useZustandGroupPlayStore';
import ChatStore from './useChatStore/ChatStore';

enableMapSet();

export const clearCentralStore = () => {
  useZustandUserStore.getState().clearStore();
  useZustandFeedStore.getState().clearStore();
  useZustandShowdownStore.getState().clearStore();
  useGameZustandStore.getState().clearStore();
  useZustandTriggerPointsStore.getState().clearStore();
  useWebsocketZustandStore.getState().clearStore();
  useZustandExploredFeaturesStore.getState().clearStore();
  ChatStore.getState().clearStore();
};
