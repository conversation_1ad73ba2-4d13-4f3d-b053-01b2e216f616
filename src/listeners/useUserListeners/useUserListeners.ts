/* eslint-disable no-unused-vars */
import EventManager from '@/src/core/event';
import { events, listenersNamespace } from '@/src/core/event/constants';
import useUserStore from '@/src/store/useUserStore';
import { useCallback, useEffect, useRef } from 'react';
import useUserStatikCoinsEarnedEvent from '@/src/overlays/hooks/useUserStatikCoinsEarnedEvent';
import useUserRatingFixtureEvents from '@/src/overlays/hooks/useRatingFixtureEvents';
import { useAcceptFriendRequest } from '@/src/modules/friendsAndFollowers/hooks/mutations/useAcceptFriendRequest';
import { useRejectFriendRequest } from '@/src/modules/friendsAndFollowers/hooks/mutations/useRejectFriendRequest';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import { handleAsync } from '@/src/core/utils/asyncUtils';
import { useApolloClient } from '@apollo/client';
import useWeeklyLeagueModalCheck from '@/src/overlays/hooks/useWeeklyLeagueModalCheck';
// import { InAppNotificationHandler } from './handlers';

const useUserListeners = () => {
  const apolloClient = useApolloClient();

  const { showInAppToast } = useUserStore((state) => ({
    showInAppToast: state.showInAppToast,
  }));

  const { handleUserStatikCoinsEarnedEvent } = useUserStatikCoinsEarnedEvent();
  const handleUserStatikCoinsEarnedEventRef = useRef(
    handleUserStatikCoinsEarnedEvent,
  );
  handleUserStatikCoinsEarnedEventRef.current =
    handleUserStatikCoinsEarnedEvent;

  const { handleUserRatingFixtureEvents } = useUserRatingFixtureEvents();
  const handleUserRatingFixtureEventsRef = useRef(
    handleUserRatingFixtureEvents,
  );
  handleUserRatingFixtureEventsRef.current = handleUserRatingFixtureEvents;

  const { checkHasShownWeeklyLeagueModal } = useWeeklyLeagueModalCheck();
  const checkHasShownWeeklyLeagueModalRef = useRef(
    checkHasShownWeeklyLeagueModal,
  );
  checkHasShownWeeklyLeagueModalRef.current = checkHasShownWeeklyLeagueModal;

  const { acceptFriendRequest } = useAcceptFriendRequest();
  const { rejectFriendRequest } = useRejectFriendRequest();
  const handleAcceptFriendRequest = useCallback(
    async (title: any, senderId: any) => {
      Analytics.track(
        ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_ACCEPT_FRIEND_REQUEST,
      );
      const [_, err] = await handleAsync(() =>
        acceptFriendRequest({ senderId }),
      );
      if (err) {
        showToast({
          type: TOAST_TYPE.ERROR,
          description: `Failed to accept friend request of ${title}`,
        });
        return;
      }
      await apolloClient.refetchQueries({
        include: ['GetFriends'],
      });
      showToast({
        type: TOAST_TYPE.SUCCESS,
        description: `You and ${title} are now friends`,
      });
    },
    [acceptFriendRequest, apolloClient],
  );

  const handleRejectFriendRequest = useCallback(
    async (title: any, senderId: any) => {
      const [_, err] = await handleAsync(() =>
        rejectFriendRequest({ senderId }),
      );
      if (err) {
        showToast({
          type: TOAST_TYPE.ERROR,
          description: `Failed to reject friend request of ${title}`,
        });
        return;
      }
      showInAppToast({
        visible: false,
      });
      await apolloClient.refetchQueries({
        include: ['GetFriends'],
      });
      showToast({
        type: TOAST_TYPE.SUCCESS,
        description: `Successfully rejected friend request of ${title}`,
      });
    },
    [rejectFriendRequest, apolloClient, showInAppToast],
  );
  // TODO @Rishav will be used later in InApp PR
  // const actionFuncs = useCallback(
  //   (payload: any) => {
  //     if (payload?.type === InAppType.Connection) {
  //       const helperFuncs = {
  //         handleAcceptFriendRequest,
  //         handleRejectFriendRequest,
  //       };
  //       const onAccept = () => {
  //         helperFuncs.handleAcceptFriendRequest(
  //           payload?.title,
  //           payload?.inAppAdditionalInfo?.connectionRequest?.sentBy,
  //         );
  //       };
  //       const onReject = () => {
  //         helperFuncs.handleRejectFriendRequest(
  //           payload?.title,
  //           payload?.inAppAdditionalInfo?.connectionRequest?.sentBy,
  //         );
  //       };
  //       const onClose = () => {
  //         // showInAppToast({
  //         //   visible: false,
  //         // });
  //       };
  //       return {
  //         onAccept,
  //         onReject,
  //         onClose,
  //       };
  //     }
  //     // if (payload?.type === InAppType.Challenge) {
  //     // }
  //     return null;
  //   },
  //   [handleAcceptFriendRequest, handleRejectFriendRequest],
  // );

  useEffect(() => {
    const eventManager = new EventManager();

    const inAppNotificationEventFuncInfo = eventManager.on(
      events.InAppNotification,
      listenersNamespace.InAppNotification,
      (_payload: any) => {
        // const funcs = actionFuncs(_payload);
        // TODO: @Rishav merge in InApp PR
        // InAppNotificationHandler(showInAppToast, _payload, funcs);
      },
    );

    const statikCoinsEarnedEventFuncInfo = eventManager.on(
      events.StatikCoinsEarnedEvent,
      listenersNamespace.StatikCoinsEarnedEvent,
      (_payload: any) =>
        handleUserStatikCoinsEarnedEventRef?.current?.({ payload: _payload }),
    );

    const ratingFixtureEventFuncInfo = eventManager.on(
      events.RatingFixtureEvent,
      listenersNamespace.RatingFixtureEvent,
      (_payload: any) =>
        handleUserRatingFixtureEventsRef?.current?.({ payload: _payload }),
    );

    const joinedWeeklyLeagueEventFuncInfo = eventManager.on(
      events.JoinedWeeklyLeagueEvent,
      listenersNamespace.JoinedWeeklyLeagueEvent,
      (_payload: any) =>
        checkHasShownWeeklyLeagueModalRef?.current?.(_payload?.leagueInfo),
    );

    return () => {
      eventManager.offMany([
        inAppNotificationEventFuncInfo!,
        statikCoinsEarnedEventFuncInfo!,
        ratingFixtureEventFuncInfo!,
        joinedWeeklyLeagueEventFuncInfo!,
      ]);
    };
  }, []);
};

export default useUserListeners;
