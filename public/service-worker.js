importScripts('https://widgets.in.webengage.com/js/service-worker.js');
const CACHE_NAME = 'matiks-cache-v1';
const oldWasmCacheNames = ['matiks-wasm-cache-v1.1', 'matiks-wasm-cache-v1'];
const WASM_CACHE_NAME = 'matiks-wasm-cache-v1.2';
const ALLOWED_ORIGINS = ["http://localhost:8081", "http://dev.server.matiks.com", "http://dev.matiks.com", "https://matiks.com", "https://cdn.matiks.com"];

// Files to cache on install
const PRECACHE_ASSETS = [
  '/',
  '/matiks.wasm' // Pre-cache the WASM file
];

self.addEventListener("install", (event) => {
  event.waitUntil(
    Promise.all([
      // Cache regular assets
      caches.open(CACHE_NAME).then((cache) => {
        return cache.add("/");
      }),
      // Cache WASM files specifically
      caches.open(WASM_CACHE_NAME).then((cache) => {
        return cache.addAll(PRECACHE_ASSETS.filter(url => url.endsWith('.wasm')));
      })
    ])
  );
});

self.addEventListener("fetch", (event) => {
  const requestURL = new URL(event.request.url);
  
  // Check if this is a WASM file request
  if (requestURL.pathname.endsWith('matiks.wasm')) {
    event.respondWith(
      caches.open(WASM_CACHE_NAME)
        .then(cache => cache.match(event.request))
        .then(response => {
          // Return cached response if available
          oldWasmCacheNames.forEach(cacheName => {
            caches.delete(cacheName).catch(() => {});
          });
          if (response) {
            return response;
          }
          
          // Otherwise fetch from network and cache
          return fetch(event.request).then(networkResponse => {
            // Clone the response before using it
            const responseToCache = networkResponse.clone();
            
            caches.open(WASM_CACHE_NAME).then(cache => {
              cache.put(event.request, responseToCache);
            });
            
            return networkResponse;
          });
        })
    );
    return;
  }
  
  // Handle other requests
  if (!ALLOWED_ORIGINS.includes(requestURL.origin)) {
    return;
  }
  
  event.respondWith(
    fetch(event.request)
      .then((response) => {
        return caches.open(CACHE_NAME).then((cache) => {
          cache.put(event.request, response.clone());
          return response;
        });
      })
      .catch(() => caches.match(event.request))
  );
});

// self.addEventListener("activate", (event) => {
//   event.waitUntil(
//     caches.keys().then((cacheNames) => {
//       return Promise.all(
//         cacheNames.map((cache) => {
//           if (cache !== CACHE_NAME) {
//             return caches.delete(cache);
//           }
//         })
//       );
//     })
//   );
// });
