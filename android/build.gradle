// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = '35.0.0'
        minSdkVersion = Integer.parseInt(findProperty('android.minSdkVersion') ?: '24')
        compileSdkVersion = Integer.parseInt(findProperty('android.compileSdkVersion') ?: '35')
        targetSdkVersion = Integer.parseInt(findProperty('android.targetSdkVersion') ?: '35')
        kotlinVersion = findProperty('android.kotlinVersion') ?: '2.0.21'

        ndkVersion = "26.1.10909125"
     }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath('com.android.tools.build:gradle:8.10.1')
        classpath('com.facebook.react:react-native-gradle-plugin:0.79.3')
        classpath('org.jetbrains.kotlin:kotlin-gradle-plugin:2.0.21')
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.2'
        classpath("io.sentry:sentry-android-gradle-plugin:5.7.0")
        // classpath('expo-modules-core')
    }
}

// apply plugin: "com.facebook.react.rootproject"
def reactNativeAndroidDir = new File(
  providers.exec {
    workingDir(rootDir)
    commandLine("node", "--print", "require.resolve('react-native/package.json')")
  }.standardOutput.asText.get().trim(),
  "../android"
)

allprojects {
    configurations.all {
    resolutionStrategy {

      force 'org.jetbrains.kotlin:kotlin-stdlib:2.0.21',
              'org.jetbrains.kotlin:kotlin-reflect:2.0.21',
              'expo.modules:expo-modules-core:2.4.0'
    }
  }
    repositories {
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url(reactNativeAndroidDir)
        }

        google()
        mavenCentral()
        maven { url 'https://www.jitpack.io' }
    }
}

apply plugin: "expo-root-project"
apply plugin: "com.facebook.react.rootproject"
