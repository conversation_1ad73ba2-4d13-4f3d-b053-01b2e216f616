package com.matiks.app

import com.google.firebase.messaging.FirebaseMessagingService;
import com.webengage.sdk.android.WebEngage;
import com.google.firebase.messaging.RemoteMessage

class MyFirebaseMessagingService : FirebaseMessagingService() {
    override fun onNewToken(s: String) {
        super.onNewToken(s)
        WebEngage.get().setRegistrationID(s)
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
            val data: Map<String, String> = remoteMessage.data
            if (data.isNotEmpty()) {
                if (data["source"] == "webengage") {
                    WebEngage.get().receive(data)
                }
            }
        }
}